import request from '@/utils/request'


// 查询供应商列表
export function getSupplierList(data) {
  return request({
    url: '/mall/supplier/page',
    params: data,
    method: 'get'
  })
}

// 查询供应商
export function getSupplierInfo(id) {
  return request({
    url: '/mall/supplier/get?id=' + id,
    method: 'get'
  })
}


// 更新供应商
export function updateSupplier(data) {
  return request({
    url: '/mall/supplier/update',
    method: 'put',
    data: data
  })
}

// 新增供应商
export function createSupplier(data) {
  return request({
    url: '/mall/supplier/create',
    method: 'post',
    data: data
  })
}

// 删除供应商
export function deleteSupplier(id) {
  return request({
    url: '/mall/supplier/delete?id=' + id,
    method: 'delete'
  })
}

// 更新供应商运营状态
export function updateSupplierStatus(data) {
  return request({
    url: '/mall/supplier/update-status',
    method: 'put',
    data: data
  })
}

// 更新供应商接口状态
export function updateSupplierOpenAppStatus(data) {
  return request({
    url: '/mall/supplier/update-open-app-status',
    method: 'put',
    data: data
  })
}