<template>
  <div class="app-container order-detail-page" v-loading="loading">
    <el-tabs v-model="activeName" class="tabs">
      <el-tab-pane label="基础信息" name="tab1"></el-tab-pane>
      <el-tab-pane label="父子订单信息" name="tab11" v-if="notSupplierUser() && parentOrderDetail.id">
        <!-- 父订单信息 -->
        <el-descriptions title="父订单" :colon="false" :column="2" border>
          <el-descriptions-item label="订单号">{{ parentOrderDetail.no }} </el-descriptions-item>
          <!-- <el-descriptions-item label="订单ID">{{ parentOrderDetail.id }}</el-descriptions-item> -->
          <el-descriptions-item label="订单金额">{{ formatMoney(parentOrderDetail.orderPrice) }}</el-descriptions-item>
          <el-descriptions-item label="商品金额">{{ formatMoney(parentOrderDetail.productPrice) }}</el-descriptions-item>
          <el-descriptions-item label="运费金额">{{ formatMoney(parentOrderDetail.deliveryPrice) }}</el-descriptions-item>
          <el-descriptions-item label="下单时间">{{ parseTime(parentOrderDetail.submitTime) }}</el-descriptions-item>
          <el-descriptions-item label="拆单时间">{{ parseTime(parentOrderDetail.splitTime) }}</el-descriptions-item>
          <el-descriptions-item label="供应商">{{ parentOrderDetail.supplierName }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions title="子订单列表" :colon="false" direction="vertical">
          <el-descriptions-item label="">
            <el-table :data="parentOrderDetail.childOrders" :row-class-name="parentRowClassName" border>
              <el-table-column prop="no" label="订单号">
                <template v-slot="{ row }">
                  <el-button type="text" @click="showSubOrder(row)">{{ row.no }}</el-button>
                </template>
              </el-table-column>
              <el-table-column prop="orderPrice" label="订单金额">
                <template slot-scope="{ row }">
                  <span>{{ formatMoney(row.orderPrice) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="refundPrice" label="退款金额">
                <template slot-scope="{ row }">
                  <span>{{ formatMoney(row.refundPrice) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="productPrice" label="商品金额">
                <template slot-scope="{ row }">
                  <span>{{ formatMoney(row.productPrice) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="deliveryPrice" label="运费金额">
                <template slot-scope="{ row }">
                  <span>{{ formatMoney(row.deliveryPrice) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="200">
                <template slot-scope="{ row }">
                  <el-tag :type="row.status | orderStatusStyle">
                    {{ row.status | orderStatusInfo }} {{ row.userDeleted ? ": 用户删除" : "" }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-descriptions-item>
        </el-descriptions>
      </el-tab-pane>
      <el-tab-pane v-if="notSupplierUser()" v-hasPermi="['trade:order-operate-log:query']" label="操作日志" name="tab2">
        <div v-if="operateLogList && operateLogList.length">
          <el-timeline>
            <el-timeline-item :timestamp="parseTime(logItem.createTime)" placement="top"
              v-for="(logItem, index) in operateLogList" :key="index">
              <el-card>
                <p>{{ logItem.userName || logItem.userId }} {{ logItem.content }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
        <el-empty v-else></el-empty>
      </el-tab-pane>
    </el-tabs>
    <template v-if="order && activeName === 'tab1'">
      <!-- 订单信息 -->
      <el-descriptions title="订单信息" :column="4" border>
        <el-descriptions-item label="订单号" :contentStyle="{ width: '330px' }" :labelStyle="{ width: '100px' }">
          {{ order.no }} <OrderTagCom v-if="showOrderTag" :orderItems="order.skuInfoList"></OrderTagCom>
        </el-descriptions-item>
        <!-- <el-descriptions-item label="订单ID" :labelStyle="{ width: '100px' }">{{ order.id }}</el-descriptions-item> -->
        <!-- <el-descriptions-item label="父订单ID" :labelStyle="{ width: '100px' }">{{ order.parentOrderId || '--' }}</el-descriptions-item> -->
        <el-descriptions-item label="三方订单号" :labelStyle="{ width: '100px' }">{{ order.thirdOrderId || '--'
          }}</el-descriptions-item>
        <el-descriptions-item label="供应商">
          <el-tag>{{ order.supplierName || '--' }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item v-if="notSupplierUser()" label="下单人">
          <div @click="toMemberDetail(order.userId)" style="cursor: pointer;color: #409eff">{{ order.userName || '--' }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item v-else label="下单人">
          <div>{{ order.userName || '--' }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="下单时间">{{ parseTime(order.orderSubmitTime) }}</el-descriptions-item>
        <el-descriptions-item label="发货时间" v-if="order.orderDeliveryTime">{{ parseTime(order.orderDeliveryTime)
          }}</el-descriptions-item>
        <el-descriptions-item label="签收时间" v-if="order.orderReceiveTime">{{ parseTime(order.orderReceiveTime)
          }}</el-descriptions-item>
        <el-descriptions-item label="完成时间" v-if="order.orderFinishTime">{{ parseTime(order.orderFinishTime)
          }}</el-descriptions-item>
        <el-descriptions-item label="取消时间" v-if="order.orderCancelTime">{{ parseTime(order.orderCancelTime)
          }}</el-descriptions-item>
        <el-descriptions-item label="下单终端">
          <dict-tag :type="DICT_TYPE.MALL_ORDER_PLATFORM" :value="order.platform" />
        </el-descriptions-item>
        <el-descriptions-item label="支付方式">
          <dict-tag :type="DICT_TYPE.MALL_PAYMENT_METHOD" :value="order.paymentMethod || 1" />
        </el-descriptions-item>
        <template v-if="!isBusinessPay(order.paymentMethod)">
          <el-descriptions-item label="支付状态">
            <el-tag type="success" v-if="order.payed">已支付</el-tag>
            <el-tag type="info" v-if="!order.payed">未支付</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付渠道" v-if="order.payChannelCode">
            <dict-tag :type="DICT_TYPE.PAY_CHANNEL_CODE" :value="order.payChannelCode" />
          </el-descriptions-item>
          <el-descriptions-item label="支付时间" v-if="order.orderPayTime">{{ parseTime(order.orderPayTime)
            }}</el-descriptions-item>
          <el-descriptions-item label="支付金额" v-if="order.payPrice">{{ formatMoney(order.payPrice)
            }}</el-descriptions-item>
        </template>
        <el-descriptions-item label="配送方式">{{ (order.deliveryInfo &&
          order.deliveryInfo.logisticInfoList[0].deliveryCarrier) || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="收货人">{{ order.address ? order.address.name : '--' }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ order.address && order.address.mobile ? order.address.mobile : '--'
          }}</el-descriptions-item>
        <el-descriptions-item label="收货地址">
          {{ order.address ? order.address.consigneeAddress : '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="固资建档" v-if="order.status === 8">
          <el-tag :type="order.assetStatus | orderAssetStatusStyle">{{ order.assetStatus | orderAssetStatusInfo
            }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="结算方式">{{ order.offlineSettlement ? '线下结算' : '线上结算' }}</el-descriptions-item>
        <el-descriptions-item label="订单备注">{{ order.userRemark ? order.userRemark : '--' }}</el-descriptions-item>
        <template v-if="order.status === 9">
          <el-descriptions-item label="订单取消类型">
            <dict-tag :type="DICT_TYPE.TRADE_ORDER_CANCEL_TYPE" :value="order.orderCancelType" />
          </el-descriptions-item>
          <el-descriptions-item label="订单取消原因">{{ order.orderCancelReason }}</el-descriptions-item>
          <el-descriptions-item label="订单取消时间">{{ parseTime(order.orderCancelTime) }}</el-descriptions-item>
        </template>
      </el-descriptions>

      <el-descriptions title="订单金额" direction="vertical" :column="6" border>
        <el-descriptions-item label="商品总额">{{ formatMoney(order.productPrice) }}</el-descriptions-item>
        <el-descriptions-item label="运费金额">{{ formatMoney(order.deliveryPrice) }}</el-descriptions-item>
        <el-descriptions-item label="订单总金额">{{ formatMoney(order.orderTotalPrice) }}</el-descriptions-item>
        <el-descriptions-item label="退款总金额">{{ formatMoney(order.orderRefundPrice) }}</el-descriptions-item>
      </el-descriptions>

      <!-- 订单状态 -->
      <el-descriptions title="订单状态"></el-descriptions>
      <div style="font-size: 14px;align-items: center;display: flex;">
        <span>订单状态：</span>
        <span><OrderStatusCom :order="order"></OrderStatusCom></span>
        <el-button v-if="order.status == 1" v-hasPermi="['trade:order:confirm']" size="mini" style="margin-left: 20px;" type="primary"
          @click="confirmOrder">确认订单</el-button>
        <el-button v-if="[3, 4].includes(order.status)" v-hasPermi="['trade:order:receive']" size="mini" style="margin-left: 20px;" type="primary"
          @click="receiveOrder">订单签收</el-button>
        <el-button v-if="notSupplierUser() && [1, 2].includes(order.status)" v-hasPermi="['trade:order:cancel']" size="mini" style="margin-left: 20px;" type="primary"
          @click="cancelOrder">取消订单</el-button>
        <el-button v-if="[5].includes(order.status)" v-hasPermi="['trade:order:complete']" size="mini" style="margin-left: 20px;" type="primary"
          @click="completeOrder">完成订单</el-button>
        <el-button v-if="!order.offlineSettlement" v-hasPermi="['trade:order:offline-settlement']" size="mini" style="margin-left: 20px;" type="primary"
          @click="setOfflineSettlement">线下结算</el-button>
        <el-tag v-if="order.userDeleted" type="warning" style="margin-left: 10px;">用户已删除</el-tag>
      </div>

      <!-- 商品信息 -->
      <el-descriptions title="商品信息" :colon="false" direction="vertical">
        <el-descriptions-item label="">
          <el-table :data="order.skuInfoList" border>
            <el-table-column prop="skuName" label="商品名称">
              <template v-slot="{ row }">
                <div class="flex-start">
                  <img v-if="row.imgUrl" :src="row.imgUrl" alt="图片" style="height: 30px;" />
                  <span>{{ row.skuName }}</span>
                  <template v-if="row.isAsset">
                    <el-tag :type="row.assetStatus | orderAssetStatusStyle" style="margin: 0 5px;">固资
                      <span> > {{row.assetStatus | orderAssetStatusInfo}}</span>
                    </el-tag>
                    <el-link v-hasPermi="['trade:order-item-assets:query']" type="primary" @click="jumpAssetIndex(row)"
                      style="word-break: keep-all;">查看</el-link>
                  </template>
                  <OrderTagCom v-if="showOrderTag" :tags="row.skuTags"></OrderTagCom>
                  <OrderAcceptInfo :orderItem="row" :orderStatus="order.status"></OrderAcceptInfo>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="skuId" label="商品SKU" width="220">
              <template slot-scope="{ row }">
                <span>平台:{{ row.skuId }}</span>
                <span v-if="row.skuInnerId  && order.supplierType !== 20"><br>三方:{{ row.skuInnerId }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="skuPrice" label="单价(元)" width="130">
              <template slot-scope="{ row }">
                <span>{{ formatMoney(row.skuPrice) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="skuNum" label="购买数量" width="120">
              <template slot-scope="{ row }">
                <el-tag type="success">{{ row.skuNum }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="skuNum" label="退款数量" width="140">
              <template slot-scope="{ row }">
                <el-tag :type="getRefundNumStyle(row)">{{ row.skuNum - row.afterSaleAvailableCount }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="afterSaleStatus" label="状态" width="140" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <dict-tag :type="DICT_TYPE.TRADE_ORDER_ITEM_AFTER_SALE_STATUS" :value="row.afterSaleStatus"
                  v-if="row.afterSaleStatus" />
                <span v-else>{{ row.afterSaleMemo || '--' }}</span>
                <el-link v-if="row.afterSaleStatus" v-hasPermi="['trade:after-sale:query']" type="primary"
                  @click="jumpAfterSale(row)">查看</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="operation" label="操作" width="150">
              <template slot-scope="{ row }">
                <el-button v-if="showAfterSale(row)" v-hasPermi="['trade:after-sale:create']" size="mini"
                  style="margin-left: 20px;" type="primary" @click="afterSaleApply(row)">申请售后</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 发票信息 -->
      <el-descriptions title="发票信息" direction="vertical" :column="3" border
        v-if="order.tradeOrderInvoice && order.tradeOrderInvoice.invoiceStatusName">
        <el-descriptions-item label="开票状态">{{ order.tradeOrderInvoice.invoiceStatusName }}</el-descriptions-item>
        <el-descriptions-item label="发票号">
          <span @click="previewInvoice(order.tradeOrderInvoice)"
            :class="{ 'clickInvoice': order.tradeOrderInvoice.fileUrl }">
            {{ order.tradeOrderInvoice.invoiceId || '--' }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="开票时间">{{ order.tradeOrderInvoice.invoiceDate || '--' }}</el-descriptions-item>
      </el-descriptions>

      <!-- 添加备注 -->
      <template>
        <el-descriptions title="运营备注" :column="1" direction="vertical" border style="margin-top:20px;">
          <template slot="extra">
            <el-button type="primary" size="small" @click="addRemark" v-hasPermi="['trade:order:remark']">添加备注</el-button>
          </template>
          <el-descriptions-item label="备注">{{ order && (order.remark || '--') }}</el-descriptions-item>
        </el-descriptions>
      </template>

      <!-- 采购信息 -->
      <template v-if="havePurchaseInfo">
        <el-descriptions title="采购信息" :column="3" border style="margin-top: 20px;">
          <el-descriptions-item label="审批单号" v-if="order.purchaseInfo.bpmNo">{{ order.purchaseInfo.bpmNo
            }}</el-descriptions-item>
          <el-descriptions-item label="项目名称" v-if="order.purchaseInfo.projectName">{{ order.purchaseInfo.projectName
            }}</el-descriptions-item>
          <el-descriptions-item label="项目编号" v-if="order.purchaseInfo.projectNo">{{ order.purchaseInfo.projectNo
            }}</el-descriptions-item>
          <el-descriptions-item label="业财状态" v-if="order.ycrhNeed">
            <el-tag :type="order.ycrhStatus | orderYcrhStatusStyle">{{ order.ycrhStatus | orderYcrhStatusInfo
              }}</el-tag>
            <div style="display:inline-block;margin: 0 10px;" v-if="order.ycrhNeed" v-hasPermi="['trade:order:ycrh']">
              <el-button type="primary" size="small" @click="refreshYcrhStatus">同步状态</el-button>
              <!-- <el-button v-if="!order.offlineSettlement"  type="primary" size="small" @click="syncYrchOrder">推送订单</el-button> -->
              <el-button type="warning" size="small" @click="cancelYcrhOrder" v-if="order.status !== 9">取消冻结</el-button>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="采购时间">{{ parseTime(order.purchaseInfo.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="采购理由">{{ order.purchaseInfo.purchaseReason }}</el-descriptions-item>
          <el-descriptions-item label="补充材料" v-if="purchaseAttachments.length">
            <el-link v-for="(furl,findex) in purchaseAttachments" :key="findex" :href="furl" type="primary"
              target="_blank">附件{{ findex + 1 }}</el-link>
          </el-descriptions-item>
          <template v-if="order.purchaseInfo.accepterName">
            <el-descriptions-item label="验收人姓名">{{ order.purchaseInfo.accepterName }}</el-descriptions-item>
            <el-descriptions-item label="验收人电话">{{ order.purchaseInfo.accepterMobile }}</el-descriptions-item>
          </template>
          <!-- <el-descriptions-item label="采购状态"><el-tag :type="order.purchaseInfo.auditStatus | orderAuditStatusStyle">{{ order.purchaseInfo.auditStatus | orderAuditStatusInfo }}</el-tag></el-descriptions-item>
        <el-descriptions-item label="采购结果">
          {{ order.purchaseInfo.auditResult }}
        </el-descriptions-item> -->
        </el-descriptions>
        <el-table style="margin-top: 12px;" :data="auditArr" border v-if="order.purchaseInfo.bpmNo">
          <el-table-column prop="approvalRoleName" label="审批角色"></el-table-column>
          <el-table-column prop="approvalUserName" label="审批人"></el-table-column>
          <el-table-column prop="auditStatus" label="审批状态">
            <template slot-scope="{ row }">
              <el-tag :type="row.auditStatus | ycrhAuditStatusStyle">{{ row.auditStatus | ycrhAuditStatusInfo
                }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="auditResult" label="审批备注"></el-table-column>
          <el-table-column prop="approvalTime" label="审批时间" />
        </el-table>
      </template>

      <!-- 京东商品 -->
      <el-descriptions title="物流信息" :column="1" v-if="isJD">
        <!-- 物流信息 -->
        <el-descriptions-item labelClassName="no-colon">
          <!-- 包裹详情 -->
          <el-timeline>
            <el-timeline-item v-for="(activity, index) in detailGroups.trackInfoList" :key="index"
              v-show="index < 3 || showJdDeliveryMore" :timestamp="parseTime(activity.trackMsgTime)">
              {{ activity.trackOperator }}-{{ activity.trackContent }}
            </el-timeline-item>
          </el-timeline>
          <el-button icon="el-icon-more" size="small" @click="showJdDeliveryMore = !showJdDeliveryMore">{{
            showJdDeliveryMore
            ? '收起' : '显示更多' }}</el-button>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 自建商品 -->
      <template v-if="!isJD">
        <el-descriptions title="物流信息" :column="4" direction="vertical" border style="margin-top:20px;">
          <!-- 京东商品没有手动发货，要判断是不是京东 -->
          <div slot="extra" v-if="[2, 3, 4, 5, 6].includes(order.status) && !isJD"
            v-hasPermi="['trade:delivery:create']">
            <el-button type="primary" size="small" @click="orderDelivery">订单发货</el-button>
            <el-button v-if="order.deliveryRespVOS != null && order.deliveryRespVOS.length > 0" type="primary" size="small" @click="deleteDelivery">删除物流</el-button>
          </div>
        </el-descriptions>
        <template v-if="deliveryInfoList && deliveryInfoList.length">
          <el-tabs v-model="deliveryName" class="tabs" type="card">
            <el-tab-pane v-for="(item, index) in deliveryInfoList" :label="`包裹${index + 1}`" :key="index"
              :name="String(index)"></el-tab-pane>
          </el-tabs>
          <delivery-info :deliveryInfo="deliveryInfoList[deliveryName]"></delivery-info>
        </template>
        <el-empty v-else></el-empty>
      </template>
    </template>

    <!-- 订单发货 -->
    <delivery-send :open.sync="deliverySendOpen" :orders="selectOrders" @close="deliverySendClose" />

    <el-dialog title="申请售后" :visible.sync="afterSaleOpen" width="800px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="afterSaleForm" label-width="120px">
        <el-form-item label="售后商品" name="skuName">
          <span>{{ afterSaleForm.skuInfo.skuName }}</span>
        </el-form-item>
        <el-form-item label="售后方式" name="way">
          <el-radio-group v-model="afterSaleForm.way">
            <el-radio :label="10">仅退款</el-radio>
            <el-radio :label="20">退货退款</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="联系人" name="contact">
              <el-input style="width: 210px;" v-model="afterSaleForm.contact" :maxlength="50"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电话" name="phone">
              <el-input style="width: 210px;" v-model="afterSaleForm.phone" :maxlength="50"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="售后原因" name="applyReason">
          <el-select v-model="afterSaleForm.applyReason">
            <el-option v-for="(item, index) in reasonList" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="售后商品数量" name="count">
              <el-input-number :min="1" :max="maxRefundCount" v-model="afterSaleForm.count"
                :allow-input-over-limit="false"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="退款金额" name="refundPrice">
              <el-input style="width: 210px;" :value="comRefundPrice" label="￥" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="退款说明" name="applyDescription">
          <el-input type="textarea" placeholder="退款说明" v-model="afterSaleForm.applyDescription"
            :autosize="{ minRows: 3, maxRows: 5 }" maxlength="300" show-word-limit>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="afterSaleCancel">取 消</el-button>
        <el-button type="primary" @click="submitAfterSaleForm">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="添加备注" :visible.sync="remarkOpen" width="800px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="remarkForm" label-position="top" label-width="120px">
        <el-form-item label="备注">
          <el-input type="textarea" :rows="6" v-model="remarkForm.remark" placeholder="请输入备注" :maxlength="300"
            show-word-limit />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="remarkCancel">取 消</el-button>
        <el-button type="primary" @click="submitRemarkForm">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { isBusinessPay } from '@/utils/mallUtil'
import * as orderApi from "@/api/mall/trade/order.js"
import * as afterSaleApi from "@/api/mall/trade/afterSale.js"
import * as deliveryApi from "@/api/mall/trade/delivery"
import deliveryInfo from '@/views/mall/trade/order/deliveryInfo.vue'
import { AFTER_SALE_REASON_LIST } from '@/utils/mallUtil.js'
import OrderTagCom from '@/views/mall/trade/order/components/orderTag'
import OrderStatusCom from '@/views/mall/trade/order/components/orderStatus' 
import { configMixins } from '@/views/mall/config/components/configMixin.js'
import OrderAcceptInfo from '@/views/mall/trade/order/components/acceptInfo'
import DeliverySend from "@/views/mall/trade/delivery/components/delivery-send";

export default {
  name: "TradeOrderDetailCom",
  components: {
    DeliverySend
  },
  mixins: [ configMixins ],
  props: {
    showOrderTag: {
      type: Boolean,
      default() {
        return false
      }
    }
  },
  data() {
    return {
      decimals: 2,
      loading: false,
      loading2: false,
      activeName: 'tab1',
      order: null,
      auditArr: [],
      detailGroups: null,
      deliveryInfoList: [],
      deliveryName: 0,
      deliverySendOpen: false,
      selectOrders: [],
      afterSaleOpen: false,
      afterSaleForm: {
        way: 10,
        contact: '',
        phone: '',
        applyReason: '',
        count: 1,
        refundPrice: 0,
        applyDescription: '与买家协商一致退款',
        files: [],
        skuInfo: {
          orderItemId: '',
          skuName: '',
          skuId: '',
          skuPrice: '',
          skuNum: '',
          afterSaleMemo: '',
          afterSaleAvailableCount: 0,
          canAfterSale: true,
          afterSaleStatus: 0
        }
      },
      remarkOpen: false,
      remarkForm: {
        remark: ''
      },
      initCompanyList: [],
      operateLogList: [],
      showJdDeliveryMore: false,
      parentOrderDetail: {}
    }
  },
  computed: {
    isJD() {
      return this.order && this.order.supplierType && this.order.supplierType == 1
    },
    reasonList() {
      return AFTER_SALE_REASON_LIST.filter(item => item.way.includes(this.afterSaleForm.way))
    },
    maxRefundCount() {
      return this.afterSaleForm.skuInfo.afterSaleAvailableCount
    },
    comRefundPrice() {
      return (this.afterSaleForm.skuInfo.skuPrice * this.afterSaleForm.count).toFixed(2)
    },
    havePurchaseInfo() {
      return this.order.purchaseInfo
    },
    purchaseAttachments() {
      if(this.havePurchaseInfo) {
        let attachments = this.order.purchaseInfo.attachments || ''
        if(attachments) return attachments.split(',') || []
      }
      return []
    }
  },
  watch: {
  },
  mounted() {
  },
  components: {
    deliveryInfo, OrderTagCom, OrderStatusCom, OrderAcceptInfo, DeliverySend
  },
  created() {
    this.init()
  },
  methods: {
    isBusinessPay(pm) {
      return isBusinessPay(pm)
    },
    init() {
      this.loading = true
      this.activeName = 'tab1'
      orderApi.getOrderDetail({
        id: this.$route.query.id || ''
      }).then(res => {
        this.order = res.data
        this.detailGroups = res.data.deliveryInfo || {
          trackInfoList: []
        }
        this.deliveryInfoList = res.data.deliveryRespVOS || []
        this.getAuditResult()
        if(this.$store.getters.permissions.includes('trade:order-operate-log:query')){
          this.loadOrderOperateLogList()
        }
        this.loading = false

        this.afterSaleForm.contact = res.data.address.name
        this.afterSaleForm.phone = res.data.address.mobile
        if (this.notSupplierUser()){
          this.loadParentDetail()
        }
      })
    },
    showAfterSale(skuInfo) {
      return skuInfo.canAfterSale
    },
    getRefundNumStyle(row) {
      let diff = row.skuNum - row.afterSaleAvailableCount
      if(diff === 0) {
        return 'success'
      } else if(diff == row.skuNum) {
        return 'danger'
      } 
      return 'warning'
    },
    showSubOrder(row) {
      this.$router.push({ name: 'TradeOrderDetail', query: { id: row.id } })
      this.init()
    },
    loadParentDetail() {
      if (!this.order || this.order.parentType !== 0 || !this.order.parentOrderId) {
        return
      }
      orderApi.getOrderParentDetail({
        parentOrderId: this.order.parentOrderId
      }).then(res => {
        this.parentOrderDetail = res.data || {}
      })
    },
    async loadOrderOperateLogList() {
      const res = await orderApi.getOrderOperateLogPage({
        pageNo: 1,
        pageSize: 100,
        orderId: this.$route.query.id || ''
      })
      this.operateLogList = []
      if (res.code === 0) {
        this.operateLogList = res.data.list || []
      }
    },
    // 确认订单
    confirmOrder() {
      let param = { orderId: this.order.id || '' }
      this.$modal.confirm('订单确认后将进入发货流程，确认执行此操作吗?').then(function () {
        return orderApi.confirmOrder(param);
      }).then((res) => {
        this.init()
        this.$message.success('已确认该订单')
      }).catch(() => { });
    },
    // 线下结算
    setOfflineSettlement() {
      let param = { orderId: this.order.id || '' , offlineSettlement: true}
      this.$modal.confirm('设置线下结算将解冻订单，确认执行此操作吗?').then(function () {
        return orderApi.setOfflineSettlement(param);
      }).then((res) => {
        this.init()
        this.$message.success('已设置线下结算')
      }).catch(() => { });
    },
    // 订单项放开售后
    openAfterSale(item) {
      let param = { orderId: this.order.id || '', orderItemId: item.orderItemId }
      this.$modal.confirm('确认放开售后吗?').then(function () {
        return orderApi.openAfterSale(param);
      }).then((res) => {
        this.init()
        this.$message.success('操作成功')
      }).catch(() => { });
    },
    // 订单签收
    receiveOrder() {
      let param = { orderId: this.order.id || '' }
      this.$modal.confirm('确认执行订单签收吗?').then(function () {
        return orderApi.receiveOrder(param);
      }).then((res) => {
        this.init()
        this.$message.success('订单已签收')
      }).catch(() => { });
    },
    // 完成订单
    completeOrder() {
      let param = { orderId: this.order.id || '' }
      this.$modal.confirm('确认执行订单完成吗?').then(function () {
        return orderApi.completeOrder(param);
      }).then((res) => {
        this.init()
        this.$message.success('该订单已完成')
      }).catch(() => { });
    },
    // 取消订单
    cancelOrder() {
      let param = { orderId: this.order.id || '' }
      this.$modal.confirm('确认执行订单取消吗?').then(function () {
        return orderApi.cancelOrder(param);
      }).then((res) => {
        this.init()
        this.$message.success('该订单已取消')
      }).catch(() => { });
    },
    // 获取订单审批信息
    async getAuditResult() {
      const res = await orderApi.getAuditResult({
        id: this.order.id || ''
        // id: '1691286048924659714'
      })
      if (res.code === 0) {
        this.auditArr = res.data || []
      }
    },
    // 预览发票
    previewInvoice(data) {
      if (data.fileUrl) {
        window.open(data.fileUrl, '_blank')
      }
    },
    toMemberDetail(id) {
      this.$router.push({ name: 'MemberDetail', query: { id } })
    },
    // 发货完成处理
    deliverySendClose(val) {
      if (val) {
        this.init()
      }
    },
    deleteDelivery() {
      this.$modal.confirm('该操作将会清理订单绑定的物流信息，确认执行此操作吗?').then(() => {
        this.loading = true
        deliveryApi.deleteByOrder(this.order.no).then(res => {
          this.loading = false
          if (res.code == 0) {
            this.$message.success('删除物流成功')
            this.init()
          }
          else {
            this.$message.error(res.msg || '删除物流失败')
          }
        })
      })
    },
    remarkCancel() {
      this.remarkOpen = false
      this.remarkForm = {
        remark: ''
      }
    },
    orderDelivery() {
      const item = {
        id: this.order.id,
        no: this.order.no,
        skuInfoList: this.order.skuInfoList.map(x => ({
          skuId: x.skuId,
          skuName: x.skuName,
          skuPrice: x.skuPrice,
          skuNum: x.skuNum,
          skuImgUrl: x.imgUrl
        }))
      }
      this.selectOrders = [item]
      this.deliverySendOpen = true
    },
    async setRemark() {
      const res = await orderApi.setRemark({
        orderId: this.order.id,
        remark: this.remarkForm.remark
      })
      if (res.code == 0) {
        this.$message.success('订单备注成功')
        this.remarkOpen = false
        this.remarkForm = {
          remark: ''
        }
        this.init()
      }
    },
    addRemark() {
      if (this.order.remark) {
        this.remarkForm.remark = this.order.remark
      }
      this.remarkOpen = true
    },
    submitRemarkForm() {
      this.setRemark()
    },
    async getAfterSaleComponentUrl(orderItemId) {
      const res = await afterSaleApi.getAfterSaleComponentUrl({
        orderItemId: orderItemId
      })
      if (res.code == 0 && res.data) {
        window.open(res.data)
      }
    },
    afterSaleApply(skuInfo) {
      if (this.order.supplierName.includes('京东')){
        this.getAfterSaleComponentUrl(skuInfo.orderItemId)
      } else {
        if (this.afterSaleForm) {
          this.afterSaleForm.skuInfo = skuInfo
        }
        this.afterSaleOpen = true
      }
    },
    async submitAfterSale() {
      if(this.afterSaleForm.contact == null || this.afterSaleForm.contact.length == 0){
        this.$message.error('请填写联系人')
        return
      }
      if(this.afterSaleForm.phone == null || this.afterSaleForm.phone.length == 0){
        this.$message.error('请填写联系电话')
        return
      }
      if(this.afterSaleForm.applyReason == null || this.afterSaleForm.applyReason.length == 0){
        this.$message.error('请选择售后原因')
        return
      }
      if(this.afterSaleForm.applyDescription == null || this.afterSaleForm.applyDescription.length == 0){
        this.$message.error('请填写退款说明')
        return
      }
      const params = {
        way: this.afterSaleForm.way,
        count: this.afterSaleForm.count,
        refundPrice: this.comRefundPrice,
        applyReason: this.afterSaleForm.applyReason,
        contact: this.afterSaleForm.contact,
        phone: this.afterSaleForm.phone,
        applyDescription: this.afterSaleForm.applyDescription,
        applyPicUrls: null,
        orderItemId: this.afterSaleForm.skuInfo.orderItemId
      }
      const res = await afterSaleApi.createAfterSale(params)
      if (res.code == 0) {
        this.$message.success('提交成功')
        this.afterSaleOpen = false
        this.afterSaleForm = {
          way: 10,
          contact: this.order.address.name,
          phone: this.order.address.mobile,
          applyReason: '',
          count: 1,
          refundPrice: 0,
          applyDescription: '与买家协商一致退款',
          files: [],
          skuInfo: {
            orderItemId: '',
            skuName: '',
            skuId: '',
            skuPrice: '',
            skuNum: '',
            afterSaleMemo: '',
            afterSaleAvailableCount: 0,
            canAfterSale: true,
            afterSaleStatus: 0
          }
        }
        this.init()
      }
    },
    submitAfterSaleForm() {
      this.submitAfterSale()
    },
    afterSaleCancel() {
      this.afterSaleOpen = false
      this.afterSaleForm = {
        way: 10,
        contact: this.order.address.name,
        phone: this.order.address.mobile,
        applyReason: '',
        count: 1,
        refundPrice: 0,
        applyDescription: '与买家协商一致退款',
        files: [],
        skuInfo: {
          orderItemId: '',
          skuName: '',
          skuId: '',
          skuPrice: '',
          skuNum: '',
          afterSaleMemo: '',
          afterSaleAvailableCount: 0,
          canAfterSale: true,
          afterSaleStatus: 0
        }
      }
    },
    parentRowClassName({ row, rowIndex }) {
      if (row.no === this.order.no) {
        return 'success-row';
      }
      return '';
    },
    jumpAssetIndex(orderItem) {
      this.$router.push({ 
        name: 'TradeOrderAssets',
        query: {
          orderNo: this.order.no
        }
      })
    },
    jumpAfterSale(orderItem) {
      this.$router.push({ 
        name: 'TradeAfterSale',
        query: {
          orderNo: this.order.no
        }
      })
    },
    async syncYrchOrder() {
      const res = await orderApi.syncOrderYcrh({
        orderNo: this.order.no,
      })
      this.$message.success('推送成功')
      this.init()
    },
    async refreshYcrhStatus() {
      const res = await orderApi.refreshYcrhStatus({
        orderNo: this.order.no,
      })
      this.$message.success('同步成功')
      this.init()
    },
    async cancelYcrhOrder() {
      let param = { orderNo: this.order.no || '' }
      this.$modal.confirm('确认执行此操作吗?').then(function () {
        return orderApi.cancelOrderYcrh(param);
      }).then((res) => {
        this.init()
        this.$message.success('操作成功')
      }).catch(() => { });
    },
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-descriptions) {
  &:not(:nth-child(1)) {
    margin-top: 20px;
  }

  .el-descriptions__title {
    display: flex;
    align-items: center;

    &::before {
      content: '';
      display: inline-block;
      margin-right: 10px;
      width: 3px;
      height: 20px;
      background-color: #409EFF;
    }
  }

  .el-descriptions-item__container {
    margin: 0 10px;

    .no-colon {
      margin: 0;

      &::after {
        content: ''
      }
    }
  }
}

.clickInvoice {
  cursor: pointer;

  &:hover {
    color: #f13d33;
  }
}
</style>
<style lang="scss">
.el-table .success-row {
  background: #f0f9eb;
}
</style>
