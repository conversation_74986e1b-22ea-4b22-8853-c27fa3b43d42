<template>
  <div class="app-container">
    <div> 
      <el-card class="box-card" v-for="config in configList" :key="config.id">
        <div slot="header" class="clearfix">
          <span class="title1">{{ config.sysName }} / {{ config.sysCode || '--' }} </span>
          <span class="title1">【排序: {{ config.sort }} - 值越大优先级越高】</span>
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="config.status" />
          <el-button style="float: right; padding: 5px" type="text" @click="editConfig(config)">修改</el-button>
          <el-button style="float: right; padding: 5px" type="text" @click="deleteConfig(config)">删除</el-button>
        </div>
        <div>
          <el-descriptions :column="2" :border="true">
            <el-descriptions-item label="接口地址">{{ config.apiHost || '--' }}</el-descriptions-item>
            <el-descriptions-item label="应用JWTsecret">{{ config.key1 || '--' }}</el-descriptions-item>
            <el-descriptions-item label="应用JWTaeskey">{{ config.key2 || '--' }}</el-descriptions-item>
            <el-descriptions-item label="应用JWTissueId">{{ config.key3 || '--' }}</el-descriptions-item>
            <el-descriptions-item label="快捷跳转地址">{{ config.linkUrl || '--' }}</el-descriptions-item>
            <el-descriptions-item label="扩展参数1" v-if="config.paramExt1">{{ config.paramExt1 || '--' }}</el-descriptions-item>
            <el-descriptions-item label="扩展参数2" v-if="config.paramExt2">{{ config.paramExt2 || '--' }}</el-descriptions-item>
            <el-descriptions-item label="扩展参数3" v-if="config.paramExt3">{{ config.paramExt3 || '--' }}</el-descriptions-item>
            <el-descriptions-item label="下单固资校验开关">
              <el-tag :type="config.orderValidateSwitch ? 'primary' : 'info'">{{ config.orderValidateSwitch ? "打开" : "关闭" }}</el-tag>
              <el-tag v-if="config.orderValidateSwitch" style="margin-left:10px;"> 固资建档未完成的订单数量到达 {{ config.orderValidateThreshold }} 时无法下单</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="回调地址">{{ getCallbackUrl(config) }}</el-descriptions-item>
            <el-descriptions-item label="备注" v-if="config.memo">{{ config.memo || '--' }}</el-descriptions-item>
          </el-descriptions>
        </div>
        <div>
          <AssetsRule :configId="config.id"></AssetsRule>
        </div>
        <div>
          <AssetsEntry :configId="config.id"></AssetsEntry>
        </div>
      </el-card>
      <el-empty v-if="!configList || !configList.length">
        <el-button type="text" icon="el-icon-plus" @click="addConfig">开始配置</el-button>
      </el-empty>
      <el-button v-else type="primary" icon="el-icon-plus" @click="addConfig" style="margin: 10px 0;">增加配置</el-button>
    </div>

    <el-dialog :title="title" :visible.sync="open" width="900px" v-dialogDrag append-to-body>
      <el-form ref="configForm" :model="configForm" :rules="configRules" label-width="150px">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="对接系统" prop="sysCode">
              <el-select v-model="configForm.sysCode" placeholder="请选择系统" style="width:100%">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MALL_INTEGRATION_ASSETS_TYPE)"
                        :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="configForm.status">
                  <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                            :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
                  </el-radio>
                </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <el-input-number v-model="configForm.sort" :min="1" :max="99999" placeholder="请输入排序" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="接口地址" prop="apiHost">
              <el-input v-model="configForm.apiHost" maxlength="200" placeholder="请输入接口地址" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="应用JWTsecret" prop="key1">
          <el-input v-model="configForm.key1" maxlength="50" placeholder="请输入应用JWTsecret" />
        </el-form-item>
        <el-form-item label="应用JWTaesKey" prop="key2">
          <el-input v-model="configForm.key2" maxlength="100" placeholder="请输入应用JWTaesKey" />
        </el-form-item>
        <el-form-item label="应用JWTissueId" prop="key3">
          <el-input v-model="configForm.key3" maxlength="50" placeholder="请输入应用应用JWTissueId" />
        </el-form-item>
        
        <el-form-item label="快捷跳转地址" prop="linkUrl">
          <el-input v-model="configForm.linkUrl" maxlength="200" placeholder="请输入快捷跳转地址" />
        </el-form-item>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="扩展参数1" prop="paramExt1">
              <el-input v-model="configForm.paramExt1" maxlength="50" placeholder="请输入扩展参数1" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="扩展参数2" prop="paramExt2">
              <el-input v-model="configForm.paramExt2" maxlength="50" placeholder="请输入扩展参数2" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="扩展参数3" prop="paramExt3">
              <el-input v-model="configForm.paramExt3" maxlength="50" placeholder="请输入扩展参数3" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="备注" prop="memo">
          <el-input v-model="configForm.memo" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" maxlength="200" show-word-limit placeholder="请输入备注" />
        </el-form-item>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="下单固资校验开关" prop="orderValidateSwitch">
              <el-switch
                v-model="configForm.orderValidateSwitch"
                active-text="打开"
                inactive-text="关闭"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="configForm.orderValidateSwitch">
            <el-form-item label="下单固资校验阈值" prop="orderValidateThreshold" :rules="[{required: true, message: '下单固资校验阈值不能为空', trigger: 'blur'}]">
              <el-input-number
                v-model="configForm.orderValidateThreshold"
                :min="1" :max="30"
                placeholder="请输入下单固资校验阈值,下单时会检查当前固资建档中的订单数量，到达此阈值将无法下单"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>

    
  </div>
</template>

<script>
import * as api from "@/api/mall/config/assetsConfig";
import AssetsRule from "./assets-rule";
import AssetsEntry from "./assets-entry";
export default {
  name: "MallAssetsConfig",
  components: { AssetsRule, AssetsEntry },
  data() {
    return {
      configList: [],
      configInfo: {},
      formMode: 'init',
      open: false,
      title: '',
      submitLoading: false,
      configForm: {},
      configRules: {
        sysCode: [
          { required: true, trigger: 'change', message: '请选择系统' }
        ],
        apiHost: [
          { required: true, trigger: 'blur', message: '请输入接口地址' },
          { type: 'url', trigger: 'blur', message: '接口地址格式不正确' }
        ],
        status: [
          { required: true, trigger: 'blur', message: '请选择状态' }
        ],
        sort: [
          { required: true, trigger: 'blur', message: '请输入排序' }
        ],
        key1: [
          { required: true, trigger: 'blur', message: '请输入应用JWTsecret' }
        ],
        key2: [
          { required: true, trigger: 'blur', message: '请输入应用JWTaesKey' }
        ],
        key3: [
          { required: true, trigger: 'blur', message: '请输入应用JWTissueId' }
        ]
      },

      submitLoading2: false,
      ruleOpen: false,
      ruleTitle: '',
      ruleForm: {},
      ruleRules: {
        name: [
          { required: true, trigger: 'change', message: '请输入规则名称' }
        ],
        type: [
          { required: true, trigger: 'blur', message: '请选择规则类型' }
        ],
        status: [
          { required: true, trigger: 'blur', message: '请选择状态' }
        ],
      }
    }
  },
  computed: {
    sysName() {
      return this.getDictDataLabel(this.DICT_TYPE.MALL_INTEGRATION_ASSETS_TYPE, this.configForm.sysCode) || ''
    }
  },
  created() {
    this.loadConfigList()
  },
  methods: {
    getCallbackUrl(config) {
      return `https://{host}/app-api/mall/external/assets/common/${config.sysCode}/updateStatus`
    },
    async loadConfigList() {
      let params = {
        pageNo: 1,
        pageSize: 50
      }
      let res = await api.getAssetsConfigList(params);
      this.configList = res.data.list || []
    },
    reset() {
      this.configForm = {
        id: undefined,
        sysName: '',
        sysCode: '',
        apiHost: '',
        key1: '',
        key2: '',
        key3: '',
        linkUrl: '',
        paramExt1: '',
        paramExt2: '',
        paramExt3: '',
        memo: '',
        orderValidateSwitch: false,
        orderValidateThreshold: 0,
        status: 0,
        sort: 1
      }
      this.resetForm("configForm");
    },
    addConfig() {
      this.reset()
      this.title = '增加固定资产配置'
      this.open = true
    },
    deleteConfig(configInfo) {
      const id = configInfo.id;
      this.$modal.confirm('是否确认删除"' + configInfo.sysName + '"的配置项?').then(function() {
        return api.deleteAssetsConfig(id);
      }).then(() => {
        this.loadConfigList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    editConfig(configInfo) {
      this.reset()
      this.title = '编辑固定资产配置'
      Object.assign(this.configForm, configInfo)
      this.open = true
    },
    cancelForm() {
      this.open = false
    },
    async doSubmit() {
      this.submitLoading = true
      try {
        this.configForm.sysName = this.sysName
        if (this.configForm.id !== undefined) {
          await api.updateAssetsConfig(this.configForm)
          this.$modal.msgSuccess("修改成功");
        } else {
          await api.createAssetsConfig(this.configForm)
          this.$modal.msgSuccess("保存成功");
        }
        this.submitLoading = false
        this.open = false
        this.loadConfigList()
      } catch(e) {
        this.submitLoading = false
      }
    },
    submitForm() {
      this.$refs["configForm"].validate(valid => {
        if (!valid) {
          return;
        }
        this.doSubmit()
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .box-card:not(:first-child) {
    margin: 40px 0 20px;
  }
  .title1 {
    font-size: 14px;
    font-weight: 600;
  }
}
</style>