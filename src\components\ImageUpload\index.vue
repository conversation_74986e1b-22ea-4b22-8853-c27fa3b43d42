<template>
  <div class="component-upload-image">
    <el-upload
      multiple
      :action="uploadFileUrl"
      list-type="picture-card"
      :on-success="handleUploadSuccess"
      :before-upload="handleBeforeUpload"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      name="file"
      :on-remove="handleRemove"
      :disabled="disabled"
      :show-file-list="true"
      :headers="headers"
      :file-list="fileList"
      :on-preview="handlePictureCardPreview"
      :class="{ hide: this.fileList.length >= this.limit }"
    >
      <i class="el-icon-plus"></i>
    </el-upload>

    <!-- 上传提示 -->
    <div class="el-upload__tip" slot="tip" v-if="showTip">
      请上传
      <template v-if="fileSize">
        大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
      <template v-if="fileType">
        格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
      </template>
      的文件
    </div>
    <div v-if="sortable && fileList.length" style="margin-top:10px;"> 
      <el-button type="primary" round icon="el-icon-sort"  @click="dialogVisible2 = true">调整排序</el-button>
    </div>

    <el-dialog :visible.sync="dialogVisible" title="预览" width="800" append-to-body>
      <img :src="dialogImageUrl" style="display: block; max-width: 100%; margin: 0 auto"/>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">关 闭 </el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="dialogVisible2" title="商品图册编辑排序" width="500px" append-to-body>
      <div class="image-list-con"> 
        <el-card class="box-card" v-for="(item,index) in fileList" :key="index">
          <div class="flex-center" >
            <div style="width:100px;"><img :src="item.url" style="width:100%"></div>
            <div>
              <el-button type="primary" round size="small" icon="el-icon-arrow-up" v-if="index !== 0" @click="sortUp(item,index)"></el-button>
              <el-button type="primary" round size="small" icon="el-icon-arrow-down" v-if="index !== fileList.length - 1" @click="sortDown(item,index)"></el-button>
            </div>
          </div>
        </el-card>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible2 = false">关 闭 </el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import { getAccessToken } from "@/utils/auth";

export default {
  props: {
    value: [String, Object, Array],
    // 图片数量限制
    limit: {
      type: Number,
      default: 5,
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ["png", "jpg", "jpeg"],
    },
    // 结果类型 1-字符串 2-对象
    resType: {
      type: Number,
      default: 1,
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true,
    },
    // 是否显示提示
    disabled: {
      type: Boolean,
      default: false,
    },
    sortable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      number: 0,
      uploadList: [],
      dialogImageUrl: "",
      dialogVisible: false,
      dialogVisible2: false,
      hideUpload: false,
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/admin-api/infra/file/v2/upload", // 请求地址
      headers: { Authorization: "Bearer " + getAccessToken() }, // 设置上传的请求头部
      fileList: [],
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          // 首先将值转为数组, 当只穿了一个图片时，会报map方法错误
          const list = Array.isArray(val)
            ? val
            : Array.isArray(this.value.split(","))
            ? this.value.split(",")
            : Array.of(this.value);
          // 然后将数组转为对象数组
          this.fileList = list.map((item) => {
            if (typeof item === "string") {
              // edit by 金采通
              item = { name: this.getFileNameFromUrl(item), url: item };
            }
            return item;
          });
        } else {
          this.fileList = [];
          return [];
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize);
    },
  },
  methods: {
    emitFileList() {
      this.$emit("input", this.handleList(this.fileList));
    },
    // 删除图片
    handleRemove(file, fileList) {
      const findex = this.fileList.map((f) => f.name).indexOf(file.name);
      if (findex > -1) {
        this.fileList.splice(findex, 1);
        this.emitFileList();
      }
    },
    // 上传成功回调
    handleUploadSuccess(res) {
      let file = this.parseFileResponse(res.data)
      // edit by 金采通
      this.uploadList.push(file);
      if (this.uploadList.length === this.number) {
        this.fileList = this.fileList.concat(this.uploadList);
        this.uploadList = [];
        this.number = 0;
        this.emitFileList();
        this.$modal.closeLoading();
      }
    },
    // 上传前loading加载
    handleBeforeUpload(file) {
      let isImg = false;
      if (this.fileType.length) {
        let fileExtension = "";
        if (file.name.lastIndexOf(".") > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
        }
        isImg = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true;
          return !!(fileExtension && fileExtension.indexOf(type) > -1);
        });
      } else {
        isImg = file.type.indexOf("image") > -1;
      }

      if (!isImg) {
        this.$modal.msgError(
          `文件格式不正确, 请上传${this.fileType.join("/")}图片格式文件!`
        );
        return false;
      }
      if (this.fileSize) {
        const isLt = file.size * 1.0 / 1024 / 1024 < this.fileSize;
        if (!isLt) {
          this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`);
          return false;
        }
      }
      this.$modal.loading("正在上传图片，请稍候...");
      this.number++;
    },
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);
    },
    // 上传失败
    handleUploadError() {
      this.$modal.msgError("上传图片失败，请重试");
      this.$modal.closeLoading();
    },
    // 预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    parseFileResponse(data) {
      const result = { name: null, url: null };
      if (!data) return result;
      // 提取url
      result.url = typeof data === 'string' 
        ? data 
        : data.url || null;
        
      // 提取文件名
      if (typeof data === 'string') {
        result.name = this.getFileNameFromUrl(data);
      } else if (data.name && typeof data.name === 'string') {
        result.name = data.name.trim() || this.getFileNameFromUrl(result.url);
      } else if (result.url) {
        result.name = this.getFileNameFromUrl(result.url);
      }
      
      return result;
    },
    getFileNameFromUrl(url) {
      try {
        if(!url) {
          return ''
        }
        const pathname = new URL(url).pathname
        const cleanPath = pathname.endsWith('/') 
          ? pathname.slice(0, -1) 
          : pathname
        const filename = cleanPath.substring(cleanPath.lastIndexOf('/') + 1)
        return filename || ''
      } catch (error) {
        // URL 解析失败时提供降级方案
        const fallback = url.substring(url.lastIndexOf('/') + 1)
        return fallback.split(/[?#]/)[0] || ''
      }
    },
    // 对象转成指定字符串分隔
    listToString(list, separator) {
      let strs = "";
      separator = separator || ",";
      for (let i in list) {
        strs += list[i].url.replace(this.baseUrl, "") + separator;
      }
      return strs !== "" ? strs.substr(0, strs.length - 1) : "";
    },
    handleList(list, separator) {
      if(this.resType === 2) {
        return list
      } 
      return this.listToString(list, separator)
    },
    sortUp(item, index) {
      // 如果当前元素是第一个，则无法再向上移动
      if (index <= 0) return
      // 使用数组的splice方法交换相邻元素位置
      const list = this.fileList
      list.splice(index - 1, 2, list[index], list[index - 1])
      this.emitFileList()
    },
    sortDown(item, index) {
      // 如果当前元素是最后一个，则无法再向下移动
      if (index >= this.fileList.length - 1) return
      // 使用数组的splice方法交换相邻元素位置
      const list = this.fileList
      list.splice(index, 2, list[index + 1], list[index])
      this.emitFileList()
    }
  },
};
</script>
<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
:deep(.hide .el-upload--picture-card) {
  display: none;
}
// 去掉动画效果
:deep(.el-list-enter-active, .el-list-leave-active) {
  transition: all 0s;
}

:deep(.el-list-enter, .el-list-leave-active) {
  opacity: 0;
  transform: translateY(0);
}
.image-list-con {
  .box-card {
    margin: 5px 0;
  }

}
</style>
