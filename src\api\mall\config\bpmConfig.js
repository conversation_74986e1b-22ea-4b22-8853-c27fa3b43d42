import request from '@/utils/request'

// 创建审批系统集成配置
export function createBpmConfig(data) {
  return request({
    url: '/mall/bpm-config/create',
    method: 'post',
    data: data
  })
}

// 更新审批系统集成配置
export function updateBpmConfig(data) {
  return request({
    url: '/mall/bpm-config/update',
    method: 'put',
    data: data
  })
}

// 删除审批系统集成配置
export function deleteBpmConfig() {
  return request({
    url: '/mall/bpm-config/delete',
    method: 'delete'
  })
}

// 获得审批系统集成配置
export function getBpmConfig() {
  return request({
    url: '/mall/bpm-config/get',
    method: 'get'
  })
}
