<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="品牌" prop="brand">
        <el-input v-model="queryParams.brand" placeholder="请输入品牌" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="联系人" prop="contact">
        <el-input v-model="queryParams.contact" placeholder="请输入联系人" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="用户名" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入会员用户" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择" clearable="">
          <el-option :value="1" label="已回复"></el-option>
          <el-option :value="0" label="未回复"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['product:wish:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="商品分类" align="center" prop="categoryNames"/>
      <el-table-column label="品牌" align="center" prop="brand" />
      <el-table-column label="数量" align="center" prop="quantity" width="100"/>
      <el-table-column label="用户名" align="center" prop="userName" width="100"/>
      <!-- <el-table-column label="预计下单时间" align="center" prop="orderTime">
        <template v-slot="scope">
          <span>{{ scope.row.orderTime ? parseTime(scope.row.orderTime, '{y}-{m}-{d}') : '--' }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="联系人" align="center" prop="contact" />
      <el-table-column label="联系电话" align="center" prop="phone" />
      <el-table-column label="状态" align="center" prop="status">
        <template v-slot="scope">
          <el-tag :type="scope.row.status === 0 ? 'info' : 'primary'">{{ scope.row.status === 0 ? '待回复' : '已回复'}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-eye" @click="showDetail(scope.row)"
                     v-hasPermi="['product:wish:query']">查看详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" v-if="!scope.row.status" @click="reply(scope.row)"
                     v-hasPermi="['product:wish:update']">回复</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['product:wish:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    
    <el-drawer
      title="查看详情"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="60%">
      <ProductWishDetail ref="productWishDetail" />
    </el-drawer>

    <!-- 回复框 -->
    <el-dialog title="回复心愿单" :visible.sync="open" width="650px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" labelPosition="top">
        <el-form-item label="回复内容" prop="replyContent">
          <el-input v-model="form.replyContent" maxlength="200" :rows="4" type="textarea" placeholder="请输入内容"/>
          <div> 
            <el-tag type="success" @click="form.replyContent = '商品匹配成功'">上架成功提示</el-tag>
            <el-tag style="margin: 0 20px;" @click="form.replyContent = '抱歉，该商品不符合采购要求无法匹配'">未能上架提示</el-tag>
          </div>
        </el-form-item>
        <el-form-item label="指定上架商品" prop="selectedSkuName">
          <el-input v-model="form.selectedSkuName" maxlength="200" placeholder="点击搜索SKU" readonly>
            <el-button slot="append" icon="el-icon-search" @click="selectProductSku"></el-button>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitLoading" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>

    <ProductSkuSelect ref="productSkuSelect" radio @on-confirmed="handleAddCallback"></ProductSkuSelect>

  </div>
</template>

<script>
import ProductSkuSelect from "@/views/mall/product/spu/components/sku-select";
import * as api from "@/api/mall/product/wish";
import ProductWishDetail from '@/views/mall/product/wish/detail'
export default {
  name: "ProductWish",
  components: {
    ProductWishDetail,ProductSkuSelect
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      drawerVisible: false,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 心愿单列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        brand: null,
        contact: null,
        phone: null,
        userName: null,
        status: 0,
        createTime: [],
      },
      submitLoading: false,
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      let params = {...this.queryParams}
      if(params.status === '') {
        delete params.status
      }
      // 执行查询
      api.getWishPage(params).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除心愿单编号为"' + id + '"的数据项?').then(function() {
          return api.deleteWish(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    async handleUpdateStatus(row) {
      let params = {
        id: row.id,
        status: row.status === 0 ? 1 : 0
      }
      let res = await api.updateWishStatus(params)
      if(res.code === 0) {
        this.getList();
        this.$modal.msgSuccess("操作成功");
      }
    },
    showDetail(row) {
      this.drawerVisible = true
      this.$nextTick(() => {
        this.$refs.productWishDetail.init(row.id)
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有心愿单数据项?').then(() => {
          this.exportLoading = true;
          return api.exportWishExcel(params);
        }).then(response => {
          this.$download.excel(response, '心愿单.xls');
          this.exportLoading = false;
        }).catch(() => {});
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        status: 1,
        replyContent: '商品已经上架了',
        selectedSkuName: undefined,
        extParams: undefined
      };
      this.resetForm("form")
    },
    /** 回复按钮操作 */
    reply(row) {
      this.reset()
      this.form.id = row.id
      this.open = true
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        
        this.submitLoading = true
        let params = {...this.form}
        api.updateWishStatus(params).then(res => {
          this.getList()
          this.$modal.msgSuccess("操作成功")
        }).finally(() => {
          this.open = false
          this.submitLoading = false
        })
      });
    },
    selectProductSku() {
      this.$refs.productSkuSelect.init()
    },
    handleAddCallback(rows) {
      console.log('rows----', rows)
      if(!rows || !rows.length) {
        return
      }
      this.form.selectedSkuName = rows[0].skuName
      this.form.extParams = `${rows[0].supplierId},${rows[0].id}`
    },
  }
};
</script>
