<template>
  <div class="app-container">
    <el-empty v-if="!configInfo.id && formMode === 'init'">
      <el-button type="text" icon="el-icon-plus" @click="beginConfig">开始配置</el-button>
    </el-empty>
    <div v-if="configInfo.id && formMode === 'init'">
      <el-descriptions title="配置信息" :column="1" :border="true">
        <template slot="extra">
          <el-button type="success" size="small" @click="showHealthCheck" style="margin-right: 10px;">
            <i class="el-icon-monitor"></i> 接口检测
          </el-button>
          <el-button type="primary" size="small" @click="editConfig">修改</el-button>
        </template>
        <el-descriptions-item label="结算类接口地址">{{ configInfo.settlementHost || '--' }}</el-descriptions-item>
        <el-descriptions-item label="审批流类接口地址">{{ configInfo.approveHost || '--' }}
          <el-tag type="danger" v-if="bpmConfigInfo.sysCode && bpmConfigInfo.sysCode !== 'ycrh' "> 审批流集成配置非业财，请尽快检查 </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="appKey">{{ configInfo.appKey || '--' }}</el-descriptions-item>
        <el-descriptions-item label="appSecret">{{ configInfo.appSecret || '--' }}</el-descriptions-item>
        <!-- <el-descriptions-item label="调试模式">
          <el-tag :type="configInfo.testSwitch ? 'primary' : 'info'">{{ configInfo.testSwitch ? '打开' : '关闭' }}</el-tag>
          <span> 调试模式打开后不会触发订单自动确认，接口状态调试正常后方可关闭</span>
        </el-descriptions-item> -->
        <el-descriptions-item label="表单-验收人开关">
          <el-tag :type="configInfo.formAcceptorSwitch ? 'primary' : 'info'">{{ configInfo.formAcceptorSwitch ? '打开' : '关闭' }}</el-tag>
          <span> 控制采购申请表中的验收人字段显示</span>
        </el-descriptions-item>
        <el-descriptions-item label="表单-经济分类开关">
          <el-tag :type="configInfo.formEconomyClassSwitch ? 'primary' : 'info'">{{ configInfo.formEconomyClassSwitch ? '打开' : '关闭' }}</el-tag>
          <span> 控制采购申请表中的经济分类字段显示</span>
        </el-descriptions-item>
        <el-descriptions-item label="固资-经济分类匹配开关">
          <el-tag :type="configInfo.economicMatchSwitch ? 'primary' : 'info'">{{ configInfo.economicMatchSwitch ? '打开' : '关闭' }}</el-tag>
          <span> 固资系统回传6大类代码为空时，会根据开关自动进行匹配</span>
        </el-descriptions-item>
        <el-descriptions-item label="表单-经费卡内嵌规则">
          <el-tag :type="configInfo.ppRelated ? 'primary' : 'info'">{{ configInfo.ppRelated ? '打开' : '关闭' }}</el-tag>
          <span> 打开后经费卡的使用会根据配置的经费卡规则进行逻辑控制，如控制某些经费卡只针对某些人购买某些商品</span>
        </el-descriptions-item>
        <el-descriptions-item label="表单-公告提示">
          <div v-if="configInfo.notice" v-html="configInfo.notice"></div>
          <div v-else>无</div>
        </el-descriptions-item>
        <el-descriptions-item label="表单-项目说明">
          <div v-if="configInfo.notice2" v-html="configInfo.notice2"></div>
          <div v-else>无</div>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <el-form v-if="formMode === 'edit'" ref="configForm" :model="configForm" :rules="configRules"
      label-width="230px" style="width:900px">
      <el-form-item label="结算类接口地址" prop="settlementHost">
        <el-input v-model="configForm.settlementHost" maxlength="200" placeholder="请输入结算类接口地址" />
      </el-form-item>
      <el-form-item label="审批流类接口地址" prop="approveHost">
        <el-input v-model="configForm.approveHost" maxlength="200" placeholder="请输入审批流类接口地址" />
      </el-form-item>
      <el-form-item label="appKey(由业财融合提供)" prop="appKey">
        <el-input v-model="configForm.appKey" maxlength="50" placeholder="请输入appKey" />
      </el-form-item>
      <el-form-item label="appSecret(由业财融合提供)" prop="appSecret">
        <el-input v-model="configForm.appSecret" maxlength="100" placeholder="请输入appSecret" />
      </el-form-item>
      <!-- <el-form-item label="调试模式" prop="testSwitch">
        <el-switch v-model="configForm.testSwitch" active-text="打开" inactive-text="关闭" />
      </el-form-item> -->
      <el-row>
        <el-col :span="12">
          <el-form-item label="表单-验收人开关" prop="formAcceptorSwitch">
            <el-switch v-model="configForm.formAcceptorSwitch" active-text="打开" inactive-text="关闭" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="表单-经济分类开关" prop="formEconomyClassSwitch">
            <el-switch v-model="configForm.formEconomyClassSwitch" active-text="打开" inactive-text="关闭" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="固资-经济分类匹配开关" prop="economicMatchSwitch">
            <el-switch v-model="configForm.economicMatchSwitch" active-text="打开" inactive-text="关闭" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="表单-经费卡内嵌规则开关" prop="ppRelated">
            <el-switch v-model="configForm.ppRelated" active-text="打开" inactive-text="关闭" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="表单-公告提示" prop="notice">
        <el-input type="textarea" :rows="6" maxlength="600" show-word-limit v-model="configForm.notice" placeholder="请输入公告提示" />
      </el-form-item>
      <el-form-item label="表单-项目说明" prop="notice2">
        <el-input type="textarea" :rows="6" maxlength="600" show-word-limit v-model="configForm.notice2" placeholder="请输入项目说明" />
      </el-form-item>
      <el-form-item>
        <el-button type="default" @click="cancelForm">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">提 交</el-button>
      </el-form-item>
    </el-form>
    <!-- 接口健康检测组件 -->
    <ApiHealthCheck ref="healthCheck" />
  </div>
</template>

<script>
import * as api from "@/api/mall/config/ycrhConfig"
import * as bpmApi from "@/api/mall/config/bpmConfig"
import ApiHealthCheck from "./api-health-check"
export default {
  name: "MallYcrhConfig",
  components: { ApiHealthCheck },
  data() {
    return {
      bpmConfigInfo: {},
      configInfo: {},
      formMode: 'init',
      submitLoading: false,
      configForm: {
        id: undefined,
        settlementHost: '',
        approveHost: '',
        appKey: '',
        appSecret: '',
        testSwitch: true,
        formAcceptorSwitch: false,
        formEconomyClassSwitch: false,
        economicMatchSwitch: false,
        notice: null,
        notice2: null,
        ppRelated: false
      },
      configRules: {
        settlementHost: [
          { required: true, trigger: 'blur', message: '请输入结算类接口地址' },
          { type: 'url', trigger: 'blur', message: '接口地址格式不正确' }
        ],
        approveHost: [
          // { required: true, trigger: 'blur', message: '请输入审批流类接口地址' },
          { type: 'url', trigger: 'blur', message: '接口地址格式不正确' }
        ],
        appKey: [
          { required: true, trigger: 'blur', message: '请输入appKey' }
        ],
        appSecret: [
          { required: true, trigger: 'blur', message: '请输入appSecret' }
        ]
      },
    }
  },
  created() {
    this.loadConfig()
    this.loadBpmConfig()
  },
  methods: {
    beginConfig() {
      this.formMode = 'edit'
    },
    async loadBpmConfig() {
      let res = await bpmApi.getBpmConfig();
      this.bpmConfigInfo = res.data || {}
    },
    async loadConfig() {
      let res = await api.getYcrhConfig();
      this.configInfo = res.data || {}
    },
    editConfig() {
      this.formMode = 'edit'
      Object.assign(this.configForm, this.configInfo)
    },
    cancelForm() {
      this.formMode = 'init'
    },
    async doSubmit() {
      this.submitLoading = true
      try {
        if (this.configForm.id !== undefined) {
          await api.updateYcrhConfig(this.configForm)
          this.$modal.msgSuccess("修改成功");
        } else {
          await api.createYcrhConfig(this.configForm)
          this.$modal.msgSuccess("保存成功");
        }
        this.submitLoading = false
        this.formMode = 'init'
        this.loadConfig()
      } catch(e) {
        this.$modal.msgError("保存失败，请稍后重试")
        this.submitLoading = false
      }
    },
    submitForm() {
      this.$refs["configForm"].validate(valid => {
        if (!valid) {
          return;
        }
        this.doSubmit()
      });
    },
    showHealthCheck() {
      this.$refs.healthCheck.show()
    }
  }
}
</script>

<style lang="scss" scoped></style>
