<template>
  <div class="app-container">
    <div class="left">
      <el-tree ref="tree" :accordion="accordion" :default-expanded-keys="treeData" :node-key="nodeKey" @node-click="handleNodeClick" :props="props" :load="loadNode" lazy></el-tree>
    </div>

    <div class="right">
      <!-- 列表 -->
      <el-table v-loading="loading" :data="list" border>
        <el-table-column label="分类ID" align="center" prop="categoryId" />
        <el-table-column label="分类名称" align="center" prop="categoryName" show-overflow-tooltip></el-table-column>
        <el-table-column label="排序" align="center" prop="orderSort" sortable />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template v-slot="scope">
            <el-button :disabled="scope.row.categoryLevel === 2" size="medium" type="text" @click="handleView(scope.row)">查看下级</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </div>
  </div>
</template>

<script>
import { getCategoryRootList, getCategoryChildTreeList } from "@/api/mall/product/category.js"

export default {
  name: "ProductCategory",
  data() {
    return {
      nodeKey: 'categoryId',
      accordion: true, // 每次只打开一个同级树节点展开
      props: {
        label: 'categoryName',
        children: 'childCategoryList',
        isLeaf: 'leaf',
        id: 'categoryId'
      },
      treeData: [],
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      totalList: [],
      list: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10
      }
    };
  },
  created() {
    this.getTotalList(0);
  },
  methods: {
    handleNodeClick(data) {
      if (data.categoryLevel !== 2) {
        this.getTotalList(data.categoryId)
      }
    },
    loadNode(node, resolve) {
      if (node.level === 0) {
        resolve([{categoryName: '全部分类', categoryId: 0}])
        this.treeData.push(0)
      } else if (node.level === 1) {
        this.getListCategory().then(res => {
          resolve(res)
        })
      } else {
        this.getChildrenCategory(node.data.categoryId).then(res => {
          const level = node.level
          resolve(res.filter(x => x.categoryLevel === level - 1))
        })
      }
    },
    /** 查询分类列表 */
    async getListCategory() {
      // 执行查询
      const res = await getCategoryRootList()
      if (res.code === 0) {
        return res.data
      }
      this.$message.error(res.msg)
      return []
    },
    async getChildrenCategory(id) {
      const res = await getCategoryChildTreeList({
        parentCategoryId: id
      })
      if (res.code === 0) {
        return res.data.map(x => {
          if (x.childCategoryList) {
            x.childCategoryList.map(y => {
              y.leaf = !y.childCategoryList || y.childCategoryList.length === 0
              return y
            })
          }
          x.leaf = !x.childCategoryList || x.childCategoryList.length === 0
          return x
        })
      }
      this.$message.error(res.msg)
      return []
    },
    /** 查询列表 */
    async getTotalList(id) {
      this.loading = true;
      let res = null
      if (id === 0) {
        res = await getCategoryRootList()
      } else {
        res = await getCategoryChildTreeList({
          parentCategoryId: id
        })
      }
      if (res.code === 0) {
        this.totalList = res.data || []
        this.getList()
        this.total = Number(res.data ? res.data.length : 0)
      } else {
        this.$message.error(res.msg)
      }
      this.loading = false
    },
    handleView(row) {
      this.treeData.push(row.categoryId)
      this.getTotalList(row.categoryId)
    },
    getList() {
      this.list = this.totalList.slice((this.queryParams.pageNum - 1) * this.queryParams.pageSize, this.queryParams.pageNum * this.queryParams.pageSize)
    }
  }
};
</script>
<style lang="scss" scoped>
.app-container {
  display: flex;
  position: relative;
  background-color: #fff;
  .left {
    width: 300px;
    max-height: 650px;
    border: 1px solid #e9e4e4;
    background-color: #fafafa;
    overflow: auto;
    padding: 16px;
    .el-tree {
      background-color: #fafafa!important;
    }
  }
  .right {
    margin-left: 24px;
    flex: 1;
    max-height: 700px;
  }
}
</style>