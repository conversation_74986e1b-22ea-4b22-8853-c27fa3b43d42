import request from '@/utils/request'

// 创建商品 SPU
export function createSpu(data) {
  return request({
    url: '/product/spu/create',
    method: 'post',
    data: data
  })
}

// 更新商品 SPU
export function updateSpu(data) {
  return request({
    url: '/product/spu/update',
    method: 'put',
    data: data
  })
}

// 更新商品 SPU 状态
export function updateSpuStatus(data) {
  return request({
    url: '/product/spu/update-status',
    method: 'post',
    data: data
  })
}

// 删除商品 SPU
export function deleteSpu(id) {
  return request({
    url: '/product/spu/delete?id=' + id,
    method: 'delete'
  })
}

// 获得商品 SPU 详情
export function getSpuDetail(id) {
  return request({
    url: '/product/spu/get-detail?id=' + id,
    method: 'get'
  })
}

// 获得商品 SPU 分页
export function getSpuPage(query) {
  return request({
    url: '/product/spu/page',
    method: 'get',
    params: query
  })
}

// 获得商品 SPU 精简列表
export function getSpuSimpleList() {
  return request({
    url: '/product/spu/get-simple-list',
    method: 'get',
  })
}

// 获得启动状态的供应商列表
export function getSuppliers(param) {
  return request({
    url: '/mall/supplier/list-all',
    method: 'get',
    params: param
  })
}

// 获得供应商精简列表
export function getSimpleSuppliers(params) {
  return request({
    url: '/trade/order/simple-supplier-list',
    method: 'get',
    params: params
  })
}

// 获得商品品牌列表
export function getProductBrands(param) {
  return request({
    url: '/product/brand/list',
    method: 'get',
    params: param
  })
}

// 创建商品品牌列表
export function createBrandByName(data) {
  return request({
    url: '/product/brand/create-by-name',
    method: 'post',
    data: data
  })
}

// 获得商品分类树一级列表
export function getProductCategoryRoots(param) {
  return request({
    url: '/product/category/list-root',
    method: 'get',
    params: param
  })
}

// 获得商品分类树下级树形列表
export function getProductCategoryChildrenTree(param) {
  return request({
    url: '/product/category/list-children-tree',
    method: 'get',
    params: param
  })
}

// 获得商品分类的内置规格列表
export function getProductCategorySpecs(param) {
  return request({
    url: '/product/config/category-spec/list-by-category',
    method: 'get',
    params: param
  })
}

// 获得商品操作日志记录分页
export function getOperateLogPage(query) {
  return request({
    url: '/product/operate-log/page',
    method: 'get',
    params: query
  })
}

// 更新商品显示状态
export function updateShowStatus(data) {
  return request({
    url: '/product/spu/update-show-status',
    method: 'post',
    data: data
  })
}

// 更新商品显示状态
export function updatePlatformStatus(data) {
  return request({
    url: '/product/spu/update-platform-status',
    method: 'post',
    data: data
  })
}