import request from '@/utils/request'

// 创建社交消息模版
export function createSocialMessageTemplate(data) {
  return request({
    url: '/system/social-message-template/create',
    method: 'post',
    data: data
  })
}

// 更新社交消息模版
export function updateSocialMessageTemplate(data) {
  return request({
    url: '/system/social-message-template/update',
    method: 'put',
    data: data
  })
}

// 删除社交消息模版
export function deleteSocialMessageTemplate(id) {
  return request({
    url: '/system/social-message-template/delete?id=' + id,
    method: 'delete'
  })
}

// 获得社交消息模版
export function getSocialMessageTemplate(id) {
  return request({
    url: '/system/social-message-template/get?id=' + id,
    method: 'get'
  })
}

// 获得社交消息模版分页
export function getSocialMessageTemplatePage(query) {
  return request({
    url: '/system/social-message-template/page',
    method: 'get',
    params: query
  })
}

// 导出社交消息模版 Excel
export function exportSocialMessageTemplateExcel(query) {
  return request({
    url: '/system/social-message-template/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
