<template>
    <div class="app-container">
      <el-form :model="queryParams" ref="queryForm" class="order-form" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="员工编号">
          <el-input v-model="queryParams.userNo" clearable></el-input>
        </el-form-item>
        <el-form-item label="昵称">
          <el-input v-model="queryParams.nickname" clearable></el-input>
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="queryParams.mobile" clearable></el-input>
        </el-form-item>
        <el-form-item label="部门">
          <dept-select v-model="queryParams.deptId" :show-count="true" :clearable="true" placeholder="请选择归属部门" style="width:193px;"/>
        </el-form-item>
        <br>
        <el-form-item label="登录时间" prop="loginDate">
          <el-date-picker v-model="queryParams.loginDate" value-format="timestamp" type="daterange"
            range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']" />
        </el-form-item>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
      </el-form>
  
      <!-- 操作工具栏 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                      v-hasPermi="['member:user:create']">新增</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
  
      <el-table v-loading="loading" :data="list" border>
        <el-table-column label="用户编号" align="center" prop="id"></el-table-column>
        <el-table-column label="昵称" align="center" prop="nickname"></el-table-column>
        <el-table-column label="工号" align="center" prop="userNo"></el-table-column>
        <!-- <el-table-column label="部门编码" align="center" prop="deptCode"></el-table-column> -->
        <el-table-column label="部门名称" align="center" prop="deptName"></el-table-column>
        <el-table-column label="员工姓名" align="center" prop="name"></el-table-column>
        <el-table-column label="手机号" align="center" prop="mobile"></el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template v-slot="scope">
            <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status"/>
          </template>
        </el-table-column>
        <el-table-column label="注册IP" align="center" prop="registerIp"></el-table-column>
        <el-table-column label="最近登录IP" align="center" prop="loginIp"></el-table-column>
        <el-table-column label="最近登录时间" align="center" prop="loginDate">
          <template v-slot="scope">
            <span>{{ parseTime(scope.row.loginDate) }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="center" class-name="small-padding fixed-width">
          <template v-slot="scope">
            <el-button size="medium" type="text" icon="el-icon-view" @click="goToDetail(scope.row)"
                          v-hasPermi="['member:user:query']">详情</el-button>
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                         v-hasPermi="['member:user:update']">修改</el-button>
          </template>
        </el-table-column>
      </el-table>
  
      <!-- 分页组件 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
        @pagination="getList" />

      <!-- 添加或修改参数配置对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="员工编号" prop="userNo">
                <el-input v-model="form.userNo" placeholder="请输入用户编号" :maxLength="30" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="员工姓名" prop="name">
                <el-input v-model="form.name" placeholder="请输入用户姓名" :maxLength="30"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="用户昵称" prop="nickname">
                <el-input v-model="form.nickname" placeholder="请输入用户昵称" :maxLength="50" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="手机号" prop="mobile">
                <el-input v-model="form.mobile" placeholder="请输入手机号" :maxLength="20"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="部门" prop="deptId">
                <dept-select v-model="form.deptId" :show-count="true" :clearable="false" placeholder="请选择归属部门" @select="handleSelectDept" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="部门编码" prop="deptCode">
                <el-input v-model="form.deptCode" placeholder="请输入部门编码" :maxLength="30" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="部门名称" prop="deptName">
                <el-input v-model="form.deptName" placeholder="请输入部门名称" :maxLength="50"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="职位名称" prop="jobTitle">
                <el-input v-model="form.jobTitle" placeholder="请输入职务名称" :maxLength="50" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="人员类型" prop="userType">
                <el-input v-model="form.userType" placeholder="请输入人员类型" :maxLength="50"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="用户密码" prop="password">
                <el-input v-model="form.password" placeholder="请输入用户密码" type="password" :maxLength="30" show-password />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-radio-group v-model="form.status">
                  <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                            :key="dict.value" :label="parseInt(dict.value)">{{dict.label}}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" :loading="submitLoading" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </template>
    
  <script>
  import { getUserPage, getDetail, createMemberUser, updateMemberUser } from "@/api/mall/member/member.js"
  import DeptSelect from '@/views/mall/member/components/dept-select'
  export default {
    name: "MemberList",
    components: { DeptSelect },
    data () {
      const validatePassword = (rule, value, callback) => {
        // let regExp = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,30}$/
        let regExp = /^.{6,30}$/
         if (value && !regExp.test(value)) {
          // callback(new Error("密码必须包含大小写字母数字且包含@$!%*?&等字符, 长度为8-30位"));
          callback(new Error("密码长度为6-30位"));
        } else {
          callback();
        }
      };
      return {
        // 遮罩层
        loading: false,
        // 显示搜索条件
        showSearch: true,
        // 部门树选项
        deptOptions: undefined,
        // 总条数
        total: 0,
        list: [],
        queryParams: {
          pageNo: 1,
          pageSize: 10,
          userNo: '',
          nickname: '',
          status: '',
          mobile: '',
          deptId: undefined,
          loginDate: []
        },
        title: "",
        open: false,
        form: {},
        submitLoading: false,
        rules: {
          userNo: [
            { required: true, message: "员工编号不能为空", trigger: "blur" }
          ],
          name: [
            { required: true, message: "员工姓名不能为空", trigger: "blur" }
          ],
          nickname: [
            { required: true, message: "用户昵称不能为空", trigger: "blur" }
          ],
          mobile: [
            {
              pattern: /^(?:(?:\+|00)86)?1\d{10}$/,
              message: "请输入正确的手机号码",
              trigger: "blur"
            }
          ],
          status: [
            { required: true, message: "状态不能为空", trigger: "blur" }
          ],
          password: [
            { required: true, message: "密码不能为空", trigger: "blur" },
            { validator: validatePassword, trigger: "blur" }
          ]
        }
      }
    },
    created() {
      this.getList();
    },
    methods: {
      handleSelectDept(node) {
        console.log('node------', node)
        if(node && node.name) {
          this.form.deptName = node.name
          this.form.deptCode = node.code
        }
      },
      /** 查询列表 */
      async getList() {
        this.loading = true;
        const params = this.getParams();
        const res = await getUserPage(params);
        if (res.code === 0 && res.data) {
          this.list = res.data.list;
          this.total = Number(res.data.total);
        } else {
          this.list = []
          this.total = 0
        }
        this.loading = false;
      },
      getParams() {
        const params = {
          pageNo: this.queryParams.pageNo,
          pageSize: this.queryParams.pageSize,
          deptId: this.queryParams.deptId
        }
        if (this.queryParams.userNo) {
          params.userNo = this.queryParams.userNo
        }
        if (this.queryParams.loginDate && this.queryParams.loginDate.length > 0) {
          params.loginDate = this.queryParams.loginDate
        }
        if (this.queryParams.nickname) {
          params.nickname = this.queryParams.nickname
        }
        if (this.queryParams.status !== '') {
          params.status = this.queryParams.status
        }
        if (this.queryParams.mobile !== '') {
          params.mobile = this.queryParams.mobile
        }
        return params
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNo = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParams = {
          pageNo: 1,
          pageSize: 10,
          userNo: '',
          nickname: '',
          status: '',
          mobile: ''
        }
        this.getList();
      },
      goToDetail(row) {
        this.$router.push({ name: 'MemberDetail', query: { id: row.id } })
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          id: undefined,
          userNo: undefined,
          name: undefined,
          nickname: undefined,
          mobile: undefined,
          deptCode: undefined,
          deptName: undefined,
          deptId: undefined,
          jobTitle: undefined,
          userType: undefined,
          avatar: '',
          password: undefined,
          status: 0
        };
        this.resetForm("form");
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.rules.password[0].required = true
        this.title = "添加会员";
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        const id = row.id;
        getDetail({id:id}).then(response => {
          response.data.password = undefined
          this.form = response.data;
          this.rules.password[0].required = false
          this.open = true;
          this.title = "修改会员";
        });
      },
      /** 提交按钮 */
      submitForm: function() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            this.submitLoading = true
            if (this.form.id !== undefined) {
              updateMemberUser(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }).finally(() => {
                this.submitLoading = false
              });
            } else {
              createMemberUser(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }).finally(() => {
                this.submitLoading = false
              });
            }
          }
        });
      }
    }
  }
  </script>
    
  <style lang="scss" scoped>
  </style>
    