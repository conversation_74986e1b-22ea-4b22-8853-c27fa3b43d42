import request from '@/utils/request'

// 搜索商品
export function searchJDGoodsPageList(params) {
  for (const key in params) {
    if (params[key] === '') {
      delete params[key]
    }
  }
  return request({
    url: '/product/vopgoods/goodsSearchPageList',
    method: 'get',
    params
  })
}

// 查询单个商品的详细信息，包含商品售卖单位、重量、产地、包装清单、主图、规格参数等
export function getJDSkuDetailInfo(params) {
  return request({
    url: '/product/vopgoods/getSkuDetailInfo',
    method: 'get',
    params
  })
}

// 查询商品图片，包含商品主图、图片路径等
export function getJDSkuImageList(params) {
  return request({
    url: '/product/vopgoods/getSkuImageList',
    method: 'get',
    params
  })
}

// 查询商品上架、详情图片、可售、权益等
export function getJDAssociationSkuDetailInfo(params) {
  return request({
    url: '/product/vopgoods/getAssociationSkuDetailInfo',
    method: 'get',
    params
  })
}

// 查询商品库存状态等
export function queryJDGoodsStockInfo(params) {
  return request({
    url: '/product/vopgoods/queryGoodsStockInfo',
    method: 'get',
    params
  })
}


// 查询商品SKU列表
export function getSkuListBySpu(params) {
  return request({
    url: '/product/sku/list-by-spu',
    method: 'get',
    params
  })
}

// 更新商品 SKU 价格
export function updateSkuPrice(data) {
  return request({
    url: '/product/sku/update-price',
    method: 'post',
    data: data
  })
}

// 更新商品 SKU 库存
export function updateSkuStock(data) {
  return request({
    url: '/product/sku/update-stock',
    method: 'post',
    data: data
  })
}

// 搜索商品SKU
export function getSkuPage2(params) {
  return request({
    url: '/product/sku/page',
    method: 'get',
    params
  })
}

// 查询商品SKU详情
export function getSkuDetail2(params) {
  return request({
    url: '/product/sku/get-detail',
    method: 'get',
    params
  })
}

// 更新商品 SPU 状态
export function updateSkuStatus(data) {
  return request({
    url: '/product/sku/update-status',
    method: 'post',
    data: data
  })
}

// 更新商品SKU显示状态
export function updateShowStatus(data) {
  return request({
    url: '/product/sku/update-show-status',
    method: 'post',
    data: data
  })
}

// 更新商品SKU平台状态
export function updatePlatformStatus(data) {
  return request({
    url: '/product/sku/update-platform-status',
    method: 'post',
    data: data
  })
}

// 搜索SEO商品SKU
export function getSeoSkuPage(params) {
  return request({
    url: '/product/sku/seo-page',
    method: 'get',
    params
  })
}

// 下载导入模板
export function getImportTemplate() {
  return request({
    url: '/product/sku/seo/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}

// 下载导入模板
export function getTagImportTemplate() {
  return request({
    url: '/product/sku/tag/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}

// 修改商品SEO字段
export function updateSeoSku(data) {
  return request({
    url: '/product/sku/seo/update',
    method: 'post',
    data
  })
}

// 重置商品SEO状态
export function resetSkuSeoStatus(data) {
  return request({
    url: '/product/sku/seo/reset',
    method: 'post',
    data
  })
}

// 保存商品SEO状态
export function addSkuSeoStatus(data) {
  return request({
    url: '/product/sku/seo/save',
    method: 'post',
    data
  })
}

// 运营商品刷新订单标签
export function refreshOrderTags(data) {
  return request({
    url: '/product/sku/seo/refresh-order-tags',
    method: 'post',
    data
  })
}

// 保存商品标签
export function addSkuTags(data) {
  return request({
    url: '/product/sku/add-tags',
    method: 'post',
    data
  })
}

// 清除商品标签
export function clearSkuTags(data) {
  return request({
    url: '/product/sku/clear-tags',
    method: 'post',
    data
  })
}

// 导出商品SKU
export function exportSkuList(params) {
  return request({
    url: '/product/sku/export',
    // responseType: 'blob',
    method: 'get',
    params
  })
}

// 导出SEO商品SKU
export function exportSeoSkuList(params) {
  return request({
    url: '/product/sku/seo/export',
    // responseType: 'blob',
    method: 'get',
    params
  })
}

// 批量商品SKU索引状态
export function querySkuIndexStatus(params) {
  return request({
    url: '/product/sku/batch-query-index-status',
    method: 'post',
    data: params
  })
}
