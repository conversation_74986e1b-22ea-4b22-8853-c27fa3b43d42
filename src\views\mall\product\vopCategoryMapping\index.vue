<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="平台ID" prop="lastCategoryId">
        <el-input v-model="queryParams.lastCategoryId" placeholder="请输入末级分类ID" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="平台名称" prop="lastCategoryName">
        <el-input v-model="queryParams.lastCategoryName" placeholder="请输入末级分类名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="vopID" prop="vopLastCategoryId">
        <el-input v-model="queryParams.vopLastCategoryId" placeholder="请输入vop末级分类ID" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="vop名称" prop="vopLastCategoryName">
        <el-input v-model="queryParams.vopLastCategoryName" placeholder="请输入vop末级分类名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleMapping"
                   v-hasPermi="['mall:vop-category-mapping:create']">映射</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['mall:vop-category-mapping:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['mall:vop-category-mapping:export']">导出</el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

        <CategoryMappingSelect :open.sync="categoryOpen" @refresh="getList"/>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="平台分类ID" align="center" prop="lastCategoryId" />
      <el-table-column label="平台分类名称" align="center" prop="lastCategoryName" />
      <el-table-column label="完整分类id" align="center" prop="fullCategoryId" />
      <el-table-column label="完整分类名称" align="center" prop="fullCategoryName" />
      <el-table-column label="vop末级分类ID" align="center" prop="vopLastCategoryId" />
      <el-table-column label="vop末级分类名称" align="center" prop="vopLastCategoryName" />
      <el-table-column label="vop完整分类id" align="center" prop="vopFullCategoryId" />
      <el-table-column label="vop完整分类名称" align="center" prop="vopFullCategoryName" />
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['mall:vop-category-mapping:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['mall:vop-category-mapping:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="500px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="末级分类ID" prop="lastCategoryId">
          <el-input v-model="form.lastCategoryId" placeholder="请输入末级分类ID" />
        </el-form-item>
        <el-form-item label="末级分类名称" prop="lastCategoryName">
          <el-input v-model="form.lastCategoryName" placeholder="请输入末级分类名称" />
        </el-form-item>
        <el-form-item label="完整分类ID" prop="fullCategoryId">
          <el-input v-model="form.fullCategoryId" placeholder="请输入完整分类ID" />
        </el-form-item>
        <el-form-item label="完整分类名称" prop="fullCategoryName">
          <el-input v-model="form.fullCategoryName" placeholder="请输入完整分类名称" />
        </el-form-item>
        <el-form-item label="vop末级分类ID" prop="vopLastCategoryId">
          <el-input v-model="form.vopLastCategoryId" placeholder="请输入vop末级分类ID" />
        </el-form-item>
        <el-form-item label="vop末级分类名称" prop="vopLastCategoryName">
          <el-input v-model="form.vopLastCategoryName" placeholder="请输入vop末级分类名称" />
        </el-form-item>
        <el-form-item label="vop完整分类ID" prop="vopFullCategoryId">
          <el-input v-model="form.vopFullCategoryId" placeholder="请输入vop完整分类ID" />
        </el-form-item>
        <el-form-item label="vop完整分类名称" prop="vopFullCategoryName">
          <el-input v-model="form.vopFullCategoryName" placeholder="请输入vop完整分类名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createVopCategoryMapping, updateVopCategoryMapping, deleteVopCategoryMapping, getVopCategoryMapping, getVopCategoryMappingPage, exportVopCategoryMappingExcel } from "@/api/mall/product/vopCategoryMapping";
import CategoryDialog from "@/views/mall/product/spu/components/category-dialog";
import CategoryMappingSelect from "./components/category-mapping-select";

export default {
  name: "VopCategoryMapping",
  components: {
    CategoryDialog,
    CategoryMappingSelect,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 选择商品分类对话框
      categoryOpen: false,
      // 总条数
      total: 0,
      // 京东分类映射列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        lastCategoryId: null,
        lastCategoryName: null,
        fullCategoryId: null,
        fullCategoryName: null,
        vopLastCategoryId: null,
        vopLastCategoryName: null,
        vopFullCategoryId: null,
        vopFullCategoryName: null,
        createTime: [],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        lastCategoryId: [{ required: true, message: "末级分类ID不能为空", trigger: "blur" }],
        lastCategoryName: [{ required: true, message: "末级分类名称不能为空", trigger: "blur" }],
        fullCategoryId: [{ required: true, message: "完整分类id不能为空", trigger: "blur" }],
        vopLastCategoryId: [{ required: true, message: "vop末级分类ID不能为空", trigger: "blur" }],
        vopLastCategoryName: [{ required: true, message: "vop末级分类名称不能为空", trigger: "blur" }],
        vopFullCategoryId: [{ required: true, message: "vop完整分类id不能为空", trigger: "blur" }],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getVopCategoryMappingPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    refresh() {
      this.getList();
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.categoryOpen = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        lastCategoryId: undefined,
        lastCategoryName: undefined,
        fullCategoryId: undefined,
        fullCategoryName: undefined,
        vopLastCategoryId: undefined,
        vopLastCategoryName: undefined,
        vopFullCategoryId: undefined,
        vopFullCategoryName: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // handleCategoryChange(val, pathInfo) {
    //   console.log('category-change: ', val, pathInfo)
    //   this.resetCategoryIdNames()
    //   this.form.fullCategoryId = val.join("-")
    //   this.form.fullCategoryName = pathInfo.labels.join("/")
    //   if(val && val.length) {
    //     if(val.length >= 1) {
    //       this.form.categoryId = pathInfo.ids[pathInfo.ids.length - 1]
    //       this.form.categoryName = pathInfo.labels[pathInfo.labels.length - 1]
    //     }
    //   }
    // },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加京东分类映射";
    },
    /** 映射按钮操作 */
    handleMapping() {
      this.categoryOpen = true;
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getVopCategoryMapping(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改京东分类映射";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.id != null) {
          updateVopCategoryMapping(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createVopCategoryMapping(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除京东分类映射编号为"' + id + '"的数据项?').then(function() {
          return deleteVopCategoryMapping(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有京东分类映射数据项?').then(() => {
          this.exportLoading = true;
          return exportVopCategoryMappingExcel(params);
        }).then(response => {
          this.$download.excel(response, '京东分类映射.xls');
          this.exportLoading = false;
        }).catch(() => {});
    }
  }
};
</script>
