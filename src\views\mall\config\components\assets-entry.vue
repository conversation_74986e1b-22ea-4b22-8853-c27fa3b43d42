<template>
  <div>
    <div>
      <div style="margin: 15px 0;font-size:1.2em;">入口列表
        <el-button style="margin-left: 10px" size="small" type="primary" @click="handleAdd">添加入口</el-button>
      </div>
      <el-table :data="list" style="width: 85%">
        <el-table-column prop="name" label="名称"></el-table-column>
        <el-table-column prop="url" label="链接" show-overflow-tooltip width="350"></el-table-column>
        <el-table-column prop="memo" label="备注" show-overflow-tooltip></el-table-column>
        <el-table-column prop="expression" label="显示条件" show-overflow-tooltip></el-table-column>
        <el-table-column prop="status" label="状态">
          <template v-slot="scope">
            <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status"/>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime">
          <template v-slot="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding">
          <template v-slot="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="750px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="90px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" :maxlength="50"/>
        </el-form-item>
        <el-form-item label="地址" prop="url">
          <span>支持变量: acceptNo-验收单号 orderNo-订单号 orderItemId-订单明细ID 如:{orderNo}</span>
          <el-input v-model="form.url" :maxlength="200"/>
        </el-form-item>
        <el-form-item label="显示条件" prop="expression">
          <span>支持变量: skuPrice count skuTotalPrice extCategoryCode</span>
          <el-input v-model="form.expression" type="textarea" :autosize="{ minRows: 4, maxRows: 6}" :maxlength="200" show-word-limit placeholder="请输入条件表达式" />
        </el-form-item>
        <el-form-item label="备注" prop="memo">
          <el-input v-model="form.memo" :maxlength="200"/>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
              <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                        :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
              </el-radio>
            </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as api from "@/api/mall/config/assetsConfig";
export default {
  name: "MallAssetsEntry",
  props: {
    configId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      dialogOpen: false,
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 品牌列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [{required: true, message: "名称不能为空", trigger: "blur"}],
        url: [{required: true, message: "地址不能为空", trigger: "blur"}]
      }
    }
  },
  mounted() {
    this.handleQuery()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      this.queryParams.configId = this.configId
      // 执行查询
      api.getAssetsEntryList(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = parseInt(response.data.total);
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        configId: this.configId,
        name: undefined,
        memo: undefined,
        url: undefined,
        expression: undefined,
        status: 0
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 修改按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增入口";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      api.getAssetsEntry(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改入口";
      });
    },
    handleClose() {
      this.dialogOpen = false
      this.updateState()
    },
    updateState() {
      this.$emit('update')
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.id != null) {
          api.updateAssetsEntry(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        } else {
          api.createAssetsEntry(this.form).then(response => {
            this.$modal.msgSuccess("保存成功");
            this.open = false;
            this.getList();
          });
          return;
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除名称为"' + row.name + '"的数据项?').then(function () {
        return api.deleteAssetsEntry(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    }
  }
}
</script>