<template>
  <div>
    <treeselect :value="selectedVal" 
          :options="options" 
          :show-count="true" 
          clearable
          placeholder="请选择归属部门" 
          :normalizer="normalizer"
          @input="handleInput"
          @select="handleSelectDept"
          @deselect="handleDeselectDept"
          style="width:100%;margin-right:20px;"/>
  </div>
</template>

<script>
import {listSimpleDepts} from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
export default {
  name: 'MemberDeptSelect',
  components: {Treeselect},
  props: {
    value: {
      type: [String, Number, Array],
      default: () => null 
    },
    placeholder: {
      type: String,
      default:() => '请输入部门名称搜索'
    }
  },
  data() {
    return {
      options: []
    }
  },
  computed: {
    selectedVal() {
      return this.value
    }
  },
  created() {
    this.loadOptions()
  },
  methods: {
    handleDeselectDept(node) {
      console.log('deselect.......', node)
    },
    handleInput(val) {
      console.log('input.......', val)
      this.$emit("input", val)
      if(!val) {
        this.$emit('select', null)
      }
    },
    handleSelectDept(node) {
      console.log('select.......', node)
      this.$emit('select', node)
      this.$emit("input", node.id);
    },
    // 格式化部门的下拉框
    normalizer(node) {
      return {
        id: node.id,
        label: node.name,
        children: node.children
      }
    },
    async loadOptions(keyword) {
      let res = await listSimpleDepts()
      let treeNodes = this.handleTree(res.data, "id")
      this.options = [];
      this.options.push(...treeNodes);
    }
  }
}
</script>

<style>

</style>