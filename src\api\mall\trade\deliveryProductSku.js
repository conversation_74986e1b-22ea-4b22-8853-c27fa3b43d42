import request from '@/utils/request'

// 创建物流订单商品sku关系
export function createDeliveryProductSku(data) {
  return request({
    url: '/trade/delivery-product-sku/create',
    method: 'post',
    data: data
  })
}

// 更新物流订单商品sku关系
export function updateDeliveryProductSku(data) {
  return request({
    url: '/trade/delivery-product-sku/update',
    method: 'put',
    data: data
  })
}

// 删除物流订单商品sku关系
export function deleteDeliveryProductSku(id) {
  return request({
    url: '/trade/delivery-product-sku/delete?id=' + id,
    method: 'delete'
  })
}

// 获得物流订单商品sku关系
export function getDeliveryProductSku(id) {
  return request({
    url: '/trade/delivery-product-sku/get?id=' + id,
    method: 'get'
  })
}

// 获得物流订单商品sku关系分页
export function getDeliveryProductSkuPage(query) {
  return request({
    url: '/trade/delivery-product-sku/page',
    method: 'get',
    params: query
  })
}

// 导出物流订单商品sku关系 Excel
export function exportDeliveryProductSkuExcel(query) {
  return request({
    url: '/trade/delivery-product-sku/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
