<template>
  <div class="app-container">
    <el-empty v-if="!configInfo.id && formMode === 'init'">
      <el-button type="text" icon="el-icon-plus" @click="beginConfig">开始配置</el-button>
    </el-empty>
    <div v-if="configInfo.id && formMode === 'init'">
      <el-descriptions title="配置信息" :column="1" :border="true">
        <template slot="extra">
          <el-button type="primary" size="small" @click="editConfig">修改</el-button>
        </template>
        <el-descriptions-item label="SSO类型"><dict-tag :type="DICT_TYPE.MALL_SSO_TYPE" :value="configInfo.ssoType" /></el-descriptions-item>
        
        <template v-if="configInfo.ssoType === 1">
        <el-descriptions-item label="token校验地址">{{ configInfo.tokenCheckUrl || '--' }}</el-descriptions-item>
        <el-descriptions-item label="登录地址">{{ configInfo.loginUrl || '--' }}</el-descriptions-item>
        <el-descriptions-item label="业财融合SSO登录入口">{{ ssoAuthCallbackUrl || '--' }}</el-descriptions-item>
        </template>

        <template v-if="configInfo.ssoType === 2">
        <el-descriptions-item label="CAS登录入口">
          <span>{{ casAuthUrl || '--' }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="CAS服务端地址前缀">{{ configInfo.casServerUrlPrefix || '--' }}</el-descriptions-item>
        <el-descriptions-item label="CAS服务端登录地址">{{ configInfo.casServerLoginUrl || '--' }}</el-descriptions-item>
        <el-descriptions-item label="CAS服务端退出地址">{{ configInfo.casServerLogoutUrl || '--' }}</el-descriptions-item>
        <el-descriptions-item label="CAS验证类型">{{ configInfo.casValidationType || '--' }}</el-descriptions-item>
        <el-descriptions-item label="PC端地址">{{ configInfo.casClientHostUrl || '--' }}</el-descriptions-item>
        <el-descriptions-item label="PC端跳转">{{ configInfo.casWebMainUrl || '--' }}</el-descriptions-item>
        <el-descriptions-item label="移动端地址">{{ configInfo.casClientHostUrl2 || '--' }}</el-descriptions-item>
        <el-descriptions-item label="移动端跳转">{{ configInfo.casWebMainUrl2 || '--' }}</el-descriptions-item>
        </template>

        <el-descriptions-item label="状态"><dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="configInfo.status" /></el-descriptions-item>
      </el-descriptions>
    </div>
    <el-form v-if="formMode === 'edit'" ref="configForm" :model="configForm" :rules="configRules"
      label-width="200px" style="width:800px">
      <el-form-item label="SSO类型" prop="ssoType">
        <el-radio-group v-model="configForm.ssoType">
          <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.MALL_SSO_TYPE)"
                    :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="configForm.ssoType === 1">
      <el-form-item label="token校验地址" prop="tokenCheckUrl" :rules="[
          { required: true, trigger: 'blur', message: '请输入token校验地址' },
          { type: 'url', trigger: 'blur', message: '校验地址格式不正确' }]">
        <el-input v-model="configForm.tokenCheckUrl" maxlength="200" placeholder="请输入token校验地址" />
      </el-form-item>
      <el-form-item label="登录地址" prop="loginUrl" :rules="[
          { required: true, trigger: 'blur', message: '请输入登录地址' },
          { type: 'url', trigger: 'blur', message: '登录地址格式不正确' }
      ]">
        <el-input v-model="configForm.loginUrl" maxlength="200" placeholder="请输入登录地址" />
      </el-form-item>
    </template>

      <template v-if="configForm.ssoType === 2">
      <el-collapse style="margin:10px 0">
        <el-collapse-item title="点击查看CAS配置参考提示" name="1">
          <div>CAS服务端地址前缀：https://auth.wust.edu.cn/lyuapServer</div>
          <div>CAS服务端登录地址：https://auth.wust.edu.cn/lyuapServer/login</div>
          <div>CAS服务端退出地址：https://auth.wust.edu.cn/lyuapServer/logout</div>
          <div>CAS验证类型：cas3</div>
          <div>PC端端地址：https://wust.jcy360.cn:443/</div>
          <div>PC端跳转地址：https://wust.jcy360.cn:443/#/mall/sso-link</div>
          <div>移动端地址：https://wust.jcy360.cn:443/h5/</div>
          <div>移动端跳转地址：https://wust.jcy360.cn:443/h5#/mall/sso-link</div>
          <div>应用注册地址：https://wust.jcy360.cn 或 https://wust.jcy360.cn:443/app-api/**</div>
        </el-collapse-item>
      </el-collapse>
      <el-form-item label="CAS服务端地址前缀" prop="casServerUrlPrefix" :rules="[
        { required: true, trigger: 'blur', message: '请输入CAS服务端地址前缀' },
        { type: 'url', trigger: 'blur', message: '地址格式不正确' }
      ]">
        <el-input v-model="configForm.casServerUrlPrefix" maxlength="200" placeholder="请输入CAS服务端地址前缀" />
      </el-form-item>
      <el-form-item label="CAS服务端登录地址" prop="casServerLoginUrl" :rules="[
        { required: true, trigger: 'blur', message: '请输入CAS服务端登录地址' },
        { type: 'url', trigger: 'blur', message: '地址格式不正确' }
      ]">
        <el-input v-model="configForm.casServerLoginUrl" maxlength="200" placeholder="请输入CAS服务端登录地址" />
      </el-form-item>
      <el-form-item label="CAS服务端退出地址" prop="casServerLogoutUrl" :rules="[
        { required: true, trigger: 'blur', message: '请输入CAS服务端退出地址' },
        { type: 'url', trigger: 'blur', message: '地址格式不正确' }
      ]">
        <el-input v-model="configForm.casServerLogoutUrl" maxlength="200" placeholder="请输入CAS服务端退出地址" />
      </el-form-item>
      <el-form-item label="CAS验证类型" prop="casValidationType" :rules="[
        { required: true, trigger: 'blur', message: '请输入CAS验证类型' }
      ]">
        <el-input v-model="configForm.casValidationType" maxlength="50" placeholder="请输入CAS验证类型" />
      </el-form-item>
      <el-form-item label="PC端端地址" prop="casClientHostUrl" :rules="[
        { required: true, trigger: 'blur', message: '请输入PC端地址' },
        { type: 'url', trigger: 'blur', message: '地址格式不正确' }
      ]">
        <el-input v-model="configForm.casClientHostUrl" maxlength="200" placeholder="请输入PC端地址" />
      </el-form-item>
      <el-form-item label="PC端跳转地址" prop="casWebMainUrl" :rules="[
        { required: true, trigger: 'blur', message: '请输入PC端跳转地址' },
        { type: 'url', trigger: 'blur', message: '地址格式不正确' }
      ]">
        <el-input v-model="configForm.casWebMainUrl" maxlength="200" placeholder="请输入PC端跳转地址" />
      </el-form-item>
      <el-form-item label="移动端地址" prop="casClientHostUrl2" :rules="[
        { required: true, trigger: 'blur', message: '请输入移动端地址' },
        { type: 'url', trigger: 'blur', message: '地址格式不正确' }
      ]">
        <el-input v-model="configForm.casClientHostUrl2" maxlength="200" placeholder="请输入移动端地址" />
      </el-form-item>
      <el-form-item label="移动端跳转地址" prop="casWebMainUrl2" :rules="[
        { required: true, trigger: 'blur', message: '请输入移动端跳转地址' },
        { type: 'url', trigger: 'blur', message: '地址格式不正确' }
      ]">
        <el-input v-model="configForm.casWebMainUrl2" maxlength="200" placeholder="请输入移动端跳转地址" />
      </el-form-item>
      </template>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="configForm.status">
          <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                    :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button type="default" @click="cancelForm">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">提 交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import * as api from "@/api/mall/config/ssoConfig";
export default {
  name: "MallSsoConfig",
  components: {},
  data() {
    return {
      configInfo: {},
      casAuthUrl: '',
      ssoAuthCallbackUrl: '',
      formMode: 'init',
      submitLoading: false,
      configForm: {
        id: undefined,
        tokenCheckUrl: '',
        loginUrl: '',
        appKey: '',
        appSecret: '',
        status: 0,
        ssoType: 1,
        casServerUrlPrefix: '',
        casServerLoginUrl: '',
        casServerLogoutUrl: '',
        casValidationType: 'cas3',
        casClientHostUrl: '',
        casWebMainUrl: '',
        casClientHostUrl2: '',
        casWebMainUrl2: '',
      },
      configRules: {
        ssoType: [
          { required: true, trigger: 'blur', message: '请选择SSO类型' }
        ],
      },
    }
  },
  created() {
    this.loadConfig()
    this.loadCasAuthUrl()
    this.loadSsoAuthCallbackUrl()
  },
  methods: {
    beginConfig() {
      this.formMode = 'edit'
    },
    async loadCasAuthUrl() {
      let res = await api.getCasAuthUrl()
      this.casAuthUrl = res.data
    },
    async loadSsoAuthCallbackUrl() {
      let res = await api.getSsoAuthCallbackUrl()
      this.ssoAuthCallbackUrl = res.data
    },
    async loadConfig() {
      let res = await api.getSsoConfig();
      this.configInfo = res.data || {}
    },
    editConfig() {
      this.formMode = 'edit'
      Object.assign(this.configForm, this.configInfo)
    },
    cancelForm() {
      this.formMode = 'init'
    },
    async doSubmit() {
      this.submitLoading = true
      try {
        if (this.configForm.id !== undefined) {
          await api.updateSsoConfig(this.configForm)
          this.$modal.msgSuccess("修改成功");
        } else {
          await api.createSsoConfig(this.configForm)
          this.$modal.msgSuccess("保存成功");
        }
        this.submitLoading = false
        this.formMode = 'init'
        this.loadConfig()
      } catch(e) {
        this.$modal.msgError("保存失败，请稍后重试")
        this.submitLoading = false
      }
    },
    submitForm() {
      this.$refs["configForm"].validate(valid => {
        if (!valid) {
          return;
        }
        this.doSubmit()
      });
    }
  }
}
</script>

<style lang="scss" scoped></style>