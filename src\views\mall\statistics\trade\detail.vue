<template>
  <div> 
    <el-tabs v-model="activeName" class="stats-tabs">
      <el-tab-pane label="销售记录" name="first" v-hasPermi="['stats:order-item:query']">
        <StatsOrderItem></StatsOrderItem>
      </el-tab-pane>
      <el-tab-pane label="售后记录" name="second" v-hasPermi="['stats:after-sale:query']">
        <StatsAfterSale></StatsAfterSale>
      </el-tab-pane>
      <el-tab-pane label="销量统计" name="third" v-hasPermi="['stats:sale-date:query']">
        <StatsList></StatsList>
      </el-tab-pane>
    </el-tabs>

  </div>
</template>

<script>
import StatsOrderItem from '@/views/mall/statistics/trade/components/orderItem'
import StatsAfterSale from '@/views/mall/statistics/trade/components/afterSale'
import StatsList from '@/views/mall/statistics/trade/components/statsList'
export default {
  name: 'StatsTradeDetail',
  components: {
    StatsOrderItem, StatsAfterSale, StatsList
  },
  data() {
    return {
      activeName: 'first'
    }
  }
}
</script>

<style lang="scss" scoped>
.stats-tabs {
  margin: 10px 20px 5px;
}
</style>