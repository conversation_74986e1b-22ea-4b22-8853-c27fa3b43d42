import request from '@/utils/request'

// 提交售后单
export function createAfterSale(data) {
  return request({
    url: "/trade/after-sale/create",
    method: "post",
    data,
  });
}

// 获得交易售后
export function getAfterSale(query) {
  return request({
    url: '/trade/after-sale/get',
    method: 'get',
    params: query
  })
}

// 获得交易售后操作日志
export function getAfterSaleLogList(query) {
  return request({
    url: '/trade/after-sale/log-list',
    method: 'get',
    params: query
  })
}

// 获得交易售后分页
export function getAfterSalePage(query) {
  return request({
    url: '/trade/after-sale/page',
    method: 'get',
    params: query
  })
}


// 同意售后申请
export function agreeApply(id) {
  return request({
    url: '/trade/after-sale/agree?id=' + id,
    method: 'put'
  })
}


// 驳回售后申请
export function disagreeApply(data) {
  return request({
    url: '/trade/after-sale/disagree',
    method: 'put',
    data: data
  })
}

// 确认收货
export function receiveDelivery(id) {
  return request({
    url: '/trade/after-sale/receive?id=' + id,
    method: 'put'
  })
}


// 拒绝收货
export function refuseDelivery(data) {
  return request({
    url: '/trade/after-sale/refuse',
    method: 'put',
    data: data
  })
}

// 确认退款
export function refund(id) {
  return request({
    url: '/trade/after-sale/refund?id=' + id,
    method: 'post'
  })
}

// 仅退款
export function refundV2(id) {
  return request({
    url: '/trade/after-sale/refund-v2?id=' + id,
    method: 'post'
  })
}

// 查询所有订单的售后服务组件URL
export function getAllAfterSaleComponentUrl(id) {
  return request({
    url: '/trade/after-sale/getAllAfterSaleComponentUrl',
    method: 'get'
  })
}

// 查询单个订单的售后服务组件URL
export function getAfterSaleComponentUrl(query) {
  return request({
    url: '/trade/after-sale/getAfterSaleComponentUrl',
    method: 'get',
    params: query
  })
}