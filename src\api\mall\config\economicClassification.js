import request from '@/utils/request'

// 创建商城经济分类
export function createEconomicClassification(data) {
  return request({
    url: '/mall/economic-classification/create',
    method: 'post',
    data: data
  })
}

// 更新商城经济分类
export function updateEconomicClassification(data) {
  return request({
    url: '/mall/economic-classification/update',
    method: 'post',
    data: data
  })
}

// 删除商城经济分类
export function deleteEconomicClassification(id) {
  return request({
    url: '/mall/economic-classification/delete?id=' + id,
    method: 'post'
  })
}

// 获得商城经济分类
export function getEconomicClassification(id) {
  return request({
    url: '/mall/economic-classification/get?id=' + id,
    method: 'get'
  })
}

// 获得商城经济分类分页
export function getEconomicClassificationPage(query) {
  return request({
    url: '/mall/economic-classification/page',
    method: 'get',
    params: query
  })
}

// 导出商城经济分类 Excel
export function exportEconomicClassificationExcel(query) {
  return request({
    url: '/mall/economic-classification/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
