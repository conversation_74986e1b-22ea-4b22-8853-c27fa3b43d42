<template>
  <div class="app-container">
    <el-empty v-if="!configInfo.id && formMode === 'init'">
      <el-button type="text" icon="el-icon-plus" @click="beginConfig">开始配置</el-button>
    </el-empty>
    <div v-if="configInfo.id && formMode === 'init'">
      <el-descriptions title="配置信息" direction="vertical" :column="2" :border="true">
        <template slot="extra">
          <el-button type="primary" size="small" @click="editConfig">修改</el-button>
        </template>
        <el-descriptions-item label="采购帐号">{{ configInfo.username || '--' }}</el-descriptions-item>
        <el-descriptions-item label="采购密码">{{ configInfo.password || '--' }}</el-descriptions-item>
        <el-descriptions-item label="appKey">{{ configInfo.appKey || '--' }}</el-descriptions-item>
        <el-descriptions-item label="appSecret">{{ configInfo.appSecret || '--' }}</el-descriptions-item>
        <el-descriptions-item label="公钥">{{ configInfo.publicKeyStr | cut }}</el-descriptions-item>
        <el-descriptions-item label="私钥">{{ configInfo.privateKeyStr | cut }}</el-descriptions-item>
        <el-descriptions-item label="支付类型">
          <dict-tag :type="DICT_TYPE.MALL_VOP_PAYMENT_TYPE" :value="configInfo.paymentType" />
        </el-descriptions-item>
        <el-descriptions-item label="回调地址">{{ configInfo.callbackUrl }}</el-descriptions-item>
        <el-descriptions-item label="商品池校验开关">
          <el-tag :type="configInfo.poolNameValidate ? 'primary' : 'info'">{{ configInfo.poolNameValidate ? '打开' : '关闭' }}</el-tag>
          <span> 商品池校验开关打开后，商品池需要和本地商品分类强制匹配，无法匹配的商品将同步失败</span>
        </el-descriptions-item>
        <el-descriptions-item label="全量商品池开关">
          <el-tag :type="configInfo.fullPoolSwitch ? 'primary' : 'info'">{{ configInfo.fullPoolSwitch ? '打开' : '关闭' }}</el-tag>
          <span> 全量商品池开关打开后，过滤不在商品分类关系表中商品</span>
        </el-descriptions-item>
      </el-descriptions>

      <el-descriptions title="Token信息" direction="vertical" :column="2" :border="true" style="margin-top: 20px;">
        <template slot="extra">
          <el-button type="primary" size="small" @click="fetchAccessToken">生成token</el-button>
          <el-button type="primary" size="small" @click="refreshAccessToken">刷新token</el-button>
          <el-button type="primary" size="small" v-hasPermi="['vop:sync']" v-if="comAccessTokenInfo" :loading="btnLoading1" @click="queryVopTotal">查询商品池总数</el-button>
          <el-button type="primary" size="small" v-hasPermi="['vop:sync']" v-if="comAccessTokenInfo" :loading="btnLoading2" @click="syncVopGoods">同步商品池</el-button>
        </template>
        <template v-if="accessToken.id">
          <el-descriptions-item label="过期时间">
            <el-tag :type="comTagType">{{ parseTime(accessToken.validTime) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="剩余时间">
            <el-tag :type="comTagType">{{ comAcessTokenLeftTime }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="token信息">{{ comAccessTokenInfo || '--' }}</el-descriptions-item>
        </template>
        <template v-if="!accessToken.id">
          <el-descriptions-item label="token信息">
            <el-empty description="当前无token信息，请生成token"></el-empty>
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </div>
    <el-form v-if="formMode === 'edit'" ref="configForm" label-position="top" :model="configForm" :rules="configRules"
      label-width="80px" style="width:950px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="采购帐号(采购合同中获取)" prop="username">
            <el-input v-model="configForm.username" maxlength="50" placeholder="请输入采购帐号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="采购密码(采购合同中获取)" prop="password">
            <el-input v-model="configForm.password" maxlength="50" placeholder="请输入采购密码" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="appKey(京东VOP开放平台中获取)" prop="appKey">
            <el-input v-model="configForm.appKey" maxlength="50" placeholder="请输入appKey" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="appSecret(京东VOP开放平台中获取)" prop="appSecret">
            <el-input v-model="configForm.appSecret" maxlength="100" placeholder="请输入appSecret" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="公钥" prop="publicKeyStr">
        <el-input v-model="configForm.publicKeyStr" maxlength="500" type="textarea" :rows="4" placeholder="请输入公钥" />
      </el-form-item>
      <el-form-item label="私钥" prop="privateKeyStr">
        <el-input v-model="configForm.privateKeyStr" maxlength="1000" type="textarea" :rows="8" placeholder="请输入私钥" />
      </el-form-item>
      <el-form-item label="支付类型" prop="paymentType">
        <el-radio-group v-model="configForm.paymentType">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.MALL_VOP_PAYMENT_TYPE)"
                      :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
            </el-radio>
          </el-radio-group>
      </el-form-item>
      <el-form-item label="商品池校验" prop="poolNameValidate">
        <el-switch
          v-model="configForm.poolNameValidate"
          active-text="打开"
          inactive-text="关闭"
        />
      </el-form-item>
      <el-form-item label="全量商品池开关" prop="fullPoolSwitch">
        <el-switch
          v-model="configForm.fullPoolSwitch"
          active-text="打开"
          inactive-text="关闭"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="default" @click="cancelForm">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">提 交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import * as api from "@/api/mall/config/vopConfig";
export default {
  name: "MallConfigSupplierJdvop",
  components: {},
  props: {
    id: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      configInfo: {},
      accessToken: {},
      formMode: 'init',
      submitLoading: false,
      btnLoading1: false,
      btnLoading2: false,
      configForm: {
        id: undefined,
        username: '',
        password: '',
        appKey: '',
        appSecret: '',
        publicKeyStr: '',
        privateKeyStr: '',
        paymentType: undefined,
        poolNameValidate: false,
        fullPoolSwitch: false
      },
      configRules: {
        username: [
          { required: true, trigger: 'blur', message: '请输入采购帐号' }
        ],
        password: [
          { required: true, trigger: 'blur', message: '请输入采购密码' }
        ],
        appKey: [
          { required: true, trigger: 'blur', message: '请输入appKey' }
        ],
        appSecret: [
          { required: true, trigger: 'blur', message: '请输入appSecret' }
        ],
        publicKeyStr: [
          { required: true, trigger: 'blur', message: '请输入公钥' }
        ],
        privateKeyStr: [
          { required: true, trigger: 'blur', message: '请输入私钥' }
        ],
        paymentType: [
          { required: true, trigger: 'change', message: '请选择支付类型' }
        ]
      },
    }
  },
  computed: {
    comAccessTokenInfo() {
      if(this.accessToken) {
        return JSON.stringify(this.accessToken, null, 4)
      }
      return ''
    },
    comAcessTokenLeftTime() {
      if(this.accessToken.id) {
        let diff = this.accessToken.validTime - new Date().getTime()
        if(diff <= 0) {
          return '已过期'
        }
        if(diff < 1000 * 60) {
          return parseInt(diff / 1000) + '秒'
        }
        if(diff < 1000 * 60 * 60) {
          return parseInt(diff / 1000 / 60) + '分钟'
        }
        if(diff < 1000 * 60 * 60 * 24) {
          return parseInt(diff / 1000 / 60 / 60) + '小时'
        }
        return (diff / 1000 / 60 / 60 / 24) + '天'
      }
      return ''
    },
    comTagType() {
      if(this.accessToken.id) {
        let diff = this.accessToken.validTime - new Date().getTime()
        if(diff <= 0) {
          return 'error'
        }
        return 'success'
      }

      return 'error'
    }
  },
  filters: {
    cut(val, length = 20, suffix = '...') {
      if(val && val.length > length) {
        return val.substring(length) + suffix
      }
      return val || '--'
    }
  },
  created() {
    this.checkVopConfig()
  },
  methods: {
    beginConfig() {
      this.formMode = 'edit'
    },
    async checkVopConfig() {
      let res = await api.checkVopConfig(this.id)
      this.configInfo = res.data || {}
      if(this.configInfo.id) {
          this.loadAccessToken()
        }
    },
    editConfig() {
      this.formMode = 'edit'
      Object.assign(this.configForm, this.configInfo)
    },
    cancelForm() {
      this.formMode = 'init'
    },
    async doSubmit() {
      this.submitLoading = true
      this.configForm.supplierId = this.id
      try {
        if (this.configForm.id !== undefined) {
          await api.updateVopConfig(this.configForm)
          this.$modal.msgSuccess("修改成功");
        } else {
          await api.createVopConfig(this.configForm)
          this.$modal.msgSuccess("保存成功");
        }
        this.submitLoading = false
        this.formMode = 'init'
        this.checkVopConfig()
      } catch(e) {
        this.$modal.msgError("保存失败，请稍后重试")
        this.submitLoading = false
      }
    },
    submitForm() {
      this.$refs["configForm"].validate(valid => {
        if (!valid) {
          return;
        }
        this.doSubmit()
      });
    },
    loadAccessToken() {
      api.getVopAccessToken().then(response => {
        this.accessToken = response.data || {}
      })
    },
    fetchAccessToken() {
      api.getVopTokenAuthorizeUrl().then(response => {
        let url = response.data
        window.open(url, '_blank');
        setTimeout(() => {
          this.loadAccessToken()
        }, 3000)
      })
    },
    refreshAccessToken() {
      api.refreshVopAccessToken().then(response => {
        this.accessToken = response.data || {}
        console.log('vopAccessToken===', this.accessToken)
      })
    },
    async queryVopTotal() {
      this.btnLoading1 = true
      let res = await api.getVopTotal()
      if(res.code === 0) {
        this.$alert(`商品池商品总数: ${ res.data }`, '查询结果');
      }
      this.btnLoading1 = false
    },
    async syncVopGoods() {
      this.btnLoading2 = true
      try {
        let res = await api.syncVopAllGoods();
        if(res.code === 0) {
          this.$modal.msgSuccess("同步任务提交成功，后台正在同步中...");
        }
      } catch(e) {}
      this.btnLoading2 = false
    }
  }
}
</script>

<style lang="scss" scoped></style>