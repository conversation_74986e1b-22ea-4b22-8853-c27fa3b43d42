<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" class="order-form" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="搜索方式">
        <el-input v-model="queryParams.searchValue" clearable style="width: 300px">
          <el-select v-model="queryParams.searchType" slot="prepend" style="width: 100px">
            <el-option v-for="dict in searchTypes" :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-input>
      </el-form-item>
      <el-form-item label="供应商" prop="supplierId">
        <supplier-select v-model="queryParams.supplierId" placeholder="请选择供应商" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="商品分类" prop="categoryIds">
        <category-select v-model="queryParams.categoryIds" :extProps="{checkStrictly: true}" placeholder="请选择商品分类" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="统计日期" prop="statsDate">
        <el-date-picker v-model="queryParams.statsDate" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item label="标签状态" prop="tagStatus">
        <el-select v-model="queryParams.tagStatus" clearable placeholder="请选择" @keyup.enter.native="handleQuery">
          <el-option :value="1" label="有标签"></el-option>
          <el-option :value="0" label="无标签"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="商品标签" prop="skuTags">
        <tag-select size="small" v-model="queryParams.skuTags" placeholder="请选择标签" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <div>
          <el-button v-hasPermi="['stats:sale-date:export']" icon="el-icon-files" size="mini" type="primary" plain :loading="exportLoading" @click="exportData">导出</el-button>
        </div>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-card shadow="always" class="stats-card-v1">
      <div class="tips">净销售额 = 总订单金额 - 取消订单金额 - 售后订单金额</div>
      <div class="stats-total"> 
        <div class="stats-item"> 
          <span class="stats-title">净销售金额:</span>
          <span class="stats-val">{{ formatMoney(totalStats.netTotalPrice) }}</span>
        </div>
        <div class="stats-item"> 
          <span class="stats-title">总订单额:</span>
          <span class="stats-val">{{ formatMoney(totalStats.totalPrice) }}</span>
        </div>
        <div class="stats-item"> 
          <span class="stats-title">取消订单金额:</span>
          <span class="stats-val">{{ formatMoney(totalStats.cancelTotalPrice) }}</span>
        </div>
        <div class="stats-item"> 
          <span class="stats-title">总订单数量:</span>
          <span class="stats-val">{{ totalStats.totalCount }}</span>
        </div>
        <div class="stats-item"> 
          <span class="stats-title">售后订单额:</span>
          <span class="stats-val">{{ formatMoney(totalStats.afterSaleTotalPrice) }}</span>
        </div>
        <div class="stats-item"> 
          <span class="stats-title">售后订单数量:</span>
          <span class="stats-val">{{ totalStats.afterSaleTotalCount }}</span>
        </div>
      </div>
    </el-card>

    <el-tabs class="form" v-model="queryParams.statsType" style="float:none;margin-bottom: 10px;" @tab-click="handleQuery">
      <el-tab-pane label="按日期统计" name="date"></el-tab-pane>
      <el-tab-pane label="按年月统计" name="yearMonth"></el-tab-pane>
      <el-tab-pane label="按SKU统计" name="sku"></el-tab-pane>
    </el-tabs>

    <div style="margin: 0 0 20px;"> 
      <el-radio-group v-model="renderType" @change="initLineChart">
        <el-radio-button label="table">数据表格</el-radio-button>
        <el-radio-button label="chart" v-if="queryParams.statsType !== 'sku'">图表展示</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 列表 -->
    <el-table v-show="renderType === 'table'" v-loading="loading" ref="table" :data="list" 
    @sort-change="handleSortChange"
    :default-sort = "queryParams.statsType === 'sku' ? {prop: 'totalPrice', order: 'descending'} : {}"
    show-summary
    :summary-method="getSummaries">
      <el-table-column label="日期" align="center" prop="statsDate" v-if="queryParams.statsType === 'date'">
        <template slot-scope="{ row }">
          <div>{{ parseTime(row.statsDate, '{y}-{m}-{d}') }}</div>
        </template>
      </el-table-column>
      <el-table-column label="年月" align="center" prop="yearMonth" v-if="queryParams.statsType === 'yearMonth'">
        <template slot-scope="{ row }">
          <div>{{ row.yearMonth }}</div>
        </template>
      </el-table-column>
      <el-table-column label="SKU名称" align="center" prop="skuName" v-if="queryParams.statsType === 'sku'" width="300" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <div>{{ row.skuName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="平台SKU" align="center" prop="skuId" v-if="queryParams.statsType === 'sku'">
        <template slot-scope="{ row }">
          <div> {{ row.skuId }}</div>
        </template>
      </el-table-column>
      <el-table-column label="三方SKU" align="center" prop="thirdSkuId" v-if="queryParams.statsType === 'sku'">
        <template slot-scope="{ row }">
          <div> {{ row.thirdSkuId || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="商品标签" align="center" prop="skuTags" v-if="queryParams.statsType === 'sku'" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <div> {{ row.skuTags && row.skuTags.length ? row.skuTags.join('/') : '' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="供应商" align="center" prop="supplierName" v-if="queryParams.statsType === 'sku'"/>
      <el-table-column label="订单数量" align="center" prop="orderCount" :sortable="queryParams.statsType === 'sku' ? 'custom' : false">
        <template slot-scope="{ row }">
          <div>{{ row.orderCount }}</div>
        </template>
      </el-table-column>
      <el-table-column label="订单总金额" align="center" prop="totalPrice" :sortable="queryParams.statsType === 'sku' ? 'custom' : false">
        <template slot-scope="{ row }">
          <div>{{ formatMoney(row.totalPrice) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="售后订单数" align="center" prop="afterSaleTotalCount" :sortable="queryParams.statsType === 'sku' ? 'custom' : false">
        <template slot-scope="{ row }">
          <div>{{ row.afterSaleTotalCount }}</div>
        </template>
      </el-table-column>
      <el-table-column label="售后金额" align="center" prop="afterSaleTotalPrice" :sortable="queryParams.statsType === 'sku' ? 'custom' : false">
        <template slot-scope="{ row }">
          <div>{{ formatMoney(row.afterSaleTotalPrice) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="售后率" align="center" prop="totalPrice">
        <template slot-scope="{ row }">
          <div>{{ row | afterSaleRate }}%</div>
        </template>
      </el-table-column>
      <el-table-column label="取消订单金额" align="center" prop="cancelTotalPrice" :sortable="queryParams.statsType === 'sku' ? 'custom' : false">
        <template slot-scope="{ row }">
          <div>{{ formatMoney(row.cancelTotalPrice) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="净销售金额" align="center" prop="netTotalPrice" :sortable="queryParams.statsType === 'sku' ? 'custom' : false">
        <template slot-scope="{ row }">
          <div>{{ formatMoney(row.netTotalPrice) }}</div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination v-if="renderType === 'table'" v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize" @pagination="getList"/>
    
    <ExportAlert ref="exportAlert"></ExportAlert>

    <SaleLineChart ref="saleLineChart" v-show="renderType === 'chart' && queryParams.statsType !== 'sku'"></SaleLineChart>
  </div>
</template>

<script>
import { parseTime} from "@/utils/ruoyi";
import * as api from '@/api/mall/statistics/stats-v2'
import SaleLineChart from '@/views/mall/statistics/trade/components/saleLineChart'
import CategorySelect from "@/views/mall/product/spu/components/category-select"
import SupplierSelect from "@/views/mall/product/spu/components/supplier-select"
import TagSelect from "@/views/mall/product/seo/components/tag-select"
import ExportAlert from '@/components/AsyncTaskAlert/export'
export default {
  name: 'StatsStatsListCom',
  components: {
    SaleLineChart, CategorySelect, SupplierSelect, TagSelect, ExportAlert
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      allTotal: 0,
      total: 0,
      // 列表
      list: [],
      renderType: 'table',
      queryParams: {
        pageNo: 1,
        pageSize: 20,
        sortType: 10,
        statsType: 'date',
        searchType: 'skuId',
        searchValue: '',
        supplierId: null,
        tagStatus: null,
        statsDate: [],
        categoryIds: [],
        skuTags: []
      },
      searchTypes: [
        { label: '平台SKU', value: 'skuId' },
        { label: 'SKU名称', value: '商品名称' },
        { label: '用户名', value: 'userName' },
        { label: '用户ID', value: 'userId' },
        { label: '订单号', value: 'orderNo' },
        { label: '部门名称', value: 'deptName' },
        { label: '部门编码', value: 'deptCode' }
      ],
      totalStats: {}
    }
  },
  filters: {
    afterSaleRate(row) {
      if(!row.totalPrice) {
        return 0
      }
      let rate = row.afterSaleTotalPrice * 1.0 / row.totalPrice * 100
      return  rate.toFixed(2)
    }
  },
  created() {
    this.handleQuery()
    this.loadTotalStats()
  },
  methods: {
    async loadTotalStats() {
      const params = this.getParams()
      const res = await api.getStatsSaleTotal(params)
      this.totalStats = res.data
    },
    /** 查询列表 */
    async getList() {
      this.loading = true
      const params = this.getParams()
      if (params == null) {
        this.loading = false;
        return
      }
      let res = null
      if(this.queryParams.statsType === 'date') {
        res = await api.pageQuerySaleOnDate(params)
      } else if(this.queryParams.statsType === 'yearMonth') {
        res = await api.pageQuerySaleOnYearMonth(params)
      } else if(this.queryParams.statsType === 'sku') {
        res = await api.pageQueryRankSku(params)
      }
      if (res.code === 0 && res.data) {
        const list = res.data.list
        this.list = list
        this.total = Number(res.data.total)
      } else {
        this.list = []
        this.total = 0
      }
      this.loading = false
      this.loadTotalStats()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.renderType = 'table'
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 20,
        sortType: 1,
        statsType: 'date',
        searchType: 'skuId',
        searchValue: '',
        tagStatus: null,
        supplierId: null,
        statsDate: [],
        categoryIds: [],
        skuTags: []
      }
      this.handleQuery();
    },
    handleSortChange({ prop, order }) {
      this.queryParams.sortType = 10
      if(prop === 'totalPrice' && order === 'descending') {
        this.queryParams.sortType = 10
      } else if(prop === 'totalPrice' && order === 'ascending') {
        this.queryParams.sortType = 11
      } else if(prop === 'orderCount' && order === 'descending') {
        this.queryParams.sortType = 20
      } else if(prop === 'orderCount' && order === 'ascending') {
        this.queryParams.sortType = 21
      }  else if(prop === 'afterSaleTotalCount' && order === 'descending') {
        this.queryParams.sortType = 30
      } else if(prop === 'afterSaleTotalCount' && order === 'ascending') {
        this.queryParams.sortType = 31
      } else if(prop === 'afterSaleTotalPrice' && order === 'descending') {
        this.queryParams.sortType = 40
      } else if(prop === 'afterSaleTotalPrice' && order === 'ascending') {
        this.queryParams.sortType = 41
      } else if(prop === 'cancelTotalPrice' && order === 'descending') {
        this.queryParams.sortType = 50
      } else if(prop === 'cancelTotalPrice' && order === 'ascending') {
        this.queryParams.sortType = 51
      } else if(prop === 'netTotalPrice' && order === 'descending') {
        this.queryParams.sortType = 60
      } else if(prop === 'netTotalPrice' && order === 'ascending') {
        this.queryParams.sortType = 61
      } 
      console.log(prop, order)
      this.handleQuery()
    },
    getParams() {
      const params = {
        pageNo: this.queryParams.pageNo,
        pageSize: this.queryParams.pageSize,
        tagStatus: this.queryParams.tagStatus,
        sortType: this.queryParams.sortType
      }
      if(this.queryParams.statsType !== 'sku') {
        delete params.sortType
      }
      if(this.queryParams.supplierId) {
        params.supplierId = this.queryParams.supplierId
      }
      if (this.queryParams.statsDate && this.queryParams.statsDate.length > 0) {
        params.statsDate = this.queryParams.statsDate.join(',')
      }
      if (this.queryParams.searchValue) {
        this.$set(params, this.queryParams.searchType, this.queryParams.searchValue)
      }
      if(this.queryParams.skuTags && this.queryParams.skuTags.length) {
        params.skuTags = this.queryParams.skuTags
      }
      if(this.queryParams.categoryIds && this.queryParams.categoryIds.length) {
        if(this.queryParams.categoryIds.length > 0) {
          params.categoryId1 = this.queryParams.categoryIds[0]
        }
        if(this.queryParams.categoryIds.length > 1) {
          params.categoryId2 = this.queryParams.categoryIds[1]
        }
        if(this.queryParams.categoryIds.length > 2) {
          params.categoryId3 = this.queryParams.categoryIds[2]
        }
      }

      if(this.queryParams.searchType == 'userId'){
        if (Number.isNaN(Number(this.queryParams.searchValue))){
          this.$message.error('用户ID必须为数字')
          return null
        }
      }
      else if(this.queryParams.searchType == 'skuId'){
        if (Number.isNaN(Number(this.queryParams.searchValue))){
          this.$message.error('平台SKU须为数字')
          return null
        }
      }

      return params
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = []
      let offset = 0
      sums[0] = '合计'
      if(this.queryParams.statsType === 'sku') {
        offset = 4
      }
      let s1 = 0, s2 = 0, s3 = 0, s4 = 0, s5 = 0, s6 = 0
      this.list.forEach(item => {
        s1 += item.orderCount
        s2 += item.totalPrice
        s3 += item.afterSaleTotalCount
        s4 += item.afterSaleTotalPrice
        s5 += item.cancelTotalPrice
        s6 += item.netTotalPrice
      })
      sums[1 + offset] = s1
      sums[2 + offset] = this.formatMoney(s2)
      sums[3 + offset] = s3
      sums[4 + offset] = this.formatMoney(s4)
      sums[6 + offset] = this.formatMoney(s5)
      sums[7 + offset] = this.formatMoney(s6)

      return sums;
    },
    exportData() {
      let params = this.getParams();
      params.expType = this.queryParams.statsType
      this.$refs.exportAlert.init(api.exportStatsSaleByType, params, '销量统计导出')
    },
    initLineChart() {
      if(this.renderType === 'table') {
        return
      }
      
      const data = JSON.parse(JSON.stringify(this.list)).reverse()
      let xAxisData = []
      if(this.queryParams.statsType === 'date') {
        xAxisData = data.map(item => parseTime(item.statsDate, '{y}-{m}-{d}'))
      } else if(this.queryParams.statsType === 'yearMonth') {
        xAxisData = data.map(item => item.yearMonth)
      }

      const opts = {
        legend: ['订单数量', '净销售金额'],
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: [
          { type: 'value', name: '订单数量' },
          { type: 'value', name: '净销售金额' }
        ],
        series: [
          {
            name: '订单数量',
            type: 'line',
            yAxisIndex: 0,
            data: data.map(x => x.orderCount)
          },
          {
            name: '净销售金额',
            type: 'line',
            yAxisIndex: 1,
            data: data.map(x => x.netTotalPrice)
          },
        ]
      }
      setTimeout(() => {
        this.$refs.saleLineChart.init(opts)
      }, 500)
    }
  }

}
</script>

<style lang="scss">
.stats-card-v1 {
  .el-card__body {
    padding: 10px !important;
  }
  .tips {
    margin: 5px 10px;
    font-size: 14px;
    font-weight: 500;
  }
}
.stats-total {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 14px;
  .stats-item {
    margin: 0 5px;
    .stats-title {
      font-weight: 500;
      padding: 0 5px;
    }
  }
}
</style>