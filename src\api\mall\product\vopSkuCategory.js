import request from '@/utils/request'

// 创建京东sku分类关系
export function createVopSkuCategory(data) {
  return request({
    url: '/mall/vop-sku-category/create',
    method: 'post',
    data: data
  })
}

// 更新京东sku分类关系
export function updateVopSkuCategory(data) {
  return request({
    url: '/mall/vop-sku-category/update',
    method: 'put',
    data: data
  })
}

// 删除京东sku分类关系
export function deleteVopSkuCategory(id) {
  return request({
    url: '/mall/vop-sku-category/delete?id=' + id,
    method: 'delete'
  })
}

// 获得京东sku分类关系
export function getVopSkuCategory(id) {
  return request({
    url: '/mall/vop-sku-category/get?id=' + id,
    method: 'get'
  })
}

// 获得京东sku分类关系分页
export function getVopSkuCategoryPage(query) {
  return request({
    url: '/mall/vop-sku-category/page',
    method: 'get',
    params: query
  })
}

// 导出京东sku分类关系 Excel
export function exportVopSkuCategoryExcel(query) {
  return request({
    url: '/mall/vop-sku-category/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 下载导入模板
export function getImportTemplate() {
  return request({
    url: '/mall/vop-sku-category/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}
