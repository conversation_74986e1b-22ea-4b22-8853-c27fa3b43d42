import request from '@/utils/request'

// 创建人事系统集成配置
export function createHrmsConfig(data) {
  return request({
    url: '/mall/hrms-config/create',
    method: 'post',
    data: data
  })
}

// 更新人事系统集成配置
export function updateHrmsConfig(data) {
  return request({
    url: '/mall/hrms-config/update',
    method: 'put',
    data: data
  })
}

// 删除人事系统集成配置
export function deleteHrmsConfig() {
  return request({
    url: '/mall/hrms-config/delete',
    method: 'delete'
  })
}

// 获得人事系统集成配置
export function getHrmsConfig() {
  return request({
    url: '/mall/hrms-config/get',
    method: 'get'
  })
}

// 同步人事系统数据至system
export function syncAll2System() {
  return request({
    url: '/mall/hrms-config/sync-all',
    method: 'post'
  })
}
