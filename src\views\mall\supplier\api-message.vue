<template>
  <div class="app-container"> 
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="消息ID" prop="id">
        <el-input v-model="queryParams.id" placeholder="请输入消息Id" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="消息类型" prop="messageType" v-if="false">
        <el-input v-model.number="queryParams.messageType" placeholder="请输入消息类型" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="处理状态" prop="messageStatus">
        <el-select v-model="queryParams.messageStatus" placeholder="请选择处理状态" clearable size="small">
          <el-option label="已处理" :value="1"/>
          <el-option label="未处理" :value="0"/>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime" label-width="80px">
        <el-date-picker v-model="queryParams.createTime" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']" style="width: 240px;"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="消息ID" align="center" prop="id" />
      <el-table-column label="消息类型" align="center" prop="messageType" />
      <el-table-column label="消息状态" align="center" prop="messageStatus">
        <template v-slot="scope">
          <el-tag :type="scope.row.messageStatus === 1 ? 'success' : 'primary'">{{ scope.row.messageStatus === 1 ? '已处理' : '未处理' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="消息内容" align="center" prop="content"/>
      <el-table-column label="创建时间" align="center" prop="createTime">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

  </div>
</template>

<script>
import * as api from '@/api/mall/supplier/api-message'
import { formatDate } from "@/utils/dateUtils"
export default {
  name: 'SupplierApiMessage',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 消息列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        messageType: null,
        messageStatus: null,
        id: null,
        createTime: []
      },
    }
  },
  mounted() {
    let preDate = new Date()
    preDate.setTime(new Date().getTime() - (1000 * 60 * 60 * 24 * 30))
    this.queryParams.createTime.push(formatDate(preDate, 'yyyy-MM-dd 00:00:00'))
    this.queryParams.createTime.push(formatDate(new Date(), 'yyyy-MM-dd 23:59:59'))

    this.handleQuery()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      let params = {...this.queryParams}
      if(!params.id || isNaN(params.id)) {
        delete params.id
      }
      if (params.createTime && params.createTime.length > 0) {
        params.createTime = params.createTime.join(',')
      }
      // 执行查询
      api.getMessagePage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
  }

}
</script>

<style>

</style>