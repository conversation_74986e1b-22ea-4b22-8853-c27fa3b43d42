<template>
  <div class="app-container">
    <el-form v-loading="loading" :model="form" :rules="rules" ref="form" size="small" label-width="120px">
      <el-form-item label="上级分类" prop="categoryIds">
        <el-cascader ref="category" :show-all-levels="true" :props="cascaderProps" v-model="form.categoryIds"
          :options="categoryList" style="width:400px;"></el-cascader>
      </el-form-item>
      <el-form-item label="标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入标题" :maxlength="200" clearable />
      </el-form-item>
      <el-form-item label="编码" prop="code">
        <el-input v-model="form.code" placeholder="请输入编码" :maxlength="50" clearable />
      </el-form-item>
      <el-form-item label="摘要" prop="summary">
        <el-input type="textarea" v-model="form.summary" placeholder="请输入摘要" clearable />
      </el-form-item>
      <el-form-item label="开启状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                    :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="正文" prop="content">
        <!-- <editor v-model="form.content" :min-height="192" /> -->
        <Tinymce v-model="form.content"></Tinymce>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="save">保存</el-button>
        <el-button icon="el-icon-refresh" @click="back">返回</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
  
<script>
import Editor from '@/components/Editor';
import * as categoryApi from "@/api/system/cms/category.js";
import * as api from "@/api/system/cms/content.js"
import Tinymce from '@/components/tinymce/index'
export default {
  name: "CmsAddContent",
  components: {
    Editor, Tinymce
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      cascaderProps: {
        label: 'categoryName',
        value: 'id',
        checkStrictly: false
      },
      // 表单参数
      form: {
        id: undefined,
        categoryIds: [],
        categoryId: undefined,
        title: '',
        code: '',
        content: '',
        summary: '',
        status: 1,
        publishStatus: 0
      },
      // 表单校验
      rules: {
        title: [
          { required: true, message: "标题不能为空", trigger: "blur" }
        ],
        categoryIds: [
          { required: true, message: "上级分类不能为空", trigger: "change" }
        ],
        content: [
          { required: true, message: "内容不能为空", trigger: "change" }
        ]
      },
      categoryList: []
    };
  },
  created() {
    this.queryCategoryList()
    let contentId = this.$route.query.id
    if (contentId) {
      this.getDetail(contentId)
    } 
  },
  methods: {
    async queryCategoryList() {
      this.loading = true;
      const res = await categoryApi.getCmsCategoryList({status: 0})
      if (res.code === 0 && res.data) {
        this.categoryList = this.handleTree(res.data, "id", "parentId");
      } else {
        this.$message.error(res.msg)
      }
      this.loading = false
    },
    getDetail(contentId) {
      this.loading = true;
      api.getCmsContent(contentId).then(res => {
        Object.assign(this.form, res.data)
        this.loading = false;
      });
    },
    save() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.saveContent()
        }
      });
    },
    async saveContent() {
      const params = Object.assign({}, this.form)
      params.categoryId = this.form.categoryIds[this.form.categoryIds.length - 1];
      let func = api.createCmsContent
      if (params.id) {
        func = api.updateCmsContent
      } 
      const res = await func(params)
      if (res.code === 0) {
        this.$message.success(params.id ? '编辑成功' : '创建成功')
        this.back()
      } else {
        this.$message.error(res.msg)
      }
    },
    back() {
      const obj = { path: "/cms/content", name: "CmsContent" };
      this.$tab.closeOpenPage(obj).then(() => {
        this.$tab.refreshPage();
      })
    }
  }
};
</script>
  