export const ORDER_STATUS_LIST = [
    { label: '未确认', value: 1, style: 'info' },
    { label: '已确认', value: 2, style: '' },
    { label: '已发货', value: 3, style: 'warning' },
    { label: '已送达', value: 4, style: 'success' },
    { label: '已签收', value: 5, style: 'success' },
    { label: '售后中', value: 6, style: 'danger' },
    { label: '售后完成', value: 7, style: 'warning' },
    { label: '已完成', value: 8, style: 'success' },
    { label: '已取消', value: 9, style: 'info' }
]
export const ORDER_AUDIT_STATUS_LIST = [
    { label: '未审批', value: 1, style: 'info' },
    { label: '审批中', value: 2, style: '' },
    { label: '审批驳回', value: 3, style: 'danger' },
    { label: '审批通过', value: 4, style: 'success' }
]

export const YCRH_AUDIT_STATUS_LIST = [
  { label: '待审批', value: '0', style: 'info' },
  { label: '审批通过', value: '1', style: 'success' },
  { label: '审批驳回', value: '2', style: 'danger' }
]

export const BILL_STATUS_LIST = [
  { label: '新建', value: 0, style: 'info' },
  { label: '已推送', value: 1, style: '' },
  { label: '已取消', value: 2, style: 'warning' },
  { label: '已完成', value: 3, style: 'success' }
]

export const ORDER_BILL_STATUS_LIST = [
  { label: '未开始', value: -2, style: 'info' },
  { label: '关联账单', value: -1, style: 'primary' },
  { label: '已推送', value: 0, style: 'primary' },
  { label: '已提交财务', value: 1, style: 'primary' },
  { label: '已接单', value: 2, style: 'primary' },
  { label: '已分单', value: 3, style: 'primary' },
  { label: '已制单', value: 4, style: 'success' },
  { label: '已退单', value: 9, style: 'danger' }
]

export const ORDER_YCRH_STATUS_LIST = [
  { label: '未推送', value: -2, style: 'info' },
  { label: '未冻结', value: 0, style: 'primary' },
  { label: '已推送', value: 1, style: 'primary' },
  { label: '审批中', value: 2, style: 'primary' },
  { label: '已审批', value: 3, style: 'primary' },
  { label: '已下单', value: 4, style: 'primary' },
  { label: '待结算', value: 5, style: 'primary' },
  { label: '结算中', value: 6, style: 'primary' },
  { label: '已结算', value: 7, style: 'success' },
  { label: '已拆分', value: 11, style: 'danger' },
  { label: '已驳回', value: 21, style: 'danger' }
]

export const INVOICE_STATUS_LIST = [
  { label: '未开票', value: 0, style: 'info' },
  { label: '已申请开票', value: 1, style: '' },
  { label: '开票完成', value: 2, style: 'success' },
  { label: '开票失败', value: 3, style: 'danger' },
  { label: '验真处理中', value: 4, style: 'primary' },
  { label: '验真完成', value: 5, style: 'success' },
  { label: '验真失败', value: 6, style: 'danger' }
]

export const SETTLEMENT_WAY_LIST = [  
  { label: '线上结算', value: false, style: 'success' },
  { label: '线下结算', value: true, style: 'primary' }
]

export const PRODUCT_SPEC_TYPE = [
  { label: '单规格', value: 0, style: 'info' },
  { label: '多规格', value: 1, style: 'primary' }
]

export const ORDER_ASSET_STATUS = [
  { label: '待处理', value: 0, style: 'info' },
  { label: '已提交', value: 1, style: 'primary' },
  { label: '无须建档', value: 2, style: 'info' },
  { label: '建档中', value: 3, style: 'primary' },
  { label: '建档成功', value: 4, style: 'success' },
  { label: '建档失败', value: 5, style: 'danger' }
]

export const AFTER_SALE_REASON_LIST = [
  { label: '与商家协商一致退款', value: '100', way: [10,20] },
  { label: '拍错/多拍/不喜欢', value: '110', way: [10,20] },
  { label: '未按约定时间发货', value: '120', way: [10,20] },
  { label: '快递/物流一直未送到', value: '130', way: [10] },
  { label: '货物破损已拒签', value: '140', way: [10] },
  { label: '少件/漏发', value: '150', way: [10,20] },
  { label: '材质/面料与商品描述不符', value: '160', way: [20] },
  { label: '颜色/图案/款式等不符', value: '170', way: [20] },
  { label: '大小/尺寸/重量/厚度不符', value: '180', way: [20] },
  { label: '质量问题', value: '190', way: [20] },
  { label: '包装/商品破损/污渍', value: '200', way: [20] },
  { label: '假冒品牌', value: '210', way: [20] },
  { label: '卖家发错货', value: '220', way: [20] },
  { label: '发票问题', value: '230', way: [20] }
]

export const ORDER_ITEM_ACCEPT_STATUS = [
  { label: '未上传', value: 0, style: 'info' },
  { label: '已上传', value: 1, style: 'primary' },
  { label: '已确认', value: 2, style: 'success' }
]

export const SUPPLIER_FORM_STATUS_LIST = [
    { label: '待提交', value: 0, style: 'info' },
    { label: '待审批', value: 1, style: 'primary' },
    { label: '审批通过', value: 2, style: 'success' },
    { label: '审批驳回', value: 3, style: 'danger' }
]

export const getOrderLabel = (status) => {
    let obj = ORDER_STATUS_LIST.find(item => item.value === status)
    return obj ? obj.label : '--'
}

export const getOrderAuditLabel = (status) => {
    let obj = ORDER_AUDIT_STATUS_LIST.find(item => item.value === status)
    return obj ? obj.label : '--'
}

export const getYcrhAuditLabel = (status) => {
  let obj = YCRH_AUDIT_STATUS_LIST.find(item => item.value === status)
  return obj ? obj.label : '--'
}

export const getBillStatusLabel = (status) => {
  let obj = BILL_STATUS_LIST.find(item => item.value === status)
  return obj ? obj.label : '--'
}

export const getOrderBillStatusLabel = (status) => {
  let obj = ORDER_BILL_STATUS_LIST.find(item => item.value === status)
  return obj ? obj.label : '--'
}

export const getOrderYcrhStatusLabel = (status) => {
  let obj = ORDER_YCRH_STATUS_LIST.find(item => item.value.toString() === status)
  return obj ? obj.label : '--'
}

export const getOfflineSettlementWayLabel = (way) => {
  let obj = SETTLEMENT_WAY_LIST.find(item => item.value === way)
  return obj ? obj.label : '--'
}

export const getInvoiceStatusLabel = (status) => {
  let obj = INVOICE_STATUS_LIST.find(item => item.value === status)
  return obj ? obj.label : '--'
}

export const getProductSpecTypeLabel = (status) => {
  let obj = PRODUCT_SPEC_TYPE.find(item => item.value === status)
  return obj ? obj.label : '--'
}

export const getOrderAssetStatusLabel = (status) => {
  let obj = ORDER_ASSET_STATUS.find(item => item.value === status)
  return obj ? obj.label : '--'
}

export const getOrderItemAcceptStatusLabel = (status) => {
  let obj = ORDER_ITEM_ACCEPT_STATUS.find(item => item.value === status)
  return obj ? obj.label : '--'
}

export const isBusinessPay = (paymentMethod) => {
  return paymentMethod && [1,2,10].includes(paymentMethod)
} 

export const getSupplierFormStatusLabel = (status) => {
  let obj = SUPPLIER_FORM_STATUS_LIST.find(item => item.value === status)
  return obj ? obj.label : '--'
}