<template>
  <div class="import-alert" v-if="show">
    <el-progress v-if="taskInfo.status === 0" :text-inside="true" :stroke-width="26" :percentage="taskInfo.progress"></el-progress>
    <div v-loading="taskInfo.status === 0"></div>
    <div style="margin: 10px 0;">
      <el-row :gutter="20">
        <el-col :span="6">
          <div>
            <el-statistic group-separator="," :value="total" title="导入记录总数"></el-statistic>
          </div>
        </el-col>
        <el-col :span="6">
          <div>
            <el-statistic group-separator="," :value="createdCount" title="插入处理数量"></el-statistic>
          </div>
        </el-col>
        <el-col :span="6">
          <div>
            <el-statistic group-separator="," :value="updatedCount" title="更新处理数量"></el-statistic>
          </div>
        </el-col>
        <el-col :span="6">
          <div>
            <el-statistic group-separator="," :value="failedCount" title="导入失败数量"></el-statistic>
          </div>
        </el-col>
      </el-row>
    </div>
    <el-alert v-if="taskInfo.status === 1" title="导入成功" type="success" center show-icon :closable="false">
      <span>耗时： {{ timeCost }} </span>
      <el-link v-if="fileUrl" :href="fileUrl" type="primary" target="_blank" style="margin-left:20px;">点击下载导入结果</el-link>
    </el-alert>
    <el-alert v-if="taskInfo.status === 2" title="导入失败" type="error" center show-icon :closable="false">
      服务器处理异常，请稍后重试！
    </el-alert>
  </div>
</template>

<script>
import * as commonApi from '@/api/mall/common'
export default {
  name: "ImportAlert",
  data() {
    return {
      show: false,
      taskId: null,
      taskInfo: {
        status: 0,
        progress: 0
      }
    }
  },
  computed: {
    fileUrl() {
      return this.taskInfo.downloadUrl
    },
    timeCost() {
      if(this.taskInfo.startTime && this.taskInfo.finishTime) {
        let diff = this.taskInfo.finishTime - this.taskInfo.startTime
        return (diff * 1.0 / 1000).toFixed(1) + 'S'
      }
      return 0
    },
    total() {
      return this.taskInfo.total || 0
    },
    createdCount() {
      return this.taskInfo.createdCount || 0
    },
    updatedCount() {
      return this.taskInfo.updatedCount || 0
    },
    failedCount() {
      return this.taskInfo.failedCount || 0
    },
  },
  methods: {
    reset() {
      this.taskInfo.status = 0
      this.taskInfo.progress = 0
      this.taskInfo.total = 0
      this.taskInfo.createdCount = 0
      this.taskInfo.updatedCount = 0
      this.taskInfo.failedCount = 0
      this.show = false
    },
    init(taskFunc, params) {
      this.reset()
      this.show = true
      taskFunc(params).then(res => {
        console.log('import-----', res)
        this.taskId = res.data
        this.taskInfo.status = 1
        this.pollingQuery()
      }).catch((e) => {
        this.taskInfo.status = 2
      })
    },
    init2(taskId) {
      this.reset()
      this.taskId = taskId
      this.show = true
      this.pollingQuery()
    },
    pollingQuery() {
      let func = () => {
        this.loadTaskInfo()
        if(!this.checkFinish()) {
          setTimeout(func, 5000)
        } else {
          this.$emit('on-complete')
        }
      }
      // this.taskInfo.progress = 10
      setTimeout(() => {
        func()
      }, 1000)
    },
    checkFinish() {
      return this.taskInfo.status !== 0
    },
    loadTaskInfo() {
      if(!this.taskId) {
        return
      }
      let params = {
        taskId: this.taskId,
        taskType: '2'
      }
      commonApi.getAsyncTaskInfo(params).then(res => {
        this.taskInfo = res.data || {}
      })
    }
  }

}
</script>

<style scoped lang="scss">
.import-alert {
  margin-top: 20px;
}
</style>
