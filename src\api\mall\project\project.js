import request from '@/utils/request'

// 创建直采项目经费卡
export function createZcProject(data) {
  return request({
    url: '/mall/project/create',
    method: 'post',
    data: data
  })
}

// 更新直采项目经费卡
export function updateZcProject(data) {
  return request({
    url: '/mall/project/update',
    method: 'put',
    data: data
  })
}

// 更新直采项目经费卡余额
export function updateZcProjectBalance(data) {
  return request({
    url: '/mall/project/update-balance',
    method: 'put',
    data: data
  })
}

// 同步采集直采项目经费卡
export function collectZcProject(params) {
  return request({
    url: '/mall/project/collect',
    method: 'put',
    params: params
  })
}

// 删除直采项目经费卡
export function deleteZcProject(id) {
  return request({
    url: '/mall/project/delete?id=' + id,
    method: 'delete'
  })
}

// 获得直采项目经费卡
export function getZcProject(id) {
  return request({
    url: '/mall/project/get?id=' + id,
    method: 'get'
  })
}

// 获得直采项目经费卡分页
export function getZcProjectPage(query) {
  return request({
    url: '/mall/project/page',
    method: 'get',
    params: query
  })
}

// 导出直采项目经费卡 Excel
export function exportZcProjectExcel(query) {
  return request({
    url: '/mall/project/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 批量查询余额及额度
export function batchQueryBalance(data) {
  return request({
    url: '/mall/project/batch-get-balance',
    method: 'post',
    data: data
  })
}