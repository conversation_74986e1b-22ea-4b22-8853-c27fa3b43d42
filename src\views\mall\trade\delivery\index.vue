<template>
  <div class="app-container">
    <el-tabs class="form" v-model="tabType" style="float:none;" @tab-click="handleClick" v-if="showTab">
      <el-tab-pane :label="`全部待发货(${allTotal})`" name="all"></el-tab-pane>
      <el-tab-pane name="1">
        <span slot="label">
          超2天待发货(
          <span :style="{ color: needDeliveryTotal > 0 ? 'red' : '' }">{{ needDeliveryTotal || 0 }}</span>
          )
        </span>
      </el-tab-pane>
      <el-tab-pane name="2">
        <span slot="label">
          已发货(
            <span>{{ shippedTotal || 0 }}</span>
          )
        </span>
      </el-tab-pane>
    </el-tabs>

    <el-form :model="queryParams" ref="queryForm" class="order-form" size="small" :inline="true" v-show="showSearch"
      label-width="68px">
      <el-form-item label="订单" label-width="40px">
        <el-input v-model="queryParams.orderCode" clearable style="width: 180px">
        </el-input>
      </el-form-item>
      <el-form-item label="商品" label-width="40px">
        <el-input v-model="queryParams.skuCode" clearable style="width: 180px">
        </el-input>
      </el-form-item>
      <el-form-item label="用户" label-width="40px">
        <el-input v-model="queryParams.searchValue" clearable style="width: 280px">
          <el-select v-model="queryParams.searchType" slot="prepend" style="width: 110px">
            <el-option v-for="dict in searchTypes" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-input>
      </el-form-item>
      <el-form-item v-if="notSupplierUser()" v-hasPermi="['mall:supplier']" label="供应商" prop="supplierId"
        label-width="60px">
        <supplier-select v-model="queryParams.supplierId" placeholder="请选择供应商" @keyup.enter.native="handleQuery"
          style="width: 170px" />
      </el-form-item>
      <el-form-item label="商品标签" prop="skuTags" v-if="showTagSelect">
        <tag-select size="small" v-model="queryParams.skuTags" placeholder="请选择标签" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item v-if="tabType == 'all'" label="超时天数" label-width="90px">
        <el-select v-model="overDays" clearable placeholder="请选择超时天数" style="width: 140px">
          <el-option label="超2天" :value="2" />
          <el-option label="超3天" :value="3" />
          <el-option label="超4天" :value="4" />
          <el-option label="超5天" :value="5" />
          <el-option label="超6天" :value="6" />
          <el-option label="超7天" :value="7" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button v-hasPermi="['trade:order:query']" icon="el-icon-search" type="primary" size="small"
          @click="handleQuery">搜索</el-button>
        <el-button v-hasPermi="['trade:order:query']" icon="el-icon-refresh" size="small"
          @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <div>
          <el-button v-hasPermi="['trade:delivery:create']" icon="el-icon-refresh" size="small"
            @click="batchOrderDelivery">订单发货</el-button>
          <el-button v-hasPermi="['trade:delivery:create']" icon="el-icon-collection-tag" size="small"
            @click="handleImport">物流导入</el-button>
          <el-button v-hasPermi="['trade:order:export']" icon="el-icon-download" size="small" :loading="exportLoading"
            @click="exportOrder">订单导出</el-button>
        </div>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table ref="table" class="order-table" v-loading="loading" :data="list" row-key="no" max-height="560"
      default-expand-all :row-class-name="cellClass" @selection-change="handleSelectionChange"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column label="商品" width="360">
        <template slot-scope="{ row }">
          <span v-if="row.children != undefined" class="orderCodeNum">
            订单编号: {{ row.no || '--' }} <OrderTagCom v-if="showOrderTag" :orderItems="row.items"></OrderTagCom>
          </span>
          <div v-else class="goodsCard">
            <div class="goodsCard-item" v-for="item in row.productList" :key="item.skuId">
              <div class="goodsCard-item-img">
                <img :src="item.picUrl" alt="" height="30" />
              </div>
              <div class="goodsCard-item-info">
                <div class="goodsCard-item-info-name text-ellipsis" :title="item.skuName">{{ item.skuName || '--' }}
                </div>
                <div class="goodsCard-item-info-code">{{ item.skuId || '--' }} <span
                    v-if="item.skuInnerId && row.supplierType !== 20"> / {{ item.skuInnerId }}</span></div>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="单价" width="130px">
        <template slot-scope="{ row }">
          <div v-if="row.children != undefined">下单时间:</div>
          <div v-else class="unit-price" v-for="item in row.productList" :key="item.skuId">
            <span class="unit-price-number">{{ formatMoney(item.skuPrice) }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="数量" width="180px">
        <template slot-scope="{ row }">
          <div v-if="row.children != undefined" :class="calDiffClass(row)">
            {{ parseTime(row.submitTime) }}
          </div>
          <div v-else class="unit-price" v-for="item in row.productList" :key="item.skuId">
            x{{ item.count }}
            <div style="display:inline-block;margin-left:5px;" v-if="item.afterSaleStatus">
              <dict-tag :type="DICT_TYPE.TRADE_ORDER_ITEM_AFTER_SALE_STATUS" :value="item.afterSaleStatus" />
            </div>
            <OrderTagCom v-if="showOrderTag" :tags="item.skuTags"></OrderTagCom>
            <OrderAcceptInfo :orderItem="item" :orderStatus="row.status"></OrderAcceptInfo>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="下单人" align="center">
        <template slot-scope="{ row }">

          <div v-if="row.children != undefined">

            <span v-if="row.cancelTime">取消时间:</span>

            <span v-else-if="row.finishTime">完成时间:</span>
            <span v-else-if="row.receiveTime">签收时间:</span>
            <span v-else-if="row.deliveryTime">发货时间:</span>
            <div v-else-if="row.auditCompleteTime">确认时间:</div>
          </div>
          <div v-if="row.children == undefined">
            <div>{{ row.userName }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="收货人" align="center" width="170px">
        <template slot-scope="{ row }">
          <div v-if="row.children != undefined" :class="calDiffClass(row)">
            {{ parseTime(row.auditCompleteTime) }}
          </div>
          <div v-if="row.children != undefined">
            <span v-if="row.cancelTime">{{ parseTime(row.cancelTime) }}</span>
            <span v-else-if="row.finishTime">{{ parseTime(row.finishTime) }}</span>
            <span v-else-if="row.receiveTime">{{ parseTime(row.receiveTime) }}</span>
            <span v-else-if="row.deliveryTime" :class="calDiffClass(row, 2)">{{ parseTime(row.deliveryTime) }}</span>
          </div>
          <div v-if="row.children == undefined">
            <div>{{ row.receiverName }}</div>
            <div>{{ row.receiverMobile || '--' }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="下单金额" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.children != undefined">
            <dict-tag :type="DICT_TYPE.MALL_ORDER_PLATFORM" :value="row.platform" />
          </div>
          <div v-if="row.children == undefined">
            <div>{{ formatMoney(row.orderPrice) }}</div>
            <dict-tag :type="DICT_TYPE.MALL_PAYMENT_METHOD" :value="row.paymentMethod || 1" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.children == undefined">
            <div class="status-box-">
              <div class="status-ball" :style="{ backgroundColor: getStatusColor(row.status) }"></div>
              <OrderStatusCom :order="row"></OrderStatusCom>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="供应商" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.children != undefined">
            <div>{{ row.supplierName }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" v-if="false">
        <template slot-scope="{ row }">
          <div v-if="row.children == undefined">
            <div>{{ parseTime(row.updateTime) }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button v-if="!scope.row.children" size="medium" type="text" @click="goToDetail(scope.row)">详情</el-button>
          <el-button v-if="!scope.row.children" size="medium" type="text"
            @click="orderDelivery(scope.row)">订单发货</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination v-show="total > 0" :page-sizes="[20, 50, 100, 200]" :total="total" :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 订单物流导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="550px" append-to-body>
      <div class="flex-vertical-center">
        <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url"
          :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess"
          :auto-upload="false" drag>
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip text-center" slot="tip">
            <span>仅允许导入xls、xlsx格式文件，建议单次导入不超过5000条</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
              @click="importTemplate">下载模板</el-link>
          </div>
        </el-upload>
        <ImportAlert v-if="upload.open" ref="importAlert" @on-complete="getList"></ImportAlert>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="importLoading" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <ExportAlert ref="exportAlert"></ExportAlert>

    <delivery-send :open.sync="deliverySendOpen" :orders="selectOrders" @close="deliverySendClose" />
  </div>
</template>

<script>
import OrderTagCom from '@/views/mall/trade/order/components/orderTag'
import OrderStatusCom from '@/views/mall/trade/order/components/orderStatus'
import { getOrderList, exportOrderList, getOrderStatus } from "@/api/mall/trade/order.js"
import { formatDate } from "@/utils/dateUtils";
import SupplierSelect from "@/views/mall/product/spu/components/supplier-select";
import ExportAlert from '@/components/AsyncTaskAlert/export'
import TagSelect from "@/views/mall/product/seo/components/tag-select"
import { configMixins } from '@/views/mall/config/components/configMixin.js'
import OrderAcceptInfo from '@/views/mall/trade/order/components/acceptInfo'
import DeliverySend from "@/views/mall/trade/delivery/components/delivery-send";
import * as deliveryApi from "@/api/mall/trade/delivery"
import ImportAlert from '@/components/AsyncTaskAlert/import'
import { getBaseHeader } from "@/utils/request";

export default {
  name: "DeliveryIndex",
  components: { SupplierSelect, ExportAlert, OrderTagCom, TagSelect, OrderStatusCom, OrderAcceptInfo, DeliverySend, ImportAlert },
  mixins: [configMixins],
  props: {
    showTab: {
      type: Boolean,
      default() {
        return true
      }
    },
    paramTags: {
      type: Array,
      default() {
        return []
      }
    },
    showOrderTag: {
      type: Boolean,
      default() {
        return false
      }
    },
    showTagSelect: {
      type: Boolean,
      default() {
        return false
      }
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      importLoading: false,
      // 发货弹窗
      deliverySendOpen: false,
      // 当前操作的订单
      orderId: null,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      tabType: 'all',
      overDays: '',
      // 总条数
      allTotal: 0,
      total: 0,
      //已发货数量
      shippedTotal: 0,
      // 交易售后列表
      list: [],
      selectOrders: [],
      // 定义超时天数映射
      overDaysMap: {
        2: 2,
        3: 3,
        4: 4,
        5: 5,
        6: 6,
        7: 7
      },
      queryParams: {
        pageNo: 1,
        pageSize: 20,
        status: 2,
        delivery_status: 0,
        supplierId: null,
        platform: null,
        auditCompleteTime: [],
        skuTags: [],
        orderCode: null,
        skuCode: null,
        paymentMethod: null,
        searchType: 'userName',
        searchValue: null,
        auditStatusList: [],
      },
      searchTypes: [
        { label: '下单人', value: 'userName' },
        { label: '收货人', value: 'receiverName' },
        { label: '下单人Id', value: 'userId' },
        { label: '下单人手机', value: 'userMobile' },
        { label: '收货人手机', value: 'receiverMobile' }
      ],
      multipleSelection: [],
      needDeliveryTotal: 0,
      pickerOptions: {
        shortcuts: [{
          text: '本周',
          onClick(picker) {
            // This week (Monday to Sunday)
            const now = new Date()
            const day = now.getDay() || 7 // 周日返回0，转换为7
            const monday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - day + 1)
            const sunday = new Date(now.getFullYear(), now.getMonth(), now.getDate() + (7 - day))
            picker.$emit('pick', [monday, sunday])
          }
        }, {
          text: '本月',
          onClick(picker) {
            // Current month
            const start = new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            const end = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '本年',
          onClick(picker) {
            // Current year
            const start = new Date(new Date().getFullYear(), 0, 1)
            const end = new Date(new Date().getFullYear(), 11, 31)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '近7天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近30天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近90天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近180天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      // 导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/trade/delivery/import',
      },
    }
  },
  created() {
    this.getList()
    this.countStatus()
    this.getOrderStatusCount()
  },
  methods: {
    getStatusColor(status) {
      if ([1, 2, 3, 6].includes(status)) {
        return '#FF9500'
      } else if (status === 9) {
        return '#E5E6EB'
      }
      return '#00B42A'
    },
    calcAuditCompleteTime(days) {
      return [
        "2000-01-01 00:00:00",
        formatDate(new Date(Date.now() - days * 24 * 60 * 60 * 1000), 'yyyy-MM-dd HH:mm:ss')
      ]
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleClick(val) {
      // 重置查询参数
      this.queryParams = {
        pageNo: 1,
        pageSize: 20,
        status: 2,
        auditCompleteTime: null,
        delivery_status: 0,
      };

      this.getList()
      this.countStatus()
    },
    getParams() {
      var params = {
        pageNo: this.queryParams.pageNo,
        pageSize: this.queryParams.pageSize,
        sortType: 1,
        status: 2,
        delivery_status: 0,
      }
      if (this.queryParams.supplierId) {
        params.supplierId = this.queryParams.supplierId
      }
      if (this.queryParams.paymentMethod) {
        params.paymentMethod = this.queryParams.paymentMethod
      }
      if (this.queryParams.platform) {
        params.platform = this.queryParams.platform
      }
      if (this.queryParams.status !== 'all') {
        params.status = this.queryParams.status
      }
      if (this.queryParams.auditCompleteTime && this.queryParams.auditCompleteTime.length > 0) {
        params.auditCompleteTime = this.queryParams.auditCompleteTime.join(',')
      }
      if (this.queryParams.orderCode) {
        params.orderCode = this.queryParams.orderCode.trim()
      }
      if (this.queryParams.skuCode) {
        params.skuCode = this.queryParams.skuCode.trim()
      }
      if (this.paramTags && this.paramTags.length) {
        params.skuTags = this.paramTags
      }
      if (this.queryParams.skuTags && this.queryParams.skuTags.length) {
        params.skuTags = this.queryParams.skuTags
      }
      if (this.queryParams.auditStatusList && this.queryParams.auditStatusList.length > 0) {
        params.auditStatusList = this.queryParams.auditStatusList.map(item => {
          return Number(item)
        })
      }

      if (this.queryParams.searchValue) {
        if (this.queryParams.searchType == 'userId') {
          if (Number.isNaN(Number(this.queryParams.searchValue.trim()))) {
            this.$message.error('用户ID必须为数字')
            return null
          }
          else {
            params.userId = this.queryParams.searchValue.trim()
          }
        }

        if (this.queryParams.searchType == 'userName') {
          params.userName = this.queryParams.searchValue.trim()
        }

        if (this.queryParams.searchType == 'receiverName') {
          params.receiverName = this.queryParams.searchValue.trim()
        }

        if (this.queryParams.searchType == 'userMobile') {
          params.userMobile = this.queryParams.searchValue.trim()
        }

        if (this.queryParams.searchType == 'receiverMobile') {
          params.receiverMobile = this.queryParams.searchValue.trim()
        }
      }
      return params
    },

    async getOrderStatusCount(){
      const res = await getOrderStatus()
      if (res.code === 0 && res.data) {
        res.data.forEach(item => {
          if (item.status === 3) {
          this.shippedTotal = item.count || 0
        }
        })
      }
    },

    /** 查询列表 */
    async getList() {
      if (this.tabType === 'all') {
        if (this.overDays === null || this.overDays === '') {
          this.queryParams.auditCompleteTime = null
        } else if (this.overDaysMap[this.overDays]) {
          const days = this.overDaysMap[this.overDays]
          this.queryParams.auditCompleteTime = this.calcAuditCompleteTime(days)
        }
      } else if (this.tabType === '1') {
        this.queryParams.auditCompleteTime = this.calcAuditCompleteTime(2)
      }else if (this.tabType === '2') {
       this.queryParams.status = 3
      }

      this.loading = true;
      const params = this.getParams();
      if (params == null) {
        this.loading = false;
        return
      }
      const res = await getOrderList(params);
      if (res.code === 0 && res.data) {
        const list = res.data.list;
        res.data.list.forEach((item, index) => {
          item.children = [
            {
              id: item.id,
              no: item.no + index,
              supplierType: item.supplierType,
              productList: item.items || [],
              productCount: item.productCount,
              userName: item.userName,
              receiverName: item.receiverName,
              productPrice: item.productPrice,
              orderPrice: item.orderPrice,
              status: item.status,
              submitTime: item.submitTime,
              receiveTime: item.receiveTime,
              deliveryTime: item.deliveryTime,
              auditCompletetime: item.auditCompletetime,
              finishTime: item.finishTime,
              cancelTime: item.cancelTime,
              updateTime: item.updateTime,
              userDeleted: item.userDeleted,
              platform: item.platform,
              receiverMobile: item.receiverMobile,
              skuTags: item.skuTags,
              paymentMethod: item.paymentMethod,
              auditStatus: item.auditStatus
            }
          ]
        })
        this.list = list
        if (this.queryParams.status === 'all') {
          this.allTotal = Number(res.data.total)
        }
        this.total = Number(res.data.total);
        if (this.tabType === 'all') {
          this.allTotal = res.data.total || 0;
        }
        else if (this.tabType === '1'){
          this.needDeliveryTotal = res.data.total || 0;
        }

        this.$nextTick(() => {
          this.$refs.table.doLayout()
        })
      } else {
        this.list = []
        this.total = 0
      }
      this.loading = false;
    },

    async countStatus() {
      var params = this.getParams();
      params.auditCompleteTime = this.calcAuditCompleteTime(2).join(',')
      params.pageSize = 1
      const res = await getOrderList(params);
      if (res.code === 0 && res.data) {
        this.needDeliveryTotal = res.data.total || 0
      } else {
        this.needDeliveryTotal = 0
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.orderId = null;           // 清空订单ID
      this.queryParams.pageNo = 1;
      this.getList();
      this.countStatus();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.orderId = null;           // 清空订单ID
      this.overDays = null;
      this.queryParams = {
        pageNo: 1,
        pageSize: 20,
        orderCode: null,
        skuCode: null,
        status: 2,
        delivery_status: 0,
        auditCompleteTime: [],
        searchType: 'userName',
        searchValue: null,
      }
      this.handleQuery();
    },
    goToDetail(row) {
      this.$router.push({ name: this.showOrderTag ? 'TradeOrderDetail' : 'TradeOrderSDetail', query: { id: row.id } })
    },
    cellClass({ row }) {
      if (!row.hasOwnProperty('children')) {
        return 'disableheadselection'
      }
      return 'orderCode'
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      sums[0] = '合计'
      let s1 = 0
      this.list.forEach(item => {
        s1 += item.orderPrice
      })
      sums[5] = this.formatMoney(s1)

      return sums;
    },
    // 订单导出
    exportOrder() {
      const params = this.getParams();
      this.$refs.exportAlert.init(exportOrderList, params, '订单导出')
    },
    // 订单发货
    orderDelivery(row) {
      const order = {
        id: row.id,
        no: row.no.slice(0, -1),
        skuInfoList: row.productList.map(x => ({
          skuId: x.skuId,
          skuName: x.skuName,
          skuPrice: x.skuPrice,
          skuNum: x.count,
          skuImgUrl: x.picUrl
        }))
      }
      this.selectOrders = [order]
      this.deliverySendOpen = true
    },
    // 批量发货
    batchOrderDelivery() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择要发货的订单')
        return
      }
      this.selectOrders = this.multipleSelection.map(item => {
        return {
          id: item.id,
          no: item.no,
          skuInfoList: item.items
        }
      })
      this.deliverySendOpen = true
    },
    // 发货完成处理
    deliverySendClose(val) {
      if (val) {
        this.getList()
        this.countStatus()
      }
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "订单物流导入";
      this.importLoading = false
      this.upload.open = true;
      this.$nextTick(() => {
        this.$refs.upload.clearFiles();
      })
    },
    /** 下载模板操作 */
    importTemplate() {
      deliveryApi.getImportTemplate().then(response => {
        this.$download.excel(response, '订单物流导入模板.xls');
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.importLoading = false
      if (response.code !== 0) {
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        this.$modal.msgError(response.msg)
        return;
      }
      // this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$refs.importAlert.init2(response.data)
    },
    // 提交上传文件
    submitFileForm() {
      let files = this.$refs.upload.uploadFiles
      if (!files.length) {
        this.$modal.msg("请上传导入文件")
        return
      }

      this.importLoading = true
      this.$refs.upload.submit();
    },
  }
}
</script>

<style lang="scss" scoped>
.order-table {
  :deep(.el-table__expand-icon) {
    display: none;
  }

  :deep(.disableheadselection) {
    color: #8c8c8c;

    >td:first-child {
      >.cell {
        >.el-checkbox {
          display: none;
        }
      }
    }
  }

  :deep(.orderCode) {
    color: #333333;
    background-color: #f2f4f7;
  }

  .orderCodeNum {
    font-size: 12px;
    font-weight: bold;
    color: #333333;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .goodsCard {
    display: block;

    .goodsCard-item {
      display: flex;
      width: 100%;

      +.goodsCard-item {
        margin-top: 20px;
      }

      .goodsCard-item-img {
        position: relative;
      }

      .goodsCard-item-info {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-left: 8px;
        font-size: 12px;
        line-height: 20px;
        max-width: 300px;

        .goodsCard-item-info-name {
          width: 300px;
          color: #1d2129;
        }

        .goodsCard-item-info-code {
          color: #86909c;
        }
      }
    }
  }

  .unit-price {
    display: flex;
    align-items: center;
    height: 38px;

    +.unit-price {
      margin-top: 20px;
    }

    .unit-price-number {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .status-box {
    display: flex;
    align-items: center;
    justify-content: center;

    .status-ball {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      margin-right: 10px;
    }
  }
}
</style>
