import request from '@/utils/request'



// 查询供应商收款账号
export function getSupplierAccountInfo(id) {
  return request({
    url: '/mall/supplier-account/get?supplierId=' + id,
    method: 'get'
  })
}


// 更新供应商收款账号
export function updateSupplierAccount(data) {
  return request({
    url: '/mall/supplier-account/update',
    method: 'put',
    data: data
  })
}

// 新增供应商收款账号
export function createSupplierAccount(data) {
  return request({
    url: '/mall/supplier-account/create',
    method: 'post',
    data: data
  })
}

