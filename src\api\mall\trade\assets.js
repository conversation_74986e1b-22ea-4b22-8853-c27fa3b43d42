import request from '@/utils/request'


// 查询固资建档信息列表
export function getOrderAssetsPage(params) {
  return request({
    url: '/trade/order-assets/page',
    method: 'get',
    params
  })
}

// 更新固资明细信息分类
export function updateOrderAssetsCategory(data) {
  return request({
    url: "/trade/order-assets/update-category",
    method: "put",
    data
  });
}

// 查询固资建档信息详情
export function getOrderAssetsDetail(params) {
  return request({
    url: '/trade/order-assets/get',
    method: 'get',
    params
  })
}

// 导出固资建档信息列表
export function exportOrderAssetsList(params) {
  return request({
    url: '/trade/order-assets/export-excel',
    method: 'get',
    params
  })
}

// 推送固资信息到财务
export function pushAsset2Ycrh(params) {
  return request({
    url: '/trade/order-assets/push-asset-ycrh',
    method: 'post',
    params
  })
}

// 推送固资信息到资产系统
export function pushAsset2Sys(params) {
  return request({
    url: '/trade/order-assets/push-asset-sys',
    method: 'post',
    params
  })
}

// 更新固资状态为建档完成
export function updateAssetComplete(data) {
  return request({
    url: '/trade/order-assets/update-complete',
    method: 'post',
    data
  })
}

// 更新固资状态为无须建档
export function updateNotAsset(params) {
  return request({
    url: '/trade/order-assets/update-not-asset',
    method: 'post',
    params
  })
}
