<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" @keyup.enter.native="handleQuery" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="商品名称" prop="skuName">
        <el-input v-model="queryParams.skuName" placeholder="请输入商品名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="平台SKU" prop="id">
        <el-input v-model="queryParams.id" oninput="value=value.replace(/^(0+)|[^\d]+/g,'')" placeholder="请输入商品SKU" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="三方SKU" prop="skuInnerId" v-if="notSupplierUser()">
        <el-input v-model="queryParams.skuInnerId" placeholder="请输入三方SKU" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item v-if="notSupplierUser() && checkPermi(['mall:supplier'])" label="供应商" prop="supplierId">
        <supplier-select size="small" v-model="queryParams.supplierId" placeholder="请选择供应商" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item v-if="notSupplierUser()" v-hasPermi="['product:category:query']" label="商品分类" prop="categoryIds">
        <category-select v-model="queryParams.categoryIds" :extProps="{checkStrictly: true}" placeholder="请选择商品分类" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="商品品牌" prop="brandId">
        <brand-select size="small" v-model="queryParams.brandId" placeholder="请选择商品品牌" @keyup.enter.native="handleQuery" />
      </el-form-item> -->
      <el-form-item v-if="checkPermi(['product:spu:update']) && notSupplierUser()" label="列出状态" prop="showStatus">
        <el-select v-model="queryParams.showStatus" clearable placeholder="请选择" @keyup.enter.native="handleQuery">
          <el-option :value="1" label="打开"></el-option>
          <el-option :value="0" label="关闭"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="checkPermi(['product:spu:update'])" label="SKU状态" prop="status">
        <el-select v-model="queryParams.status" clearable placeholder="请选择" @keyup.enter.native="handleQuery">
          <el-option :value="0" label="下架"></el-option>
          <el-option :value="1" label="上架"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="checkPermi(['product:spu:update'])" label="商品销量">
        <div class="flex-range-item"> 
          <div style="width:150px;">
            <el-form-item prop="salesCountMin">
              <el-input v-model="queryParams.salesCountMin" placeholder="最低销量" clearable oninput="value=value.replace(/^(0+)|[^\d]+/g,'')" @keyup.enter.native="handleQuery"/>
            </el-form-item>
          </div>
          <div class="seperator">-</div>
          <div style="width:150px;">
            <el-form-item prop="salesCountMax">
              <el-input v-model="queryParams.salesCountMax" placeholder="最高销量" clearable oninput="value=value.replace(/^(0+)|[^\d]+/g,'')" @keyup.enter.native="handleQuery"/>
            </el-form-item>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="商品价格" prop="salePrice" v-if="false">
        <div class="flex-range-item"> 
          <div style="width:150px;">
            <el-form-item prop="salePriceMin">
              <el-input v-model="queryParams.salePriceMin" placeholder="最低价格" clearable oninput="value= value.match(/\d+(\.\d{0,2})?/) ? value.match(/\d+(\.\d{0,2})?/)[0] : ''" @keyup.enter.native="handleQuery"/>
            </el-form-item>
          </div>
          <div class="seperator">-</div>
          <div style="width:150px;">
            <el-form-item prop="salePriceMax">
              <el-input v-model="queryParams.salePriceMax" placeholder="最高价格" clearable oninput="value= value.match(/\d+(\.\d{0,2})?/) ? value.match(/\d+(\.\d{0,2})?/)[0] : ''" @keyup.enter.native="handleQuery"/>
            </el-form-item>
          </div>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['product:spu:create']">添加商品</el-button>
        <el-button type="primary" plain icon="el-icon-edit" size="mini" @click="handleAddVop" v-hasPermi="['vop:sync']" v-if="notSupplierUser()">同步京东商品</el-button>
        <el-button v-if="checkPermi(['product:spu:update']) && notSupplierUser()" type="primary" plain icon="el-icon-document" size="mini" @click="multipleChangeStatus">批量调整列出</el-button>
        <el-button icon="el-icon-files" v-hasPermi="['product:sku:export']" plain size="mini" type="primary" :loading="exportLoading" @click="exportSku">商品导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"/>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list"
    @selection-change="handleSelectionChange"
    @sort-change="handleSortChange"
    :default-sort = "{prop: 'createTime', order: 'descending'}">
      <el-table-column type="selection" width="55" v-if="notSupplierUser()"></el-table-column>
      <el-table-column label="商品信息" align="center" width="250">
        <template v-slot="scope">
          <div class="product-info">
            <img v-if="scope.row.picUrl" :src="scope.row.picUrl" alt="分类图片" class="img-height" />
            <div :title="scope.row.skuName" class="message">{{ scope.row.skuName }}</div>
          </div>
        </template>
        <!-- TODO 前端优化：可以有个 + 号，点击后，展示每个 sku -->
      </el-table-column>
      <el-table-column label="平台/三方SKU" align="center" prop="id" width="180">
        <template v-slot="scope">
          <span>{{ scope.row.id }}</span><br>
          <span v-if="scope.row.skuInnerId">{{ scope.row.skuInnerId }}</span>
        </template>
      </el-table-column>

      <el-table-column label="销售价格" align="center" prop="salePrice" sortable="custom" width="150">
        <template v-slot="scope">
          <span>{{ formatMoney(scope.row.salePrice) }}</span>
          <span v-if="checkPermi(['product:spu:update-stock-price'])" style="margin-left: 5px;" @click="changeSkuPrice(scope.row)"><i class="el-icon-edit" style="cursor: pointer;"></i></span>
      </template>
      </el-table-column>
      <el-table-column v-if="checkPermi(['product:spu:update'])" label="销量" align="center" prop="salesCount" sortable="custom"/>
      <el-table-column label="供应商" align="center" prop="supplierName"/>
      <el-table-column label="品牌" align="center" prop="brandName"/>
      <el-table-column label="商品分类" align="center" prop="fullCategoryName"/>
      <el-table-column v-if="checkPermi(['product:spu:update'])" label="上架状态" align="center" prop="status">
        <template v-slot="scope">
          <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" @change="handleStatus(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column v-if="checkPermi(['product:spu:update-show-status'])" label="列出状态" align="center" prop="showStatus">
        <template v-slot="scope">
          <el-switch v-model="scope.row.showStatus" :active-value="1" :inactive-value="0" @change="handleShowStatus(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column v-if="checkPermi(['product:spu:update-platform-status'])" label="平台状态" align="center" prop="platformStatus">
        <template v-slot="scope">
          <el-switch v-model="scope.row.platformStatus" :active-value="1" :inactive-value="0" @change="handlePlatformStatus(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="100" sortable="custom">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="checkPermi(['product:spu:update'])" label="SKU状态" align="center" prop="status">
        <template v-slot="{row}">
          <dict-tag :type="DICT_TYPE.PRODUCT_SPU_STATUS" :value="row.status"/>
          <el-popover
            style="margin-left:5px;"
            v-if="row.statusError"
            title="不可售"
            width="200"
            trigger="click"
            :content="row.indexStatusDetail.reason">
            <i slot="reference" class="el-icon-warning"></i>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handlePreview(scope.row)"
                     v-hasPermi="['product:spu:query']">预览</el-button>
          <el-button size="mini" type="text" icon="el-icon-picture" @click="handleView(scope.row)"
                      v-hasPermi="['product:spu:query']">详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                      v-hasPermi="['product:spu:update']">修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <SkuForm ref="skuForm" :open.sync="skuFormOpen" :spuId="curSpuId" :skuId="curSkuId" @update-success="getList"/>
    <Preview ref="spuPreview" :open.sync="previewOpen" :spuId="curSpuId" :skuId="curSkuId"/>
    <SyncForm ref="syncForm" :open.sync="syncFormOpen" @success="getList"></SyncForm>

    <ExportAlert ref="exportAlert"></ExportAlert>
  </div>
</template>

<script>
import { getSkuPage2, getSkuDetail2, updateSkuStatus, updateShowStatus, updatePlatformStatus, exportSkuList, querySkuIndexStatus } from "@/api/mall/product/sku";
import SupplierSelect from "@/views/mall/product/spu/components/supplier-select";
import BrandSelect from "@/views/mall/product/spu/components/brand-select";
import CategorySelect from "@/views/mall/product/spu/components/category-select";
import SkuForm from "@/views/mall/product/spu/sku-form";
import SyncForm from "@/views/mall/product/spu/sync-form";
import Preview from "@/views/mall/product/spu/preview";
import ExportAlert from '@/components/AsyncTaskAlert/export';
export default {
  name: "ProductSku",
  components: { SupplierSelect, BrandSelect, CategorySelect, SkuForm, Preview, SyncForm , ExportAlert},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商品spu列表
      list: [],
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        skuInnerId: null,
        skuName: null,
        id: null,
        categoryIds: [],
        supplierId: null,
        showStatus: null,
        brandId: null,
        status: null,
        salesCountMin: null,
        salesCountMax: null,
        salePriceMin: null,
        salePriceMax: null,
      },
      skuFormOpen: false,
      curSpuId: null,
      curSkuId: null,
      previewOpen: false,
      syncFormOpen: false,
      multipleSelection: []
    };
  },
  watch: {
    "queryParams": {
      deep: true,
      handler(newVal, oldVal) {
        const ck = 'sku-list-params'
        sessionStorage.setItem(ck, JSON.stringify(newVal))
      }
    }
  },
  created() {
    this.initParams();
    this.getList();
  },
  methods: {
    initParams() {
      const ck = 'sku-list-params'
      const val = sessionStorage.getItem(ck)
      if(val) {
        Object.assign(this.queryParams, JSON.parse(val))
        this.queryParams.categoryIds = []
        this.queryParams.fullCategoryId = null
      }
    },
    getParams() {
      let params = {...this.queryParams};
      if(!params.skuId)  delete params.skuId
      if(!params.skuInnerId) delete params.skuInnerId
      params.salePriceMin = this.queryParams.salePriceMin === null ? null : params.salePriceMin;
      params.salePriceMax = this.queryParams.salePriceMax === null ? null : params.salePriceMax;
      if(params.categoryIds && params.categoryIds.length) {
        params.fullCategoryId = params.categoryIds.join('-')
      }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, "createTime");
      return params
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      const params = this.getParams();
      // 执行查询
      getSkuPage2(params).then(async (response) => {
        let list = ((response.data && response.data.list) || []).map(item => {
          if (item.showStatus === undefined) {
            item.showStatus = 1
          }
          return item
        });
        this.total = parseInt(response.data.total);
        await this.getSkuIndexStatus(list) 
        this.list = list
        this.loading = false;
      });
    },
    async getSkuIndexStatus(list) {
      if(!list || !list.length) {
        return
      }
      let params = {
        skuIds: list.map(item => item.id)
      }
      let res = await querySkuIndexStatus(params)
      if(res.data && res.data.length) {
        list.forEach((item) => {
          item.indexStatusDetail = res.data.find(indexItem => indexItem.skuId === item.id)
          item.statusError = item.indexStatusDetail && !item.indexStatusDetail.needIndex && (item.status && item.platformStatus && item.showStatus)
        })
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push({ name: 'ProductSpuForm'})
    },
    changeSkuPrice(row) {
      if(row.supplierType === 1) {
        this.$modal.msgWarning("京东商品不支持修改价格");
        return
      }
      this.skuFormOpen = true
      this.curSpuId = row.spuId
      this.curSkuId = row.id
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      if(row.supplierType === 1) {
        this.$modal.msgWarning("京东商品不支持修改");
        return
      }
      this.$router.push({ name: 'ProductSpuForm', query: { spuId: row.spuId }})
    },
    handleView(row) {
      this.$router.push({ name: 'SkuDetail', query: { spuId: row.spuId } })
    },
    handlePreview(row) {
      this.curSpuId = row.spuId
      this.curSkuId = row.id
      this.previewOpen = true
    },
    handleAddVop() {
      this.syncFormOpen = true
    },
    /** 修改状态按钮操作 */
    async handleStatus(row) {
      let params = {
        id: row.id,
        status: row.status
      }
      await updateSkuStatus(params)
      this.getList()
      this.$modal.msgSuccess("操作成功")
    },
    async handleShowStatus(row) {
      let params = [{
        id: row.id,
        showStatus: row.showStatus
      }]
      await updateShowStatus(params)
      this.getList()
      this.$modal.msgSuccess("操作成功")
    },
    async handlePlatformStatus(row) {
      let params = [{
        id: row.id,
        status: row.platformStatus
      }]
      await updatePlatformStatus(params)
      this.getList()
      this.$modal.msgSuccess("操作成功")
    },
    async multipleChangeStatus() {
      if (!this.multipleSelection.length) {
        this.$message.warning("请至少选择一个商品")
        return;
      }
      let params = this.multipleSelection.map(item => {
        return {
          id: item.id,
          showStatus: item.showStatus === 1 ? 0 : 1
        }
      })
      let res = await updateShowStatus(params)
      if(res.code === 0) {
        this.getList()
        this.$modal.msgSuccess("操作成功")
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleSortChange({ prop, order }) {
      this.queryParams.sortType = 10
      if(prop === 'createTime' && order === 'descending') {
        this.queryParams.sortType = 10
      } else if(prop === 'createTime' && order === 'ascending') {
        this.queryParams.sortType = 11
      } else if(prop === 'salesCount' && order === 'descending') {
        this.queryParams.sortType = 20
      } else if(prop === 'salesCount' && order === 'ascending') {
        this.queryParams.sortType = 21
      } else if(prop === 'salePrice' && order === 'descending') {
        this.queryParams.sortType = 40
      } else if(prop === 'salePrice' && order === 'ascending') {
        this.queryParams.sortType = 41
      }
      console.log(prop, order)
      this.handleQuery()
    },
    // 商品导出
    exportSku() {
      const params = this.getParams()
      this.$refs.exportAlert.init(exportSkuList, params, '商品导出')
    },
  }
};
</script>
<style lang="scss">
.app-container {
  .product-info {
    display: flex;

    .img-height {
      height: 50px;
      width: 50px;
    }

    .message {
      margin-left: 10px;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      word-break: break-all;
      -webkit-box-orient: vertical;
      white-space: normal;
      overflow: hidden;
      height: 50px;
      line-height: 25px;
    }
  }
}
</style>
