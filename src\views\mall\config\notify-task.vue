<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option v-for="dict in statusDictDatas" :key="parseInt(dict.value)" :label="dict.label" :value="parseInt(dict.value)"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['mall:notify-task:edit']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="submitAll" v-if="!taskList.length && !loading" v-hasPermi="['mall:notify-task:edit']">一键添加</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <div> 
      <el-table :data="taskList" style="width: 100%">
        <el-table-column prop="taskCode" align="center" label="任务名称" width="220">
          <template v-slot="{ row }">
            <span>{{ getEnumName(row.taskCode) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="notifySwitch" align="center" label="站内信开关">
          <template v-slot="{ row }">
            <el-tag :type="row.notifySwitch ? 'success' : 'info'">{{ row.notifySwitch ? '打开' : '关闭' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="notifyTplCode" align="center" label="站内信模板" width="270">
          <template v-slot="{ row }">
            <span>{{ row.notifyTplCode }}</span>
            <el-tag v-if="row.tplResult" size="small" :type="row.tplResult[row.notifyTplCode] ? 'success' : 'info'">{{ row.tplResult[row.notifyTplCode] ? '有效' : '无效' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="smsSwitch" align="center" label="短信开关">
          <template v-slot="{ row }">
            <el-tag :type="row.smsSwitch ? 'success' : 'info'">{{ row.smsSwitch ? '打开' : '关闭' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="smsTplCode" align="center" label="短信模板" width="270">
          <template v-slot="{ row }">
            <span>{{ row.smsTplCode }}</span>
            <el-tag v-if="row.tplResult" size="small" :type="row.tplResult[row.smsTplCode] ? 'success' : 'info'">{{ row.tplResult[row.smsTplCode] ? '有效' : '无效' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" align="center" label="状态">
          <template v-slot="{ row }">
            <el-switch v-model="row.status" :active-value="0" :inactive-value="1" @change="handleStatusChange(row)" />
          </template>
        </el-table-column>
        <el-table-column prop="memo" align="center" label="备注" show-overflow-tooltip></el-table-column>
        <el-table-column prop="address" align="center" label="操作" width="240">
          <template v-slot="{ row }">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEdit(row)" v-hasPermi="['mall:notify-task:edit']">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(row)" v-hasPermi="['mall:notify-task:edit']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页组件 -->
    <pagination v-show="total > queryParams.pageSize" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize" @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="700px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="任务名称" prop="taskCode">
          <el-select v-model="form.taskCode" placeholder="请选择" @change="taskCodeChange" style="width:100%;" :disabled="!!form.id"> 
            <el-option v-for="(opt,index) in enumList" :key="index" :value="opt.code" :label="opt.name" :disabled="getEnumStatus(opt.code)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="站内信开关" prop="notifySwitch">
          <el-radio-group v-model="form.notifySwitch">
            <el-radio :label="true">打开</el-radio>
            <el-radio :label="false">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="站内信模板" prop="notifyTplCode">
          <el-input v-model="form.notifyTplCode" placeholder="请输入站内信模板" readonly />
        </el-form-item>
        <el-form-item label="短信开关" prop="smsSwitch">
          <el-radio-group v-model="form.smsSwitch">
            <el-radio :label="true">打开</el-radio>
            <el-radio :label="false">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="短信模板" prop="smsTplCode">
          <el-input v-model="form.smsTplCode" placeholder="请输入短信模板" readonly />
        </el-form-item>
        <el-form-item label="备注" prop="memo">
          <el-input type="textarea" :rows="3" maxlength="200" show-word-limit v-model="form.memo" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in statusDictDatas" :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getDictDatas, DICT_TYPE } from '@/utils/dict'
import * as api from '@/api/mall/config/notifyTask'
export default {
  name: 'ConfigNotifyTask',
  data() {
    return {
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      queryParams: {
        pageNo: 1,
        pageSize: 100,
        status: null
      },
      taskList: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        taskCode: [{ required: true, message: "任务编码不能为空", trigger: "blur" }]
      },
      enumList: [],
      // 数据字典
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS)
    }
  },
  computed: {
    taskCodeList() {
      return []
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.handleQuery()
      this.loadEnumList()
    },
    async loadEnumList() {
      const res = await api.getNotifyTaskEnumList()
      this.enumList = res.data
    },
    getEnumStatus(code) {
      if(this.taskList && this.taskList.length) {
        let hitObj = this.taskList.find(item => item.taskCode === code)
        return !!hitObj
      }

      return false
    },
    getEnumObj(code) {
      if(this.enumList && code) {
        return this.enumList.find(item => item.code === code)
      }

      return null
    },
    getEnumName(code) {
      let obj = this.getEnumObj(code)
      if(obj) {
        return obj.name
      }

      return ''
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery()
    },
    async getList() {
      this.loading = true 
      try {
        const res = await api.getNotifyTaskPage(this.queryParams)
        res.data.list.forEach(item => {
          item.tplResult = null
        })
        this.taskList = res.data.list
        this.validateTemplate()
        this.loading = false
      } catch(e) {
        this.loading = false
      }
    },
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加通知任务"
    },
    handleEdit(row) {
      this.reset()
      const id = row.id
      api.getNotifyTask(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改通知任务"
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        taskCode: undefined,
        notifySwitch: true,
        notifyTplCode: undefined,
        smsSwitch: false,
        smsTplCode: undefined,
        memo: undefined,
        status: 0,
      };
      this.resetForm("form")
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        if (this.form.id != null) {
          api.updateNotifyTask(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        api.createNotifyTask(this.form).then(response => {
          this.$modal.msgSuccess("新增成功")
          this.open = false
          this.getList()
        });
      });
    },
    async submitAll() {
      if(!this.taskList || !this.taskList.length) {
        this.enumList.forEach(async item => {
          let param = {
            taskCode: item.code,
            notifySwitch: true,
            notifyTplCode: item.tplCode,
            smsSwitch: false,
            smsTplCode: item.smsTplCode,
            memo: undefined,
            status: 0
          }
          await api.createNotifyTask(param)
          this.getList()
        })
      }
    },
    taskCodeChange() {
      let obj = this.getEnumObj(this.form.taskCode)
      if(obj) {
        this.form.notifyTplCode = obj.tplCode
        this.form.smsTplCode = obj.smsTplCode
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除此通知任务吗?').then(function() {
        return api.deleteNotifyTask(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    async validateTemplate() {
      if(this.taskList) {
        this.taskList.forEach(async row => {
          let res = await api.validateTplCode(row)
          let index = this.taskList.find(item => item.id === row.id)
          row.tplResult = res.data
          this.$set(this.taskList, index, row)
        })
      }
    },
    // 状态修改
    handleStatusChange(row) {
      let text = row.status === 0 ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '"此通知任务吗?').then(function() {
          return api.updateNotifyTask(row)
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === 1 ? 0: 1;
      });
    },
  }
}
</script>

<style>

</style>