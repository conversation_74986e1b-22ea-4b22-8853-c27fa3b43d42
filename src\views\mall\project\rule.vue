<template>
  <div>
    <el-tabs class="rule-tabs" v-model="tabIndex">
      <el-tab-pane label="项目规则" name="1"></el-tab-pane>
      <el-tab-pane label="商品白名单" name="2"></el-tab-pane>
    </el-tabs>

    <RuleTab1 v-if="tabIndex === '1'"></RuleTab1>
    <RuleTab2 v-if="tabIndex === '2'"></RuleTab2>
  </div>
</template>

<script>
import RuleTab1 from '@/views/mall/project/rule-tab1'
import RuleTab2 from '@/views/mall/project/rule-tab2'
export default {
  name: 'RuleIndex',
  components: { RuleTab1, RuleTab2 },
  data() {
    return {
      tabIndex: '1'
    }
  }
}
</script>

<style lang="scss" scoped>
.rule-tabs {
  margin: 10px 20px 5px;
}
</style>