<template>
  <div v-if="checkPermi(['product:tag:stats']) && comTags && comTags.length" class="order-tag-con"> 
    <el-tooltip effect="dark" placement="top">
      <template #content>
        <el-tag v-for="htag in comTags" :key="htag" style="margin-left: 5px;"> {{ htag }} </el-tag>
      </template>
      <el-tag>{{ comTags.join('/') | cut(6, comTags.length) }}</el-tag>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  name: 'OrderTagCom',
  props: {
    orderItems: {
      type: Array,
      default() {
        return []
      }
    },
    tags: {
      type: Array,
      default() {
        return []
      }
    }
  },
  filters: {
    cut(val, length, tagLength) {
      if(val && val.length > length) {
        return val.substring(0, length) + '...'
      }
      if(tagLength > 1) {
        return val + `(${tagLength})`
      }

      return val
    }
  },
  computed: {
    comTags() {
      if(this.orderItems && this.orderItems.length) {
        let tags = []
        this.orderItems.forEach(oi => {
          tags = tags.concat(oi.skuTags)
        })
        return Array.from(new Set(tags)).filter(tt => tt)
      }

      return this.tags
    }
  }

}
</script>

<style lang="scss" scoped>
.order-tag-con {
  display: inline-block;
  .el-tag {
    margin-left: 5px;
  }
}
</style>