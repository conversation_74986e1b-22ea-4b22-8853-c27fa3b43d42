import request from '@/utils/request'

// 获得VOP消息
export function getVopMessage(id) {
  return request({
    url: '/mall/vop-message/get?id=' + id,
    method: 'get'
  })
}

// 获得VOP消息分页
export function getVopMessagePage(query) {
  return request({
    url: '/mall/vop-message/page',
    method: 'get',
    params: query
  })
}

// vop消息消费
export function consume(messageId) {
  return request({
    url: '/mall/vop-message/consume?messageId=' + messageId,
    method: 'get'
  })
}

// vop消息批量消费
export function batchConsume(data) {
  return request({
    url: '/mall/vop-message/batchConsume',
    method: 'post',
    data: data
  })
}