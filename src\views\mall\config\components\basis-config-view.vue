<template>
  <div> 
    <div v-if="configInfo.id">
      <div class="flex-between"> 
        <div>
          <span style="font-size:1em;font-weight: 300;"> 上次更新时间：{{ parseTime(configInfo.updateTime) }} </span>
        </div>
        <div>
          <el-button type="primary" size="small" @click="editConfig">修改</el-button>
        </div>
      </div>
      
      <el-tabs v-model="activeTab" @tab-click="handleTabChange">
        <el-tab-pane label="基础配置" name="tab1">
          <el-descriptions :column="2" :border="true">
            <el-descriptions-item label="采购方名称" :labelStyle="{ width: '12%' }" :contentStyle="{ width: '38%' }">{{ configInfo.name || "--" }}（一般为主体名称，可用到导出或水印）</el-descriptions-item>
            <el-descriptions-item label="商城标题" :labelStyle="{ width: '12%' }">{{ configInfo.title || "--" }}</el-descriptions-item>
            <el-descriptions-item label="商城域名">
              <el-tag :key="tag" v-for="tag in configInfo.domainList" :disable-transitions="false">
                {{ tag }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="提交订单开关">
              <el-tag :type="configInfo.orderSwitch ? 'primary' : 'info'">{{ configInfo.orderSwitch ? "打开" :
                "关闭" }}</el-tag>
              <span> 如果关闭，则不会显示提交订单的按钮；</span>
            </el-descriptions-item>
            <el-descriptions-item v-if="!configInfo.orderSwitch" label="提交订单白名单">{{ configInfo.orderWhiteList || "--" }}</el-descriptions-item>
            <el-descriptions-item label="用户注册开关">
              <el-tag :type="configInfo.registerSwitch ? 'primary' : 'info'">{{ configInfo.registerSwitch ? "打开" :
                "关闭" }}</el-tag>
              <span> 如果打开，则开放用户通过手机号验证码注册，自助创建新账号</span>
            </el-descriptions-item>
            <el-descriptions-item label="找回密码开关">
              <el-tag :type="configInfo.resetPasswordSwitch ? 'primary' : 'info'">{{ configInfo.resetPasswordSwitch ?
                "打开" :
                "关闭" }}</el-tag>
              <span> 如果打开，则开放用户通过手机号验证码重置密码</span>
            </el-descriptions-item>
            <el-descriptions-item label="查看价格强制登录开关">
              <el-tag :type="configInfo.getPriceMustLogin ? 'primary' : 'info'">{{ configInfo.getPriceMustLogin ? "打开" :
                "关闭"
                }}</el-tag>
              <span> 如果打开，会显示采购入口并且会显示相关经费卡字段，如项目下拉选择，订单金额会直接从项目经费卡额度绑定，只能在项目经费卡可用额度范围内才能提交采购；</span>
            </el-descriptions-item>
            <el-descriptions-item label="商品展示字段">
              <el-checkbox-group :value="configInfo.productField" disabled>
                <el-checkbox v-for="dict in comProductFieldList" :key="dict.value" :label="dict.value">{{ dict.label
                  }}</el-checkbox>
              </el-checkbox-group>
            </el-descriptions-item>
            <el-descriptions-item label="登录类型">
              <dict-tag :type="DICT_TYPE.MALL_CONFIG_LOGIN_TYPE" :value="configInfo.loginType" />
              <dict-tag :type="DICT_TYPE.MALL_CONFIG_LOGOUT_TYPE" :value="configInfo.logoutType"
                style="margin-left:10px;" />
            </el-descriptions-item>
            <el-descriptions-item label="登录方式">
              <div v-for="item in configInfo.loginMethodList" :key="item"
                style="display: inline-block;margin-right: 10px;">
                <dict-tag :type="DICT_TYPE.MALL_LOGIN_METHOD" :value="item" />
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="登录地址">{{ configInfo.loginUrl || "--" }}</el-descriptions-item>
            <el-descriptions-item label="支付方式">
              <div v-for="item in configInfo.payMethodList" :key="item"
                style="display: inline-block;margin-right: 10px;">
                <dict-tag :type="DICT_TYPE.MALL_PAYMENT_METHOD" :value="item" />
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="全局通知开关">
              <el-tag :type="configInfo.globalTipSwitch ? 'primary' : 'info'">{{ configInfo.globalTipSwitch ? '打开' :
                '关闭'
                }}</el-tag>
              <span> 控制在商城端进入时是否可以弹框提示</span>
            </el-descriptions-item>
            <el-descriptions-item label="全局通知内容" v-if="configInfo.globalTipSwitch">
              <div v-if="configInfo.globalTipContent" v-html="configInfo.globalTipContent"></div>
              <div v-else>无</div>
            </el-descriptions-item>
            <el-descriptions-item label="商品评价开关">
              <el-tag :type="configInfo.productCommentSwitch ? 'primary' : 'info'">{{ configInfo.productCommentSwitch ?
                '打开' : '关闭' }}</el-tag>
              <span> 控制在商城端是否支持评价</span>
            </el-descriptions-item>
            <el-descriptions-item label="地址备注开关">
              <el-tag :type="configInfo.addressRemarkSwitch ? 'primary' : 'info'">{{ configInfo.addressRemarkSwitch ?
                '打开' : '关闭' }}</el-tag>
              <span> 下单时在地址后面添加备注信息，方便快递员识别直采平台商品，需要送货上门</span>
            </el-descriptions-item>
            <el-descriptions-item label="地址备注">{{ configInfo.addressRemark || "--" }}</el-descriptions-item>
            <el-descriptions-item label="平台客服">{{ configInfo.custService || "--" }}</el-descriptions-item>
            <el-descriptions-item label="管理后台入口">
              <span v-if="configInfo.adminEntrySwitch">
                <el-link :href="configInfo.adminEntryUrl" target="_blank"> {{ configInfo.adminEntryUrl || '未配置' }}</el-link>
              </span>
              <el-tag v-else type="info">已关闭</el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="业务配置" name="tab2">
          <el-descriptions :column="2" :border="true">
            <el-descriptions-item label="自动审批开关" :labelStyle="{ width: '12%' }" :contentStyle="{ width: '38%' }">
              <el-tag :type="configInfo.autoApprove ? 'primary' : 'info'">{{ configInfo.autoApprove ? "打开" : "关闭"
                }}</el-tag>
              <span> 仅在前置审批关闭时生效，打开后订单创建30分钟后自动确认；</span>
            </el-descriptions-item>
            <el-descriptions-item label="前置审批开关" :labelStyle="{ width: '12%' }">
              <el-tag :type="configInfo.approveSwitch ? 'primary' : 'info'">{{ configInfo.approveSwitch ? "打开" : "关闭"
                }}</el-tag>
              <span> 如果打开，会显示采购入口并且会显示审批流相关字段，如采购原因，补充材料等，订单只有审批通过才会进入发货流程；<b>需要在外部集成中完成配置审批流后打开</b></span>
            </el-descriptions-item>
            <el-descriptions-item label="项目经费卡开关">
              <el-tag :type="configInfo.projectSwitch ? 'primary' : 'info'">{{ configInfo.projectSwitch ? "打开" : "关闭"
                }}</el-tag>
              <span> 如果打开，会显示采购入口并且会显示相关经费卡字段，如项目下拉选择，订单金额会直接从项目经费卡额度绑定，只能在项目经费卡可用额度范围内才能提交采购；</span>
            </el-descriptions-item>
            <el-descriptions-item label="设备仪器申请单导出开关">
              <el-tag :type="configInfo.instrumentApplyExport ? 'primary' : 'info'">{{ configInfo.instrumentApplyExport ?
                '打开' : '关闭' }}</el-tag>
              <span> 如果打开，支持导出设备仪器申请单</span>
            </el-descriptions-item>
            <el-descriptions-item label="电子签收单导出开关">
              <el-tag :type="configInfo.orderReceiptExport ? 'primary' : 'info'">{{ configInfo.orderReceiptExport ?
                '打开' : '关闭' }}</el-tag>
              <span> 如果打开，支持导出订单电子签收单</span>
            </el-descriptions-item>
            <el-descriptions-item label="查看价格强制登录开关">
              <el-tag :type="configInfo.getPriceMustLogin ? 'primary' : 'info'">{{ configInfo.getPriceMustLogin ? "打开" :
                "关闭"
                }}</el-tag>
              <span> 如果打开，会显示采购入口并且会显示相关经费卡字段，如项目下拉选择，订单金额会直接从项目经费卡额度绑定，只能在项目经费卡可用额度范围内才能提交采购；</span>
            </el-descriptions-item>
            <el-descriptions-item label="资产登记开关">
              <el-tag :type="configInfo.assetsSwitch ? 'primary' : 'info'">{{ configInfo.assetsSwitch ? "打开" : "关闭"
                }}</el-tag>
              <span> 如果打开，会显示在商城前台显示资产登记页面，关闭则隐藏该页面（目前只控制该页面是否显示，不影响固资建档功能本身）</span>
            </el-descriptions-item>
            <el-descriptions-item label="资产手动推送开关">
              <el-tag :type="configInfo.assetsManualSwitch ? 'primary' : 'info'">{{ configInfo.assetsManualSwitch ? "打开" : "关闭"
                }}</el-tag>
              <span> 如果打开，前台用户在个人中心订单详情中会出现推送固资的操作入口</span>
            </el-descriptions-item>
            <el-descriptions-item label="固定资产商品混合下单开关">
              <el-tag :type="configInfo.assetsMixSwitch ? 'primary' : 'info'">{{ configInfo.assetsMixSwitch ? "打开" : "关闭"
                }}</el-tag>
              <span> 如果打开，前台用户可以将固定资产的商品和非固定资产的商品一起下单；否则，固定资产的商品只能单独下单</span>
            </el-descriptions-item>
            <el-descriptions-item label="固定资产线下结算开关">
              <el-tag :type="configInfo.assetsOfflinePaySwitch ? 'primary' : 'info'">{{ configInfo.assetsOfflinePaySwitch ? "打开" : "关闭"
                }}</el-tag>
              <span> 如果打开，固定资产类的订单将不会推送到业财融合进行结算；</span>
            </el-descriptions-item>
            <el-descriptions-item label="自动开票申请开关">
              <el-tag :type="configInfo.autoInvoiceSwitch ? 'primary' : 'info'">{{ configInfo.autoInvoiceSwitch ? "打开" :
                "关闭"
                }}</el-tag>
              <el-tag v-if="configInfo.autoInvoiceSwitch" style="margin-left:10px;"> 订单完成 {{
                configInfo.autoInvoiceThreshold }}
                天后自动发送开票申请</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="账单发票校验开关">
              <el-tag :type="configInfo.verifyInvoice ? 'primary' : 'info'">{{ configInfo.verifyInvoice ?
                '打开' : '关闭' }}</el-tag>
              <span> 如果关闭，则在账单中可以添加未开发票的订单</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="扩展配置" name="tab3">
          <el-descriptions :column="2" :border="true">
            <el-descriptions-item :label="extItem.label" v-for="(extItem,extIndex) in comExtConfigItems" :key="extIndex" :labelStyle="{ width: '12%' }" :contentStyle="{ width: '38%' }">
              <template v-if="extItem.comType === 'input'"> 
                <span> {{ extItem.value || '--' }} </span>
              </template>
              <template v-if="extItem.comType === 'switch'"> 
                <el-tag :type="extItem.value ? 'primary' : 'info'">{{ extItem.value ?  '打开' : '关闭' }}</el-tag>
              </template>
              <span> {{ extItem.memo }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
      </el-tabs>

      
    </div>
  </div>
</template>

<script>
import { productFieldList, extConfigDics } from '@/views/mall/config/configUtil'
export default {
  name: 'MallBasisConfigView',
  props: {
    configInfo: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      activeTab: 'tab1'
    }
  },
  mounted() {
    this.readTabStatus()
  },
  computed: {
    comProductFieldList() {
      let arr = [...productFieldList]
      arr.forEach(item => {
        item.checked = this.configInfo.productField.includes(item.value)
      })

      return arr
    },
    comExtConfigItems() {
      let extConfigItems = [...extConfigDics]
      if(this.configInfo.extConfig) {
        let obj = JSON.parse(this.configInfo.extConfig)
        extConfigItems.forEach(item => {
          item.value = obj[item.key]
        })
      }
      return extConfigItems
    }
  },
  methods: {
    handleTabChange() {
      let key = 'jctmall-basis-config-tab'
      sessionStorage.setItem(key, this.activeTab)
    },
    readTabStatus() {
      let key = 'jctmall-basis-config-tab'
      this.activeTab = sessionStorage.getItem(key) || 'tab1'
    },
    editConfig() {
      this.$emit('edit', '1')
    }
  }

}
</script>

<style lang="scss" scoped>

.el-tag+.el-tag {
  margin-left: 10px;
}

</style>