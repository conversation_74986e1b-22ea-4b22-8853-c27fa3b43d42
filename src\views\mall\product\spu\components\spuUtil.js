import * as skuApi from "@/api/mall/product/sku.js"

export const fetchJDSkuInfo = async (skuId) => {
  let params = {
    skuId: skuId
  }
  let res1 = await skuApi.getJDSkuDetailInfo(params)
  let res2 = await skuApi.getJDSkuImageList(params)

  let result = {}
  if(res1 && res1.data) {
    result.introduce = '<p>' + res1.data.introduce + '</p>'
    let spuSpecs = []
    let categoryAttrList = res1.data.categoryAttrList
    if(categoryAttrList) {
      categoryAttrList.forEach(cateAtt => {
        let name = cateAtt.cateAttrName
        let value = cateAtt.cateAttrValList[0].join('')
        spuSpecs.push({
          specId: 0,
          specName: name,
          specValue: value
        })
      })
    }
    result.spuSpecs = spuSpecs
  }

  if(res2.data && res2.data.length) {
    let imageList = res2.data[0].skuImageList
    let jdImgPrefix = 'https://img13.360buyimg.com/n12/'
    imageList = imageList.filter(img => !img.isPrimary).map(img => jdImgPrefix + img.shortPath)
    result.imageList = imageList
  }

  return result
}
