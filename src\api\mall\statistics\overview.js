import request from '@/utils/request'

export function getUserCount(params) {
	return request({
		url: '/trade/statistics/overview/getUserCount' ,
		method: 'get',
		params
	})
}

export function getSupplierSellProportion(params) {
	return request({
		url: '/trade/statistics/overview/getSupplierSellProportion' ,
		method: 'get',
		params
	})
}

export function getSellCount(params) {
	return request({
		url: '/trade/statistics/overview/getSellCount' ,
		method: 'get',
		params
	})
}

export function getSellAmount(params) {
	return request({
		url: '/trade/statistics/overview/getSellAmount' ,
		method: 'get',
		params
	})
}

export function dailySaleSummary(data) {
	return request({
		url: '/trade/statistics/overview/dailySaleSummary' ,
		method: 'post',
		data
	})
}

export function dailySaleSummaryV2(data) {
	return request({
		url: '/trade/statistics/overview/v2/dailySaleSummary' ,
		method: 'post',
		data
	})
}

export function monthlySalesSummary(data) {
	return request({
		url: '/trade/statistics/overview/monthlySalesSummary' ,
		method: 'post',
		data
	})
}

export function monthlySalesSummaryV2(data) {
	return request({
		url: '/trade/statistics/overview/v2/monthlySalesSummary' ,
		method: 'post',
		data
	})
}

export function getTotalSaleV2(params) {
	return request({
		url: '/trade/statistics/overview/v2/getTotalSale' ,
		method: 'get',
		params
	})
}

export function getSupplierSaleV2(params) {
	return request({
		url: '/trade/statistics/overview/v2/getSupplierSale' ,
		method: 'get',
		params
	})
}

export function getProductCategoryProportion(params) {
	return request({
		url: '/product/statistics/overview/getProductCategoryProportion' ,
		method: 'get',
		params
	})
}


export function getSupplierProductProportion(params) {
	return request({
		url: '/product/statistics/overview/getSupplierProductProportion' ,
		method: 'get',
		params
	})
}

export function getSkuTotal(params) {
	return request({
		url: '/product/statistics/overview/getSkuTotal' ,
		method: 'get',
		params
	})
}