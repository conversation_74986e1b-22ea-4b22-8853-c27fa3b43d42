<template>
  <div class="app-container project-rule" v-loading="loading">
    <template v-if="detailData">
      <!-- 固资建档信息 -->
      <el-descriptions title="" :column="2" border>
        <el-descriptions-item label="规则名称">{{ detailData.name }} <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="detailData.status"/></el-descriptions-item>
        <el-descriptions-item label="规则生效时段">{{ parseTime(detailData.effectTime, '{y}-{m}-{d}') || '--' }} 至 {{ parseTime(detailData.expireTime, '{y}-{m}-{d}') || '--' }}</el-descriptions-item>
        <el-descriptions-item label="规则类型"><dict-tag :type="DICT_TYPE.MALL_PROJECT_RULE_TYPE" :value="detailData.ruleType"/></el-descriptions-item>
        <el-descriptions-item label="项目范围"><dict-tag :type="DICT_TYPE.MALL_PROJECT_SCOPE_TYPE" :value="detailData.projectScopeType"/></el-descriptions-item>
        <el-descriptions-item label="用户范围" v-if="detailData.ruleType === 3"><dict-tag :type="DICT_TYPE.MALL_USER_SCOPE_TYPE" :value="detailData.userScopeType"/></el-descriptions-item>
        <el-descriptions-item label="指定用户" v-if="detailData.ruleType === 3">{{ detailData.userScopeType !== 0 ? detailData.userScopeContent : '全部' }}</el-descriptions-item>
        <el-descriptions-item label="指定项目">{{ detailData.projectScopeType !== 0 ?  detailData.projectScopeContent : '全部' }}</el-descriptions-item>
        <el-descriptions-item label="商品范围" v-if="detailData.ruleType === 3"><dict-tag :type="DICT_TYPE.MALL_PRODUCT_SCOPE_TYPE" :value="detailData.productScopeType"/></el-descriptions-item>
        <el-descriptions-item label="指定商品" v-if="detailData.ruleType === 3 && detailData.productScopeType === 2">{{ detailData.productScopeContent || '--' }}</el-descriptions-item>
        <el-descriptions-item label="余额校验">{{ detailData.balanceValidate ? '打开' : '关闭' }} 授权余额值: {{ formatMoney(detailData.balanceThreshold) }}</el-descriptions-item>
        <el-descriptions-item label="额度校验">{{ detailData.balance2Validate ? '打开' : '关闭' }} 授权额度值: {{ formatMoney(detailData.balance2Threshold) }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(detailData.createTime) || '--' }}</el-descriptions-item>
      </el-descriptions>

      <el-descriptions style="margin-top: 30px;" title="商品范围-分类明细" :colon="false" direction="vertical" v-if="detailData.productScopeType === 1">
        <el-descriptions-item label="">
          <el-row :gutter="10" class="mb8">
            <el-col :span="24">
              <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="categoryOpen = true"
                        v-hasPermi="['mall:project-rule:create']">添加</el-button>
              <el-button type="primary" plain icon="el-icon-trash" size="mini" @click="handleDeleteItemAll"
                        v-hasPermi="['mall:project-rule:create']" :disabled="total === 0">全部删除</el-button>
              <el-button type="primary" plain icon="el-icon-trash" size="mini" @click="handleDeleteItemBatch"
                        v-hasPermi="['mall:project-rule:create']" :disabled="total === 0">批量删除</el-button>
              <el-button type="primary" plain icon="el-icon-trash" size="mini" @click="editStatus"
                        v-hasPermi="['mall:project-rule:create']" :disabled="total === 0">修改状态</el-button>
              <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="80px" style="display:inline-block">
                <el-form-item label="分类名称" prop="categoryName">
                  <el-input v-model="queryParams.categoryName" placeholder="请输入商品名称" clearable @keyup.enter.native="handleQuery"/>
                </el-form-item>
                <el-form-item label="商品分类" prop="categoryIds">
                  <category-select v-model="queryParams.categoryIds" :extProps="{checkStrictly: true}" placeholder="请选择商品分类" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
          <el-table :data="dataList" border @selection-change="handleSelectionChange" >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="categoryId" label="商品分类ID" width="150"></el-table-column>
            <el-table-column prop="categoryName" label="商品分类名称" width="200"></el-table-column>
            <el-table-column prop="fullCategoryId" label="商品分类ID路径"></el-table-column>
            <el-table-column prop="fullCategoryName" label="商品分类名称路径"></el-table-column>
            <el-table-column prop="status" label="状态">
              <template v-slot="scope">
                <el-tag :type="scope.row.status === 0 ? 'success' : 'warning'">{{ scope.row.status === 0 ? '允许购买' : '须补充材料' }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="150">
              <template v-slot="scope">
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDeleteItem(scope.row)"
                          v-hasPermi="['mall:project-rule:create']">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页组件 -->
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                      @pagination="loadProductCategorys"/>
        </el-descriptions-item>
      </el-descriptions>

      <category-dialog :open.sync="categoryOpen" @change="handleCategoryChange" :extProps="{checkStrictly: false,multiple: true}" :appendToBody="true"/>
    </template>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog title="修改状态" :visible.sync="open2" width="500px" append-to-body>
      <el-form ref="form2" :model="form2" :rules="rules2" label-width="80px">
        <el-form-item label="状态" prop="status" :rules="[{required:true, message: '请选择状态'}]">
          <el-radio-group v-model="form2.status">
            <el-radio :label="0">允许购买</el-radio>
            <el-radio :label="1">允许购买，须补充材料</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm2" :loading="submitLoading2">确 定</el-button>
        <el-button @click="open2 = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
  
<script>
import CategoryDialog from "@/views/mall/product/spu/components/category-dialog";
import * as api from "@/api/mall/project/projectRule.js"
import * as categoryApi from "@/api/mall/project/projectRuleCategory.js"
import CategorySelect from "@/views/mall/product/spu/components/category-select"
export default {
  name: "MallProjectRuleDetail",
  components: { CategoryDialog, CategorySelect },
  data() {
    return {
      loading: false,
      loading2: false,
      categoryOpen: false,
      multipleSelection: [],
      id: null,
      detailData: null,
      dataList: null,
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        ruleId: null,
        categoryName: null,
        categoryIds: []
      },
      total: 0,
      form2: {},
      rules2: {},
      submitLoading2: false,
      open2: false
    }
  },
  methods: {
    init(id) {
      this.id = id
      this.loadDetailData()
    },
    loadDetailData() {
      this.loading = true
      api.getProjectRule(this.id).then(res => {
        this.detailData = res.data
        this.loadProductCategorys()
      }).finally(() => {
        this.loading = false
      })
    },
    handleQuery() {
      this.queryParams.pageNo = 1
      this.loadProductCategorys()
    },
    loadProductCategorys() {
      if(this.detailData.productScopeType !== 1) {
        return
      }
      delete this.queryParams.fullCategoryId
      if(this.queryParams.categoryIds) {
        this.queryParams.fullCategoryId = this.queryParams.categoryIds.join('-')
      }
      this.loading2 = true
      this.queryParams.ruleId = this.id
      categoryApi.getProjectRuleCategoryPage(this.queryParams).then(res => {
        this.dataList = res.data.list;
        this.total = res.data.total;
      }).finally(() => {
        this.loading2 = false
      })
    },
    handleDeleteItem(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除吗?').then(function() {
        return categoryApi.deleteProjectRuleCategory(id);
      }).then(() => {
        this.loadProductCategorys();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleCategoryChange(ids, nodes) {
      console.log('category-change: ', ids, nodes)
      if(nodes) {
        let items = nodes.map(node => {
          return {
            ruleId: this.id,
            categoryId: node.data.categoryId,
            categoryName: node.data.categoryName,
            fullCategoryId: node.data.fullCategoryId,
            fullCategoryName: node.data.fullCategoryName,
            status: 0
          }
        })
        categoryApi.createProjectRuleCategoryBatch(items).then(res => {
            this.loadProductCategorys();
            this.$modal.msgSuccess("添加成功");
          }
        )
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleDeleteItemBatch() {
      if(!this.multipleSelection || !this.multipleSelection.length) {
        this.$modal.msgWarning("请选择至少一项");
        return
      }
      let ids = this.multipleSelection.map(item => item.id)
      this.$modal.confirm('是否确认删除吗?').then(function() {
        return categoryApi.deleteProjectRuleCategoryBatch(ids);
      }).then(() => {
        this.loadProductCategorys();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleDeleteItemAll() {
      let id = this.id
      this.$modal.confirm('是否确认删除吗?').then(function() {
        return categoryApi.deleteProjectRuleCategoryByRule(id);
      }).then(() => {
        this.loadProductCategorys();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    editStatus() {
      if(!this.multipleSelection || !this.multipleSelection.length) {
        this.$modal.msgWarning("请选择至少一项");
        return
      }
      this.open2 = true
      this.form2 = {
        status: null
      }
    },
    submitForm2() {
      this.$refs["form2"].validate(valid => {
        if (!valid) {
          return;
        }
        this.submitLoading2 = true
        let ids = this.multipleSelection.map(item => item.id)
        let params = {
          ids: ids,
          status: this.form2.status
        }

        // 添加的提交
        categoryApi.updateProjectRuleCategoryStatus(params).then(response => {
          this.$modal.msgSuccess("操作成功");
          this.open2 = false;
          this.loadProductCategorys();
        }).finally(() => {
          this.submitLoading2 = false
        });
      })
    }
  }
}
</script>
<style lang="scss">
.project-rule {
  .el-descriptions-item__label {
    word-break: keep-all;
  }
}
.el-cascader-menu__wrap {
  height: auto;
}
</style>
  
