<template>
  <div class="export-alert">
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      :show-close="false"
      width="35%"
      >
      <el-progress v-if="taskInfo.status === 0" :text-inside="true" :stroke-width="26" :percentage="taskInfo.progress"></el-progress>
      <div v-loading="taskInfo.status === 0"></div>
      <el-alert v-if="taskInfo.status === 1" title="导出成功" type="success" center show-icon :closable="false">
        <span>耗时： {{ timeCost }} </span>
        <el-link v-if="fileUrl" :href="fileUrl" type="primary" target="_blank" style="margin-left:20px;">点击下载结果</el-link>
        <span v-if="!fileUrl"> ：无数据 </span>
      </el-alert>
      <el-alert v-if="taskInfo.status === 2" title="导出失败" type="error" center show-icon :closable="false">
        服务器处理异常，请稍后重试！
      </el-alert>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" :loading="taskInfo.status === 0">{{ taskInfo.status === 0 ? '导出中...' : '关闭' }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import * as commonApi from '@/api/mall/common'
export default {
  name: "ExportAlert",
  data() {
    return {
      dialogVisible: false,
      title: '文件导出',
      taskId: null,
      taskInfo: {
        status: 0,
        progress: 0
      }
    }
  },
  computed: {
    fileUrl() {
      return this.taskInfo.downloadUrl
    },
    timeCost() {
      if(this.taskInfo.startTime && this.taskInfo.finishTime) {
        let diff = this.taskInfo.finishTime - this.taskInfo.startTime
        return (diff * 1.0 / 1000).toFixed(1) + 'S'
      }
      return 0
    }
  },
  methods: {
    reset() {
      this.taskInfo.status = 0
      this.taskInfo.progress = 0
    },
    init(exportFunc, params, title) {
      this.reset()
      this.dialogVisible = true
      this.title = title || '文件导出'

      exportFunc(params).then(res => {
        console.log('export-----', res)
        this.taskId = res.data
        this.taskInfo.status = 0
        this.pollingQuery()
      }).catch((e) => {
        this.taskInfo.status = 2
      })
    },
    pollingQuery() {
      let func = () => {
        this.loadTaskInfo()
        if(!this.checkFinish()) {
          setTimeout(func, 5000)
        }
      }
      this.taskInfo.progress = 10
      setTimeout(() => {
        func()
      }, 1000)
    },
    checkFinish() {
      return this.taskInfo.status !== 0
    },
    loadTaskInfo() {
      if(!this.taskId) {
        return
      }
      let params = {
        taskId: this.taskId,
        taskType: '1'
      }
      commonApi.getAsyncTaskInfo(params).then(res => {
        this.taskInfo = res.data || {}
      })
    }
  }

}
</script>

<style scoped lang="scss">
.export-alert {

}
</style>
