import * as mallUtil from '@/utils/mallUtil'

const orderStatusInfo = (val) => {
    return mallUtil.getOrderLabel(val)
}

const orderStatusStyle = (val) => {
  let item = mallUtil.ORDER_STATUS_LIST.find(item => item.value === val)
  return item ? item.style : ''
}

const orderAuditStatusInfo = (val) => {
    return mallUtil.getOrderAuditLabel(val)
}

const orderAuditStatusStyle = (val) => {
  let item = mallUtil.ORDER_AUDIT_STATUS_LIST.find(item => item.value === val)
  return item ? item.style : ''
}

const billStatusInfo = (val) => {
  return mallUtil.getBillStatusLabel(val)
}

const billStatusStyle = (val) => {
  let item = mallUtil.BILL_STATUS_LIST.find(item => item.value === val)
  return item ? item.style : ''
}

const orderBillStatusInfo = (val) => {
  return mallUtil.getOrderBillStatusLabel(val)
}

const orderBillStatusStyle = (val) => {
  let item = mallUtil.ORDER_BILL_STATUS_LIST.find(item => item.value === val)
  return item ? item.style : ''
}

const orderYcrhStatusInfo = (val) => {
  return mallUtil.getOrderYcrhStatusLabel(val)
}



const orderYcrhStatusStyle = (val) => {
  let item = mallUtil.ORDER_YCRH_STATUS_LIST.find(item => item.value.toString() === val)
  return item ? item.style : ''
}

const offlineSettlementInfo = (val) => {
  return mallUtil.getOfflineSettlementWayLabel(val)
}

const offlineSettlementWayStyle = (val) => {
  let item = mallUtil.SETTLEMENT_WAY_LIST.find(item => item.value === val)
  return item ? item.style : ''
}

const invoiceStatusInfo = (val) => {
  return mallUtil.getInvoiceStatusLabel(val)
}

const invoiceStatusStyle = (val) => {
  let item = mallUtil.INVOICE_STATUS_LIST.find(item => item.value === val)
  return item ? item.style : ''
}

const settlementWayStyle = (val) => {
  let item = mallUtil.SETTLEMENT_WAY_LIST.find(item => item.value === val)
  return item ? item.style : ''
}

const productSpecTypeInfo = (val) => {
  return mallUtil.getProductSpecTypeLabel(val)
}

const productSpecTypeStyle = (val) => {
  let item = mallUtil.PRODUCT_SPEC_TYPE.find(item => item.value === val)
  return item ? item.style : ''
}

const orderAssetStatusInfo = (val) => {
  return mallUtil.getOrderAssetStatusLabel(val)
}

const orderAssetStatusStyle = (val) => {
  let item = mallUtil.ORDER_ASSET_STATUS.find(item => item.value === val)
  return item ? item.style : ''
}

const ycrhAuditStatusInfo = (val) => {
  return mallUtil.getYcrhAuditLabel(val)
}

const ycrhAuditStatusStyle = (val) => {
  let item = mallUtil.YCRH_AUDIT_STATUS_LIST.find(item => item.value === val)
  return item ? item.style : ''
}

const orderItemAcceptStatusInfo = (val) => {
  return mallUtil.getOrderItemAcceptStatusLabel(val)
}

const orderItemAcceptStatusStyle = (val) => {
  let item = mallUtil.ORDER_ITEM_ACCEPT_STATUS.find(item => item.value === val)
  return item ? item.style : ''
}

const supplierFormStatusInfo = (val) => {
  return mallUtil.getSupplierFormStatusLabel(val)
}

const supplierFormStatusStyle = (val) => {
  let item = mallUtil.SUPPLIER_FORM_STATUS_LIST.find(item => item.value === val)
  return item ? item.style : ''
}

   
export default { orderStatusInfo, orderStatusStyle, orderAuditStatusInfo, orderAuditStatusStyle, 
  billStatusInfo, billStatusStyle, invoiceStatusInfo, invoiceStatusStyle, productSpecTypeInfo,
  productSpecTypeStyle, orderAssetStatusInfo, orderAssetStatusStyle, ycrhAuditStatusInfo, ycrhAuditStatusStyle,
  orderBillStatusInfo, orderBillStatusStyle, orderYcrhStatusInfo, orderYcrhStatusStyle,
  orderItemAcceptStatusInfo, offlineSettlementWayStyle, offlineSettlementInfo, orderItemAcceptStatusStyle,
  supplierFormStatusInfo, supplierFormStatusStyle}