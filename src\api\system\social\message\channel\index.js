import request from '@/utils/request'

// 创建三方消息通道
export function createSocialMessageChannel(data) {
  return request({
    url: '/system/social-message-channel/create',
    method: 'post',
    data: data
  })
}

// 更新三方消息通道
export function updateSocialMessageChannel(data) {
  return request({
    url: '/system/social-message-channel/update',
    method: 'put',
    data: data
  })
}

// 删除三方消息通道
export function deleteSocialMessageChannel(id) {
  return request({
    url: '/system/social-message-channel/delete?id=' + id,
    method: 'delete'
  })
}

// 获得三方消息通道
export function getSocialMessageChannel(id) {
  return request({
    url: '/system/social-message-channel/get?id=' + id,
    method: 'get'
  })
}

// 获得三方消息通道分页
export function getSocialMessageChannelPage(query) {
  return request({
    url: '/system/social-message-channel/page',
    method: 'get',
    params: query
  })
}

// 导出三方消息通道 Excel
export function exportSocialMessageChannelExcel(query) {
  return request({
    url: '/system/social-message-channel/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获得三方消息渠道精简列表
export function getSimpleSocialMessageChannels() {
  return request({
    url: '/system/social-message-channel/list-all-simple',
    method: 'get',
  })
}