<template>
  <div> 
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span class="title">基本信息</span>
        <!-- <dict-tag :type="DICT_TYPE.MALL_SUPPLIER_TYPE" :value="supplierInfo.type" /> -->
        <el-button style="float: right; padding: 3px 0" type="text" v-hasPermi="['mall:supplier-profile:update']" v-if="formMode === 'init'" @click="edit">修改</el-button>
      </div>
      <div v-if="supplierInfo.status !== 2" class="flex-center"> 
        <el-alert v-if="supplierInfo.status === 0" title="温馨提示" type="warning"
          description="请您完成基础设置中的各项配置，运营状态未启时，商品将无法搜索和下单。"
          show-icon>
        </el-alert>
        <el-alert v-if="supplierInfo.status === 2" title="温馨提示" type="warning"
          description="运营状态为已禁用，商品将无法搜索和下单，请联系平台运营。"
          show-icon>
        </el-alert>
      </div>
      <div v-if="formMode === 'init'" style="margin-top:10px;">
        <el-descriptions title="" :column="1" :border="true">
          <el-descriptions-item label="LOGO">
            <el-image v-if="supplierInfo.logoUrl" style="width: 200px;height:100px;" :src="supplierInfo.logoUrl" fit="fit"></el-image>
          </el-descriptions-item>
          <el-descriptions-item label="供应商全称">{{ supplierInfo.fullName || '--' }}</el-descriptions-item>
          <el-descriptions-item label="运营状态">
            <dict-tag :type="DICT_TYPE.MALL_SUPPLIER_STATUS" :value="supplierInfo.status" />
          </el-descriptions-item>
          <el-descriptions-item label="供应商简称">{{ supplierInfo.name || '--' }}</el-descriptions-item>
          <el-descriptions-item label="供应商编码">{{ supplierInfo.code || '--' }}</el-descriptions-item>
          <el-descriptions-item label="授权SKU数量">{{ supplierInfo.skuLimit || '无上限'}}</el-descriptions-item>
          <el-descriptions-item label="授权上架SKU数量">{{ supplierInfo.onSaleSkuLimit || '无上限' }}</el-descriptions-item>
          <el-descriptions-item label="联系人">{{ supplierInfo.contactName || '--' }}</el-descriptions-item>
          <el-descriptions-item label="联系人电话">{{ supplierInfo.contactMobile || '--' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <div style="width:800px;">
        <el-form v-if="formMode === 'edit'" ref="form" label-position="left" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="Logo" prop="logoUrl">
            <imageUpload v-model="form.logoUrl" :limit="1" :fileSize="1"/>
            <span>(尺寸: 200 * 100)</span>
          </el-form-item>
          <el-form-item label="联系人" prop="contactName">
            <el-input v-model="form.contactName" :maxlength="30" placeholder="请输入联系人姓名" />
          </el-form-item>
          <el-form-item label="联系人电话" prop="contactMobile">
            <el-input v-model="form.contactMobile" :maxlength="20" placeholder="请输入联系人电话" />
          </el-form-item>
          <el-form-item>
            <el-button type="default" @click="cancelForm">取 消</el-button>
            <el-button type="primary" @click="submitForm" :loading="submitLoading">提 交</el-button>
          </el-form-item>
        </el-form>
      </div>

    </el-card>
  </div>
</template>

<script>
import ImageUpload from '@/components/ImageUpload'
import * as api from '@/api/mall/supplier/supplier-profile'
export default {
  name: 'SupplierProfileBaseInfo',
  components: { ImageUpload },
  props: {
    supplierInfo: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      formMode: 'init',
      submitLoading: false,
      form: {
        logoUrl: '',
        contactName: '',
        contactMobile: ''
      },
      rules: {
        logoUrl: [
          { required: true, trigger: 'change', message: '请上传LOGO' }
        ],
        contactName: [
          { required: true, trigger: 'blur', message: '请输入联系人' }
        ],
        contactMobile: [
          { required: true, trigger: 'blur', message: '请输入联系人电话' }
        ],
      }
    }
  },
  methods: {
    edit() {
      this.formMode = 'edit'
      Object.keys(this.form).forEach(key => {
        this.form[key] = this.supplierInfo[key]
      })
    },
    cancelForm() {
      this.formMode = 'init'
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return
        }
        this.doSubmit()
      })
    },
    async doSubmit() {
      this.submitLoading = true
      try {
        let data = {...this.supplierInfo}
        Object.assign(data, this.form)
        await api.updateSupplierProfile(data)
        this.$modal.msgSuccess("修改成功")

        this.$emit('on-update')
        this.submitLoading = false
        this.formMode = 'init'
      } catch(e) {
        this.$modal.msgError("保存失败，请稍后重试")
        this.submitLoading = false
      }
    },
  }

}
</script>

<style>

</style>