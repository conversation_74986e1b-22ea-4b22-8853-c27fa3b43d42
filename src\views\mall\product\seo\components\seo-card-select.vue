<template>
  <div>
    <el-select 
    :value="selectedVal" 
    @input="onInput" 
    @change="handleChange"
    :placeholder="placeholder"
    :autoWidth="autoWidth"
    :autofocus="autofocus"
    :borderless="borderless"
    :clearable="clearable"
    :filterable="filterable"
    :multiple="multiple"
    :readonly="readonly"
    :size="size"
    style="width: 100%;">
      <el-option
        v-for="item in options"
        :key="item.id"
        :disabled="item.status === 1"
        :label="item.name"
        :value="item.id">
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { getSeoCardPage } from '@/api/mall/product/seoCard'
export default {
  name: 'ProductSeoCardSelect',
  props: {
    value: {
      type: [Array],
      default: () => null 
    },
    extParams: {
      type: Object,
      default:() => {}
    },
    placeholder: {
      type: String,
      default:() => '请输入名称搜索'
    },
    autoWidth: {
      type: <PERSON>olean,
      default: () => false
    },
    autofocus: {
      type: Boolean,
      default: () => false
    },
    borderless: {
      type: Boolean,
      default: () => false
    },
    clearable: {
      type: Boolean,
      default: () => true
    },
    filterable: {
      type: Boolean,
      default: () => true
    },
    multiple: {
      type: Boolean,
      default: () => true
    },
    readonly: {
      type: Boolean,
      default: () => false
    },
    size: {
      type: String,
      default: () => 'medium'
    }
  },
  data() {
    return {
      options: []
    }
  },
  computed: {
    selectedVal() {
      return this.value
    }
  },
  created() {
    this.loadOptions()
  },
  methods: {
    async loadOptions(keyword) {
      let params = Object.assign({
        pageSize: 100,
        status: 0
      }, this.extParams)
      if(keyword) {
        params.name = keyword
      }

      let res = await getSeoCardPage(params)
      if(res.code === 0) {
        res.data.forEach(element => {
          element.id = element.id
        });

        this.options = res.data || []
      }
    },
    onInput(val){
      this.$emit("input", val);
    },
    handleChange(val) {
      let opt = this.options.find(item => item.id === val)
      this.$emit("change", val, opt);
    },
    clickSelect() {
      if (this.options.length === 0) {
        this.loadOptions('')
      }
    }
  }
}
</script>

<style>

</style>