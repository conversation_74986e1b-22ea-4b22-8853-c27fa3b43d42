<template>
    <div class="app-container">

        <!-- 搜索工作栏 -->
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-width="68px">
            <el-form-item label="模板编码" prop="code">
                <el-input v-model="queryParams.code" placeholder="请输入模板编码" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="模板名称" prop="name">
                <el-input v-model="queryParams.name" placeholder="请输入模板名称" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="模板类型" prop="type">
                <el-select v-model="queryParams.type" placeholder="请选择模板类型" clearable size="small">
                    <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_SOCIAL_MESSAGE_TEMPLATE_TYPE)"
                        :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
                </el-select>
            </el-form-item>
            <el-form-item label="渠道编号" prop="channelId">
                <el-select v-model="queryParams.channelId" placeholder="请输入渠道编号" clearable size="small">
                    <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_SOCIAL_MESSAGE_CHANNEL)"
                        :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
                    <el-option v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)" :key="dict.value"
                        :label="dict.label" :value="dict.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="创建时间" prop="createTime">
                <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss"
                    type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                    :default-time="['00:00:00', '23:59:59']" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <!-- 操作工具栏 -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                    v-hasPermi="['system:social-message-template:create']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                    :loading="exportLoading" v-hasPermi="['system:social-message-template:export']">导出</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <!-- 列表 -->
        <el-table v-loading="loading" :data="list">
            <el-table-column label="编号" align="center" prop="id" />
            <el-table-column label="渠道编号" align="center" prop="channelCode">
                <template v-slot="scope">
                    <dict-tag :type="DICT_TYPE.SYSTEM_SOCIAL_MESSAGE_CHANNEL" :value="scope.row.channelCode" />
                </template>
            </el-table-column>
            <el-table-column label="模板类型" align="center" prop="type">
                <template v-slot="scope">
                    <dict-tag :type="DICT_TYPE.SYSTEM_SOCIAL_MESSAGE_TEMPLATE_TYPE" :value="scope.row.type" />
                </template>
            </el-table-column>
            <el-table-column label="开启状态" align="center" prop="status">
                <template v-slot="scope">
                    <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
                </template>
            </el-table-column>
            <el-table-column label="模板编码" align="center" prop="code" />
            <el-table-column label="模板名称" align="center" prop="name" />
            <el-table-column label="发送人名称" align="center" prop="nickname" />
            <el-table-column label="模板标题" align="center" prop="title" />
            <el-table-column label="模板内容" align="center" prop="content" />
            <el-table-column label="跳转链接" align="center" prop="url" />
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                <template v-slot="scope">
                    <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template v-slot="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['system:social-message-template:update']">修改</el-button>
                    <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['system:social-message-template:delete']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页组件 -->
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
            @pagination="getList" />

        <!-- 对话框(添加 / 修改) -->
        <el-dialog :title="title" :visible.sync="open" width="800px" v-dialogDrag append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="140px">
                <el-form-item label="渠道编号" prop="channelId">
                    <el-select v-model="form.channelId" placeholder="请选择渠道编号" clearable>
                        <el-option v-for="channel in channelOptions" :key="channel.id" :value="channel.id"
                            :label="getDictDataLabel(DICT_TYPE.SYSTEM_SOCIAL_MESSAGE_CHANNEL, channel.code)" />
                    </el-select>
                </el-form-item>
                <el-form-item label="模板类型" prop="type">
                    <el-select v-model="form.type" placeholder="请选择模版类型">
                        <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_SOCIAL_MESSAGE_TEMPLATE_TYPE)"
                            :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
                    </el-select>
                </el-form-item>
                <el-form-item label="模板编号" prop="code">
                    <el-input v-model="form.code" placeholder="请输入模板编码" />
                </el-form-item>
                <el-form-item label="模板名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入模板名称" />
                </el-form-item>
                <el-form-item label="发送人名称" prop="nickname">
                    <el-input v-model="form.nickname" placeholder="请输入发送人名称" />
                </el-form-item>
                <el-form-item label="模版标题" prop="title">
                    <el-input v-model="form.title" placeholder="请输入标题" />
                </el-form-item>
                <el-form-item label="模板内容" prop="content">
                    <el-input v-model="form.content" type="textarea" :rows="6" placeholder="请输入模板内容" />
                </el-form-item>
                <el-form-item label="跳转链接" prop="url">
                    <el-input v-model="form.url" placeholder="请输入跳转链接" />
                </el-form-item>

                <el-form-item label="开启状态" prop="status">
                    <el-radio-group v-model="form.status">
                        <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)" :key="dict.value"
                            :label="parseInt(dict.value)">{{ dict.label }}</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" placeholder="请输入备注" />
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { createSocialMessageTemplate, updateSocialMessageTemplate, deleteSocialMessageTemplate, getSocialMessageTemplate, getSocialMessageTemplatePage, exportSocialMessageTemplateExcel } from "@/api/system/social/message/template";
import { getSimpleSocialMessageChannels } from "@/api/system/social/message/channel";
import Editor from '@/components/Editor';

export default {
    name: "SocialMessageTemplate",
    components: {
        Editor
    },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 导出遮罩层
            exportLoading: false,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 三方消息模版列表
            list: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNo: 1,
                pageSize: 10,
                type: null,
                status: null,
                code: null,
                name: null,
                nickname: null,
                title: null,
                content: null,
                url: null,
                params: null,
                remark: null,
                createTime: [],
                title: null,
                url: null,
                channelId: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                type: [{ required: true, message: "模板类型不能为空", trigger: "change" }],
                status: [{ required: true, message: "开启状态不能为空", trigger: "blur" }],
                code: [{ required: true, message: "模板编码不能为空", trigger: "blur" }],
                name: [{ required: true, message: "模板名称不能为空", trigger: "blur" }],
                nickname: [{ required: true, message: "发送人名称不能为空", trigger: "blur" }],
                title: [{ required: true, message: "模版标题不能为空", trigger: "blur" }],
                content: [{ required: true, message: "模板内容不能为空", trigger: "blur" }],
                title: [{ required: true, message: "模版标题不能为空", trigger: "blur" }],
                channelId: [{ required: true, message: "渠道编号不能为空", trigger: "blur" }],
            },
            // 三方消息渠道
            channelOptions: []
        };
    },
    created() {
        this.getList();
        // 获得短信渠道
        getSimpleSocialMessageChannels().then(response => {
            this.channelOptions = response.data;
        });
    },
    methods: {
        /** 查询列表 */
        getList() {
            this.loading = true;
            // 执行查询
            getSocialMessageTemplatePage(this.queryParams).then(response => {
                this.list = response.data.list;
                this.total = response.data.total;
                this.loading = false;
            });
        },
        /** 取消按钮 */
        cancel() {
            this.open = false;
            this.reset();
        },
        /** 表单重置 */
        reset() {
            this.form = {
                id: undefined,
                type: undefined,
                status: undefined,
                code: undefined,
                name: undefined,
                nickname: undefined,
                title: undefined,
                content: undefined,
                url: undefined,
                params: undefined,
                remark: undefined,
                title: undefined,
                url: undefined,
                channelId: undefined,
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNo = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id;
            getSocialMessageTemplate(id).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (!valid) {
                    return;
                }
                // 修改的提交
                if (this.form.id != null) {
                    updateSocialMessageTemplate(this.form).then(response => {
                        this.$modal.msgSuccess("修改成功");
                        this.open = false;
                        this.getList();
                    });
                    return;
                }
                // 添加的提交
                createSocialMessageTemplate(this.form).then(response => {
                    this.$modal.msgSuccess("新增成功");
                    this.open = false;
                    this.getList();
                });
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const id = row.id;
            this.$modal.confirm('是否确认删除三方消息模版编号为"' + id + '"的数据项?').then(function () {
                return deleteSocialMessageTemplate(id);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => { });
        },
        /** 导出按钮操作 */
        handleExport() {
            // 处理查询参数
            let params = { ...this.queryParams };
            params.pageNo = undefined;
            params.pageSize = undefined;
            this.$modal.confirm('是否确认导出所有三方消息模版数据项?').then(() => {
                this.exportLoading = true;
                return exportSocialMessageTemplateExcel(params);
            }).then(response => {
                this.$download.excel(response, '三方消息模版.xls');
                this.exportLoading = false;
            }).catch(() => { });
        }
    }
};
</script>