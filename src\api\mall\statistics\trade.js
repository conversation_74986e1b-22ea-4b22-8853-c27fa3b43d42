import request from '@/utils/request'

export function getSellCount(params) {
	return request({
		url: '/trade/statistics/order/getSellCount' ,
		method: 'get',
		params
	})
}

export function getSellAmount(params) {
	return request({
		url: '/trade/statistics/order/getSellAmount' ,
		method: 'get',
		params
	})
}


export function getNotSettleAmount(params) {
	return request({
		url: '/trade/statistics/order/getNotSettleAmount' ,
		method: 'get',
		params
	})
}

export function getOrderSummary(params) {
	return request({
		url: '/trade/statistics/order/getOrderSummary' ,
		method: 'get',
		params
	})
}

export function getOrderSummaryByDept(params) {
	return request({
		url: '/trade/statistics/order/getOrderSummaryByDept' ,
		method: 'get',
		params
	})
}

export function getOrderSummaryByProject(params) {
	return request({
		url: '/trade/statistics/order/getOrderSummaryByProject' ,
		method: 'get',
		params
	})
}

export function getOrderSummaryBySupplier(params) {
	return request({
		url: '/trade/statistics/order/getOrderSummaryBySupplier' ,
		method: 'get',
		params
	})
}

export function getAfterSaleOrderSummary(params) {
	return request({
		url: '/trade/statistics/order/getAfterSaleOrderSummary' ,
		method: 'get',
		params
	})
}