import { getBasisConfig } from '@/api/mall/common'

const user = {
  namespaced: true,
  state: {
    basisConfig: {}, // 基础配置
    loading: false,
  },

  mutations: {
    SET_BASIS_CONFIG: (state, basisConfig) => {
      state.basisConfig = basisConfig
    },
    SET_LOADING: (state, loading) => {
      state.loading = loading
    }
  },

  actions: {
    // 加载配置
    async loadBasisConfig({ commit, state }) {
      if(state.loading) {
        return
      }
      commit('SET_LOADING', true)
      const res = await getBasisConfig()
      commit('SET_BASIS_CONFIG', res.data)
      commit('SET_LOADING', false)
    }
  }
}

export default user
