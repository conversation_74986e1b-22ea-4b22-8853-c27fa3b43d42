<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="订单号" prop="orderNo">
        <el-input v-model="queryParams.orderNo" placeholder="请输入订单号" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="物流单号" prop="num">
        <el-input v-model="queryParams.num" placeholder="请输入快递单号" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="签收标记" prop="isCheck">
        <el-select v-model="queryParams.isCheck" placeholder="请选择签收标记" clearable>
          <el-option :value="0" label="未签收"></el-option>
          <el-option :value="1" label="已签收"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <!-- 新增应该是后台获取数据 -->
      <!-- <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['trade:delivery:create']">新增</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['trade:delivery:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="订单号" align="center" prop="orderNo" width="190"/>
      <el-table-column label="物流单号" align="center" prop="num" width="180"/>
      <el-table-column label="物流公司" align="center" prop="name" />
      <el-table-column label="签收标记" align="center" prop="isCheck" :formatter="formatterCheck" />
      <el-table-column label="快递单状态" align="center" prop="state" :formatter="formatterState" />
      <el-table-column label="始发地" align="center" prop="fromName" show-overflow-tooltip />
      <el-table-column label="目的地" align="center" prop="toName" show-overflow-tooltip />
      <el-table-column label="当前位置" align="center" prop="curName" show-overflow-tooltip />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="{ row }">
          <div>{{ parseTime(row.createTime) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleDetail(scope.row)"
                      v-hasPermi="['trade:delivery:query']">查看</el-button>
          <!-- 数据都是后台同步的，应该不能修改 -->
          <!-- <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['trade:delivery:update']">修改</el-button> -->
          <!-- <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['trade:delivery:delete']">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="500px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="订单编号" prop="orderNo">
          <el-input v-model="form.orderNo" placeholder="请输入订单编号" readonly />
        </el-form-item>
        <el-form-item label="物流公司" prop="com">
          <el-select style="width: 300px;" v-model="form.com" placeholder="请选择公司名称">
            <el-option v-for="item in initCompanyList" :key="item.com" :label="item.name" :value="item.com"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="快递单号" prop="num">
          <el-input v-model="form.num" placeholder="请输入快递单号" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as api from "@/api/mall/trade/delivery"
import * as companyApi from "@/api/mall/trade/deliveryCompany"
export default {
  name: "TradeDelivery",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物流信息列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        orderNo: null,
        com: null,
        num: null,
        isCheck: null,
        state: null,
        subscribe: null,
        createTime: [],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        orderNo: [{ required: true, message: "订单编号不能为空", trigger: "blur" }],
        com: [{ required: true, message: "快递公司编码,一律用小写字母不能为空", trigger: "blur" }],
        num: [{ required: true, message: "快递单号不能为空", trigger: "blur" }],
        isCheck: [{ required: true, message: "是否签收标记，0未签收，1已签收，请忽略，明细状态请参考state字段不能为空", trigger: "blur" }],
        state: [{ required: true, message: "快递单当前状态，默认为0在途，1揽收，2疑难，3签收，4退签，5派件，8清关，14拒签等10个基础物流状态，如需要返回高级物流状态，请参考 resultv2 传值不能为空", trigger: "blur" }],
        subscribe: [{ required: true, message: "订阅状态，默认为0未订阅，1订阅不能为空", trigger: "blur" }],
      },
      initCompanyList: []
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      api.getDeliveryPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        orderNo: undefined,
        com: undefined,
        num: undefined,
        isCheck: undefined,
        state: undefined,
        subscribe: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加物流信息";
    },
    /** 修改按钮操作 */
    async handleUpdate(row) {
      this.reset();
      const id = row.id;
      api.getDelivery(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改物流信息";
      });
      const res = await companyApi.getDeliveryCompanyPage({
        pageSize: 500,
        pageNo: 1
      })
      if (res.code == 0) {
        this.initCompanyList = (res.data.list || [])
      }
    },
    handleDetail(row) {
      this.$router.push({
        name: 'DeliveryTrackDetail',
        query: {
          num: row.num,
          orderNo: row.orderNo,
        }
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        const company = this.initCompanyList.find(x => x.com === this.form.com) || {}
        this.form.name = company.name
        // 修改的提交
        if (this.form.id != null) {
          api.updateDelivery(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        api.createDelivery(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除物流信息编号为"' + id + '"的数据项?').then(function() {
          return api.deleteDelivery(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有物流信息数据项?').then(() => {
          this.exportLoading = true;
          return exportDeliveryExcel(params);
        }).then(response => {
          this.$download.excel(response, '物流信息.xls');
          this.exportLoading = false;
        }).catch(() => {});
    },
    formatterCheck(row) {
      const check = ['未签收', '已签收']
      return check[row.isCheck] || '--';
    },
    formatterState(row) {
      const state = ['在途', '揽收', '疑难', '签收', '退签', '派件', '', '', '清关', '', '', '', '', '', '拒签']
      return state[row.state] || '--';
    },
    formatterSub(row) {
      const sub = ['未订阅', '已订阅']
      return sub[row.subscribe] || '--';
    }
  }
};
</script>
