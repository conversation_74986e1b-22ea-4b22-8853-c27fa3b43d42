<template>
  <div class="app-container">
    <el-card>
    <el-tabs v-model="activeTab">
      <el-tab-pane label="业财融合集成配置" name="ycrh" v-hasPermi="['mall:ycrh-config']">
        <integrationYcrh></integrationYcrh>
      </el-tab-pane>
      <el-tab-pane label="统一登录集成配置" name="sso" v-hasPermi="['mall:sso-config']">
        <integrationSso></integrationSso>
      </el-tab-pane>
      <el-tab-pane label="固定资产集成配置" name="assets" v-hasPermi="['mall:assets-config']">
        <integrationAssets></integrationAssets>
      </el-tab-pane>
      <el-tab-pane label="审批流集成配置" name="bpm" v-hasPermi="['mall:bpm-config']">
        <integrationBpm></integrationBpm>
      </el-tab-pane>
      <el-tab-pane label="人事系统集成配置" name="hrms" v-hasPermi="['mall:hrms-config']">
        <integrationHrms></integrationHrms>
      </el-tab-pane>
    </el-tabs>
    </el-card>

  </div>
</template>

<script>
import integrationYcrh from './components/integration-ycrh.vue'
import integrationSso from './components/integration-sso.vue'
import integrationAssets from './components/integration-assets.vue'
import integrationBpm from './components/integration-bpm.vue'
import integrationHrms from './components/integration-hrms.vue'
export default {
  name: "MallConfigIntegration",
  components: { integrationYcrh, integrationSso, integrationAssets, integrationBpm, integrationHrms },
  data() {
    return {
      activeTab: 'ycrh',
    }
  },
  methods: {

  }
}
</script>

<style lang="scss" scoped></style>