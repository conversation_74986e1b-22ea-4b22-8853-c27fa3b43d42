<template>
  <div class="app-container" v-show="open">
    <el-dialog :title="title" :visible.sync="open" @close="close" width="900px">
      <div>
        <div class="stitle">已选标签</div>
        <div class="flex-start">
          <div v-for="tag in selectedList" :key="tag.id" class="tag-item">
            <el-tag closable  type="primary" @close="handleClose(tag)">
              {{ tag.name }}
            </el-tag>
          </div>
        </div>
      </div>
      <el-divider></el-divider>
      <div>
        <el-card class="box-card" header="可选列表">
          <div v-if="availableList && availableList.length">
            <div v-for="group in availableList" :key="group.id">
              <div class="group-name">{{ group.name }}</div>
              <div class="flex-start"> 
                <div v-for="item in group.tagList" :key="item.id" class="tag-item">
                  <el-tag type="success" effect="plain" @click="selectTag(item)">
                    {{ item.name }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
          <el-empty v-else description="可选内容为空"></el-empty>
        </el-card>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
        <el-button @click="close">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAllDictTag } from '@/api/mall/product/tag'
export default {
  name: 'TagDialog',
  data() {
    return {
      title: '选择标签',
      loading: false,
      selectedIds: [],
      submitLoading: false,
      open: false,
      groupTagData: []
    }
  },
  computed: {
    selectedList() {
      let arr = []
      if(this.groupTagData) {
        let itemList = []
        this.groupTagData.forEach(group => {
          itemList = itemList.concat(group.tagList)
        })

        console.log('itemlist----', itemList)
        this.selectedIds.forEach(id => {
          let obj = itemList.find(item => item.id === id)
          if(obj) arr.push(obj)
        })
      }
      return arr
    },
    availableList() {
      let list = JSON.parse(JSON.stringify(this.groupTagData || []))
      list.forEach(group => {
        let tags = group.tagList.filter(tag => !this.selectedIds.includes(tag.id))
        group.tagList = tags
      })
      return list.filter(group => group.tagList && group.tagList.length)
    }
  },
  methods: {
    show(ids= []) {
      this.open = true
      this.selectedIds = ids
      this.loadAllDictTag()
    },
    selectTag(tag) {
      if(this.selectedIds.includes(tag.id)) {
        return
      }
      this.selectedIds.push(tag.id)
    },
    handleClose(tag) {
      if(!this.selectedIds.includes(tag.id)) {
        return
      }

      let index = this.selectedIds.findIndex(id => id === tag.id)
      this.selectedIds.splice(index, 1)
    },
    loadAllDictTag() {
      getAllDictTag().then(res => {
        this.groupTagData = res.data || []
      })
    },
    submitForm() {
      if(!this.selectedList.length) {
        this.$modal.msgWarning("请选择标签")
        return
      }
      this.open = false
      this.$emit('on-update', this.selectedList)
    },
    close() {
      this.open = false
    }
  }

}
</script>

<style lang="scss" scoped>
.app-container {
  .stitle {
    margin-bottom: 15px;
  }
  .group-name {
    margin: 10px 0;
  }
  .el-divider {
    margin: 10px 0;
  }
  .flex-start {
    flex-wrap: wrap;
  }
  .tag-item {
    padding: 5px;
  }
}

</style>
