<template>
    <div class="app-container">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="60px">
        <el-form-item label="供应商" prop="supplierId">
          <supplier-select v-model="queryParams.supplierId" size="small" placeholder="请选择供应商" @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
  
      <el-table :data="tableData" size="mini" max-height="240" border fit>
        <el-table-column label="总订单数量" prop="num" min-width="80px" show-overflow-tooltip />
        <el-table-column label="订单总金额" prop="amount" min-width="80px" show-overflow-tooltip />
        <el-table-column label="已完成订单总数量" prop="num1" min-width="80px" show-overflow-tooltip />
        <el-table-column label="已完成订单总金额" prop="amount1" min-width="80px" show-overflow-tooltip />
        <el-table-column label="处理中订单总数量" prop="num2" min-width="80px" show-overflow-tooltip />
        <el-table-column label="处理中订单总金额" prop="amount2" min-width="80px" show-overflow-tooltip />
        <el-table-column label="已结算订单总数量" prop="num3" min-width="80px" show-overflow-tooltip />
        <el-table-column label="已结算订单总金额" prop="amount3" min-width="80px" show-overflow-tooltip />
      </el-table>
    </div>
  </template>
  
  <script>
  import * as api from "@/api/mall/statistics/trade";
  import SupplierSelect from "@/views/mall/product/spu/components/supplier-select";
  
  export default {
    components: {
      SupplierSelect
    },
    data() {
      return {
        // 查询参数
        queryParams: {
          supplierId: ''
        },
        tableData: []
      };
    },
    created() {
      if (this.$route.query.supplierId) {
        this.queryParams.supplierId = this.$route.query.supplierId
      }
      this.getList();
    },
    methods: {
      /** 搜索按钮操作 */
      handleQuery() {
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParams = {
          supplierId: ''
        }
        this.tableData = []
        this.getList();
      },
      getList() {
        this.tableData = []
        api.queryOrderStatisticsTotalVO({
          supplierId: this.queryParams.supplierId
        }).then(response => {
          if (response.code === 0) {
            const table = {
              amount: response.data.totalOrderAmount || 0,
              num: response.data.totalOrderNum || 0,
              amount1: 0,
              num1: 0,
              amount2: 0,
              num2: 0,
              amount3: 0,
              num3: 0,
            }
            // (1, "未确认"),
            // (2, "已确认"),
            // (3, "已发货"),
            // (4, "已送达"),
            // (5, "已签收"),
            // (6, "售后中"),
            // (7, "售后完成"),
            // (8, "已完成"),
            // (9, "已取消");
            // 21-已结算 20-待结算
            const list = response.data.orderDetail || []
            list.forEach(item => {
              if (item.status == 8) {
                table.num1 = item.orderNum
                table.amount1 = item.orderAmount
              }
              if ([1, 2, 3, 4, 5].includes(item.status)) {
                table.num2 += item.orderNum
                table.amount2 += item.orderAmount
              }
              if (item.status == 21) {
                table.num3 = item.orderNum
                table.amount3 = item.orderAmount
              }
            })
            this.tableData.push(table)
          }
        })
      }
    }
  };
  </script>
  
  <style lang="scss" scoped>
  </style>