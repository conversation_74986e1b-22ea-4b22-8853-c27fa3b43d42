import request from '@/utils/request'

// 创建商品过滤规则
export function createProductRule(data) {
  return request({
    url: '/mall/product-rule/create',
    method: 'post',
    data: data
  })
}

// 更新商品过滤规则
export function updateProductRule(data) {
  return request({
    url: '/mall/product-rule/update',
    method: 'put',
    data: data
  })
}

// 删除商品过滤规则
export function deleteProductRule(id) {
  return request({
    url: '/mall/product-rule/delete?id=' + id,
    method: 'delete'
  })
}

// 获得商品过滤规则
export function getProductRule(id) {
  return request({
    url: '/mall/product-rule/get?id=' + id,
    method: 'get'
  })
}

// 获得商品过滤规则分页
export function getProductRulePage(query) {
  return request({
    url: '/mall/product-rule/page',
    method: 'get',
    params: query
  })
}

// 导出商品过滤规则 Excel
export function exportProductRuleExcel(query) {
  return request({
    url: '/mall/product-rule/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
