<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="100px">
      <el-form-item label="供应商" prop="createTime">
        <supplier-select v-model="queryParams.supplierId" size="small" placeholder="请选择供应商" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="日期范围" prop="createTime">
        <el-date-picker v-model="queryParams.queryDate" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange" :picker-options="pickerOptions"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" @blur="blurDate" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div id="suppliers-echart" class="suppliers-echart"></div>
  </div>
</template>

<script>
import SupplierSelect from "@/views/mall/product/spu/components/supplier-select";
import { formatDate } from "@/utils/dateUtils";
import * as api from "@/api/mall/statistics/commodity";
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  components: {
    SupplierSelect
  },
  data() {
    const that = this
    return {
      // 查询参数
      queryParams: {
        queryDate: [],
        supplierId: '3'
      },
      pickerMinDate: '',
      pickerOptions: {  
        onPick: obj => {
          that.pickerMinDate = new Date(obj.minDate).getTime()
        },
        disabledDate(time) {  
          // 禁用今天和之后的日期，跨度不能超过7天
          if (new Date().toDateString() === time.toDateString()) {
            return true
          }
          if (that.pickerMinDate) {
            const day1 = 7 * 24 * 3600 * 1000 // 时间跨度不超过7天
            const minTime = that.pickerMinDate - day1
            const maxTime = that.pickerMinDate + day1
            return !(time.getTime() > minTime && time.getTime() < maxTime) || time.getTime() > Date.now()
          }
          return time.getTime() > Date.now()
        }  
      },
      chart: null
    };
  },
  mounted() {
    if (this.$route.query.supplierId) {
      this.queryParams.supplierId = this.$route.query.supplierId
    }
    // 近七天
    this.queryParams.queryDate = [formatDate(new Date(new Date().getTime() - 7 * 24 * 3600 * 1000), 'yyyy-MM-dd') + ' 00:00:00', formatDate(new Date(), 'yyyy-MM-dd') + ' 23:59:59']
    this.getList();
  },
  methods: {
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        queryDate: [],
        supplierId: ''
      }
      this.handleQuery();
    },
    getList() {
      if (!this.queryParams.supplierId) {
        this.$message.error('请选择供应商')
        this.closeEchart()
        return
      }
      if (this.queryParams.queryDate.length !== 2) {
        this.$message.error('请选择日期范围')
        this.closeEchart()
        return
      }
      api.supplierSkuSummary({
        supplierId: this.queryParams.supplierId,
        startTime: this.queryParams.queryDate[0],
        endTime: this.queryParams.queryDate[1]
      }).then(response => {
        if (response.code === 0) {
          if (response.data.length > 0) {
            this.initChart(response.data)
          } else {
            this.closeEchart()
            this.$message.info('当前范围内没有数据')
          }
        }
      })
    },
    initChart(data) {
      const chartDom = document.getElementById('suppliers-echart');
      this.chart = echarts.init(chartDom, 'macarons')
      this.chart.setOption({
        legend: {},
        tooltip: {},
        grid: {
          left: '1%',
          right: '1%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.map(x => x.date)
        },
        yAxis: {
          type: 'value'
        },
        series: [
          { 
            type: 'line',
            data: data.map(x => x.listingTotal)
          }
        ]
      })
    },
    blurDate() {
      this.pickerMinDate = ''
    },
    closeEchart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    }
  },
  beforeDestroy() {
    this.closeEchart()
  }
};
</script>

<style lang="scss" scoped>
.suppliers-echart {
  width: 100%;
  height: 400px;
}
</style>