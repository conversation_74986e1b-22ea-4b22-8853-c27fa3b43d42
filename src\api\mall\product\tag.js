import request from '@/utils/request'

// 删除分组
export function deleteGroup(id) {
  return request({
    url: '/product/tag-group/delete?id=' + id,
    method: 'delete'
  })
}

// 查询分组
export function getGroup(id) {
  return request({
    url: '/product/tag-group/get?id=' + id,
    method: 'get'
  })
}

// 查询分组分页列表
export function getGroupPage(query) {
  return request({
    url: '/product/tag-group/page',
    method: 'get',
    params: query
  })
}

// 新建分组
export function createGroup(data) {
  return request({
    url: '/product/tag-group/create' ,
    method: 'post',
    data: data
  })
}

// 修改分组
export function updateGroup(data) {
  return request({
    url: '/product/tag-group/update' ,
    method: 'put',
    data: data
  })
}

// 删除标签
export function deleteTag(id) {
  return request({
    url: '/product/tag/delete?id=' + id,
    method: 'delete'
  })
}

// 查询标签
export function getTag(id) {
  return request({
    url: '/product/tag/get?id=' + id,
    method: 'get'
  })
}

// 查询标签分页列表
export function getTagPage(query) {
  return request({
    url: '/product/tag/page',
    method: 'get',
    params: query
  })
}

// 新建标签
export function createTag(data) {
  return request({
    url: '/product/tag/create' ,
    method: 'post',
    data: data
  })
}

// 修改标签
export function updateTag(data) {
  return request({
    url: '/product/tag/update' ,
    method: 'put',
    data: data
  })
}

// 查询标签全量字典
export function getAllDictTag() {
  return request({
    url: '/product/sku/dict-tags',
    method: 'get'
  })
}