<template>
  <div class="app-container">
    <div class="left">
      <el-tree ref="tree" :accordion="accordion" :default-expanded-keys="treeData" :node-key="nodeKey" @node-click="handleNodeClick" :props="props" :load="loadNode" lazy></el-tree>
    </div>

    <div class="right">
      <div style="text-align: right;margin-bottom: 20px;">
        <el-button type="primary" @click="addCategory">新增分类</el-button>
      </div>
      <!-- 列表 -->
      <el-table v-loading="loading" :data="list" border>
        <el-table-column label="编码" align="center" prop="id" />
        <el-table-column label="名称" align="center" prop="categoryName" show-overflow-tooltip></el-table-column>
        <el-table-column label="排序" align="center" prop="orderSort"></el-table-column>
        <el-table-column fixed="right" label="操作" align="center" class-name="small-padding fixed-width">
          <template v-slot="scope">
            <el-button size="medium" type="text" @click="editCategory(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </div>
    <el-drawer
      title="新增分类"
      :visible.sync="visible"
      direction="rtl"
      size="1000px"
      class="cms-drawer"
      :before-close="handleClose">
      <div class="drawer-body-wrap">
        <el-form ref="form" class="form" :rules="rules" :model="form" size="small" :inline="false" label-width="100px">
          <el-form-item label="上级分类">
            <el-cascader ref="cascader" :show-all-levels="false" :props="cascaderProps" v-model="form.parentId" :options="options"></el-cascader>
          </el-form-item>
          <el-form-item label="分类名称" prop="categoryName">
            <el-input placeholder="请输入分类名称" v-model="form.categoryName" :maxlength="100" style="width: 210px;"></el-input>
          </el-form-item>
          <el-form-item label="分类編码" prop="categoryCode">
            <el-input placeholder="请输入分类编码" v-model="form.categoryCode" :maxlength="50" style="width: 210px;"></el-input>
          </el-form-item>
        </el-form>
        <div class="drawer__footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="confirmHandler">确 认</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
// import { queryCategoryList, saveCategory } from "@/api/system/cms/index.js"

export default {
  name: "CmsCategory",
  data() {
    return {
      nodeKey: 'id',
      accordion: true, // 每次只打开一个同级树节点展开
      props: {
        label: 'categoryName',
        children: 'childrenList',
        isLeaf: 'leaf',
        id: 'id'
      },
      treeData: [],
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      totalList: [],
      showList: [],
      list: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      visible: false,
      options: [],
      title: '新增分类',
      cascaderProps: {
        label: 'categoryName',
        children: 'childrenList',
        value: 'id',
        checkStrictly: true,
        emitPath: false
      },
      rules: {
        categoryName: [{ required: true, message: "分类名称不能为空", trigger: "blur" }],
      },
      form: {
        parentId: 0,
        categoryName: '',
        categoryCode: ''
      },
      editId: ''
    };
  },
  created() {
    this.queryCategoryList()
  },
  methods: {
    handleClose() {
      this.visible = false
      this.form = {
        value: 0,
        name: ''
      }
      this.editId = ''
    },
    confirmHandler() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let checkNodes = this.$refs.cascader.getCheckedNodes()
          const categoryLevel = 0
          if(checkNodes && checkNodes.length) {
            categoryLevel = checkNodes[0].level - 1
          } 
          if (this.editId) {
            this.editCategory(categoryLevel)
          } else {
            this.addCategory(categoryLevel)
          }
          this.handleClose()
          this.queryCategoryList()
        } else {
          return false;
        }
      });
    },
    async editCategory(categoryLevel) {
      const res = await saveCategory({
        id: this.editId,
        categoryName: this.form.categoryName,
        categoryCode: this.form.categoryCode,
        parentId: this.form.parentId,
        categoryLevel,
        status: 1
      })
      if (res.code === 0) {
        this.$message.success('修改分类成功')
      }
    },
    async addCategory(categoryLevel) {
      const res = await saveCategory({
        categoryName: this.form.categoryName,
        categoryCode: this.form.categoryCode,
        parentId: this.form.parentId,
        categoryLevel,
        status: 1
      })
      if (res.code === 0) {
        this.$message.success('创建分类成功')
      }
    },
    addCategory() {
      this.title = '新增分类'
      this.visible = true
    },
    editCategory(row) {
      this.title = '修改分类'
      this.form.parentId = row.parentId
      this.form.categoryName = row.categoryName
      this.form.categoryCode = row.categoryCode
      this.editId = row.id
      this.visible = true
    },
    handleNodeClick(data) {
      if (!data.leaf) {
        if (data.id !== 0) {
          this.showList = data.childrenList.filter(x => x.status)
        }
        this.getList()
      }
    },
    loadNode(node, resolve) {
      if (node.level === 0) {
        resolve([{categoryName: '全部分类', id: 0}])
        this.treeData.push(0)
      } else if (node.level === 1) {
        this.getCategoryList().then(res => {
          resolve(res)
        })
      } else {
        const arr = node.data.childrenList.filter(x => x.status).map(x => {
          if (x.childrenList && x.childrenList.length > 0) {
            x.leaf = false
          } else {
            x.leaf = true
          }
          return x
        })
        resolve(arr)
      }
    },
    /** 查询分类列表 */
    async getCategoryList() {
      // 执行查询
      const res = await queryCategoryList()
      if (res.code === 0) {
        return res.data.filter(x => x.status)
      }
      this.$message.error(res.msg)
      return []
    },
    /** 查询列表 */
    async queryCategoryList() {
      this.loading = true;
      const res = await queryCategoryList()
      if (res.code === 0 && res.data) {
        this.totalList = res.data.filter(x => x.status) || []
        this.showList = this.totalList
        this.options = [{
          id: 0,
          parentId: null,
          categoryName: '全部分类',
          childrenList: JSON.parse(JSON.stringify(res.data.filter(x => x.status)))
        }]
        this.forEachData(this.options)
        this.getList()
        this.total = Number(res.data ? res.data.length : 0)
      } else {
        this.$message.error(res.msg)
      }
      this.loading = false
    },
    forEachData(arr) {
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].childrenList && arr[i].childrenList.length > 0) {
          const flag = arr[i].childrenList.every(x => x.childrenList && x.childrenList.length === 0)
          if (flag) {
            delete arr[i].childrenList
          } else {
            this.forEachData(arr[i].childrenList)
          }
        } else {
          delete arr[i]
        }
      }
    },
    getList() {
      this.list = this.showList.slice((this.queryParams.pageNum - 1) * this.queryParams.pageSize, this.queryParams.pageNum * this.queryParams.pageSize)
    }
  }
};
</script>
<style lang="scss" scoped>
.app-container {
  display: flex;
  position: relative;
  background-color: #fff;
  .left {
    width: 300px;
    min-width: 300px;
    height: 100%;
    border: 1px solid #e9e4e4;
    background-color: #fafafa;
    overflow: auto;
    padding: 16px;
    .el-tree {
      background-color: #fafafa!important;
    }
  }
  .right {
    margin-left: 24px;
    flex: 1;
  }
}
.cms-drawer {
  :deep(.el-drawer__header) {
    font-size: 20px;
    font-weight: 600;
    border-bottom: 1px solid #eee;
    padding-bottom: 20px;
  }
  .drawer-body-wrap {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    .form {
      padding: 20px;
    }
    .drawer__footer {
      text-align: right;
      border-top: 1px solid #eee;
      padding: 20px;
    }
  }
}
</style>