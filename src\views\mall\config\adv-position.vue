<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="标题" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入标题" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="终端类型" prop="terminalType">
        <el-select v-model="queryParams.terminalType" placeholder="请选择终端类型" clearable>
          <el-option v-for="dict in terminalTypeDictDatas" :key="parseInt(dict.value)" :label="dict.label" :value="parseInt(dict.value)"/>
        </el-select>
      </el-form-item>
      <el-form-item label="业务类型" prop="bizType">
        <el-select v-model="queryParams.bizType" placeholder="请选择业务类型" clearable>
          <el-option v-for="dict in bizTypeDictDatas" :key="parseInt(dict.value)" :label="dict.label" :value="parseInt(dict.value)"/>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option v-for="dict in statusDictDatas" :key="parseInt(dict.value)" :label="dict.label" :value="parseInt(dict.value)"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['mall:adv-position:create']">新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="标题" align="center" prop="title" />
      <el-table-column label="图片" align="center" prop="imgUrl">
        <template v-slot="scope">
          <el-image style="width: 80px;" :src="scope.row.imgUrl" fit="contain"></el-image>
        </template>
      </el-table-column>
      <el-table-column label="跳转链接" align="center" prop="link" width="180"/>
      <el-table-column label="终端类型" align="center" prop="terminalType">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.MALL_ADV_POSITION_TERMINAL_TYPE" :value="scope.row.terminalType" />
        </template>
      </el-table-column>
      <el-table-column label="业务类型" align="center" prop="bizType">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.MALL_ADV_POSITION_BIZ_TYPE" :value="scope.row.bizType" />
        </template>
      </el-table-column>
      <el-table-column label="状态" key="status" align="center">
        <template v-slot="scope">
          <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1" @change="handleStatusChange(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sort" width="80"/>
      <el-table-column label="备注" align="center" prop="memo" show-overflow-tooltip />
      <el-table-column label="创建时间" align="center" prop="createTime">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['mall:adv-position:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['mall:adv-position:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="700px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" :maxlength="100" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="图片地址" prop="imgUrl">
          <imageUpload v-model="form.imgUrl" :limit="1" :fileSize="1"/>
          <div>轮播尺寸：700*470, 校园风采：800*450</div>
        </el-form-item>
        <el-form-item label="跳转链接" prop="link">
          <el-input v-model="form.link" :maxlength="255" placeholder="请输入跳转链接" />
        </el-form-item>
        <el-form-item label="终端类型" prop="terminalType">
          <el-radio-group v-model="form.terminalType">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.MALL_ADV_POSITION_TERMINAL_TYPE)"
                      :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="业务类型" prop="bizType">
          <el-radio-group v-model="form.bizType">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.MALL_ADV_POSITION_BIZ_TYPE)"
                      :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" placeholder="请输入排序" />
        </el-form-item>
        <el-form-item label="备注" :maxlength="255" prop="memo">
          <el-input v-model="form.memo" type="textarea" placeholder="请输入备注"/>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in statusDictDatas"
                      :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ImageUpload from '@/components/ImageUpload';
import * as api from "@/api/mall/config/advPosition";
import { getDictDatas, DICT_TYPE } from '@/utils/dict'

export default {
  name: "AdvPosition",
  components: {
    ImageUpload
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商城广告位列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        title: null,
        terminalType: null,
        bizType: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [{ required: true, message: "标题不能为空", trigger: "blur" }],
        imgUrl: [
          { required: true, message: "图片地址不能为空", trigger: "blur" }, 
          { type: 'url', message: "图片地址格式不正确", trigger: "blur" }
        ],
        link: [
          { type: 'url', message: "跳转链接格式不正确", trigger: "blur" }
        ],
        sort: [
          { required: true, message: "排序不能为空", trigger: "blur" }, 
          { type: 'number', message: "排序值不正确", trigger: "blur" }
        ],
        terminalType: [{ required: true, message: "终端类型不能为空", trigger: "change" }],
        bizType: [{ required: true, message: "业务类型不能为空", trigger: "change" }],
        status: [{ required: true, message: "状态不能为空", trigger: "blur" }],
      },
      // 数据字典
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS),
      terminalTypeDictDatas: getDictDatas(DICT_TYPE.MALL_ADV_POSITION_TERMINAL_TYPE),
      bizTypeDictDatas: getDictDatas(DICT_TYPE.MALL_ADV_POSITION_BIZ_TYPE)
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      api.getAdvPositionPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = parseInt(response.data.total);
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        title: undefined,
        imgUrl: undefined,
        link: undefined,
        terminalType: 1,
        bizType: 1,
        terminalType: 1,
        sort: 1,
        status: 0,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加商城广告位";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      api.getAdvPosition(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改商城广告位";
      });
    },
    // 状态修改
    handleStatusChange(row) {
      let text = row.status === 0 ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '"此广告位吗?').then(function() {
          let params = { id: row.id, status: row.status }
          return api.updateAdvPositionStatus(params);
        }).then(() => {
          this.$modal.msgSuccess(text + "成功");
        }).catch(function() {
          row.status = row.status === 1 ? 0: 1;
        });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.id != null) {
          api.updateAdvPosition(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        api.createAdvPosition(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除商城广告位编号为"' + id + '"的数据项?').then(function() {
          return api.deleteAdvPosition(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    }
  }
};
</script>
