<template>
  <div class="app-container" v-loading="loading">
    <template v-if="purchase">
      <div style="display: flex; justify-content: space-between; align-items: center; margin: auto auto 20px;">
        <div style="font-size: 16px; font-weight: bold;">采购单信息</div>
        <div style="float: right;"> 
          <el-button v-hasPermi="['trade:purchase:update']" v-if="purchase.status === 0 && purchase.auditStatus === -1" type="primary" size="small"
            @click="handleCancelPurchase">取消</el-button>
          <el-button v-hasPermi="['trade:purchase:update']" v-if="purchase.status === 0 && purchase.bpmNo" type="primary" size="small"
            @click="handleBpmStatus" :loading="loading2">更新审批状态</el-button>
        </div>
      </div>

      <!-- 采购单信息 -->
      <el-descriptions :column="3" border>
        <el-descriptions-item label="采购单状态">
          <!-- {{ purchase.id || '--' }}  -->
          <dict-tag :type="DICT_TYPE.MALL_PURCHASE_STATUS" :value="purchase.status"/>
        </el-descriptions-item>
        <template v-if="isBpm">
          <el-descriptions-item label="审批单号">{{ purchase.bpmNo || '--' }}
            <dict-tag :type="DICT_TYPE.MALL_PURCHASE_AUDIT_STATUS" :value="purchase.auditStatus" v-if="purchase.status === 0"/>
          </el-descriptions-item>
        </template>

        <el-descriptions-item label="采购人">{{ purchase.userName || '--' }}</el-descriptions-item>
        <el-descriptions-item label="采购原因">{{ purchase.purchaseReason || '--' }}</el-descriptions-item>
        <el-descriptions-item label="补充材料" v-if="attachments.length">
            <el-link v-for="(furl,findex) in attachments" :key="findex" :href="furl" type="primary" target="_blank">附件{{ findex + 1 }}</el-link>
          </el-descriptions-item>

        <template v-if="purchase.projectNo">
        <el-descriptions-item label="项目编号">{{ purchase.projectNo || '--' }}</el-descriptions-item>
        <el-descriptions-item label="项目名称">{{ purchase.projectName || '--' }}</el-descriptions-item>
        <el-descriptions-item label="项目类型">{{ purchase.projectType || '--' }}</el-descriptions-item>
        <el-descriptions-item label="项目负责人">{{ purchase.projectChargeName }} / {{ purchase.projectChargeNo }}</el-descriptions-item>
        <el-descriptions-item label="项目所属部门">{{ purchase.projectDepartmentName }} / {{ purchase.projectDepartmentNo }}</el-descriptions-item>
       </template>

        <el-descriptions-item v-if="purchase.accepterName" label="验收人">{{ purchase.accepterName }}</el-descriptions-item>
        <el-descriptions-item v-if="purchase.accepterMobile" label="验收人手机">{{ purchase.accepterMobile }}</el-descriptions-item>
        <el-descriptions-item v-if="purchase.accepterEmail" label="验收人邮箱">{{ purchase.accepterEmail }}</el-descriptions-item>

        <el-descriptions-item label="创建时间">{{ parseTime(purchase.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="取消时间">{{ purchase.cancelTime ? parseTime(purchase.cancelTime) : '--' }}</el-descriptions-item>
      </el-descriptions>

      <h3 v-if="isBpm && purchase.bpmNo" style="font-weight: bold">审批明细：</h3>
      <el-table style="margin-top: 12px;" :data="bpmResult" border v-if="isBpm && purchase.bpmNo">
        <el-table-column prop="approvalUserName" label="审批人">
          <template slot-scope="{ row }">
            <span v-if="row.approvalRoleName">【{{ row.approvalRoleName }}】</span>
            <span>{{ row.approvalUserName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="auditStatus" label="审批状态">
          <template slot-scope="{ row }">
            <el-tag :type="row.auditStatus | ycrhAuditStatusStyle">{{ row.auditStatus | ycrhAuditStatusInfo}}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="auditResult" label="审批备注"></el-table-column>
        <el-table-column prop="approvalTime" label="审批时间" />
      </el-table>


      <!-- 订单信息 -->
      <el-descriptions style="margin-top: 24px;" :colon="false" direction="vertical" :column="1">
        <template slot="title">
          <div class="description-title-1">订单明细</div>
        </template>
        <el-descriptions-item label="">
          <el-table ref="table" class="order-table" v-loading="loading" :data="orderList" row-key="no"
            default-expand-all :row-class-name="cellClass"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">

            <el-table-column label="订单编号" width="360">
              <template slot-scope="{ row }">
                <span v-if="row.children != undefined" class="orderCodeNum">
                  订单编号: {{ row.no || '--' }}
                </span>
                <div v-else class="goodsCard">
                  <div class="goodsCard-item" v-for="item in row.productList" :key="item.skuId">
                    <div class="goodsCard-item-img">
                      <img :src="item.picUrl" alt="" height="30" />
                    </div>
                    <div class="goodsCard-item-info">
                      <div class="goodsCard-item-info-name text-ellipsis" :title="item.skuName">{{ item.skuName || '--'
                        }}</div>
                      <div class="goodsCard-item-info-code">{{ item.skuId || '--' }}</div>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="单价" width="130px">
              <template slot-scope="{ row }">
                <div v-if="row.children != undefined">下单时间:</div>
                <div v-else class="unit-price" v-for="item in row.productList" :key="item.skuId">
                  <span class="unit-price-number">{{ formatMoney(item.skuPrice) }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="数量" width="170px">
              <template slot-scope="{ row }">
                <div v-if="row.children != undefined">
                  {{ parseTime(row.submitTime) }}
                </div>
                <div v-else class="unit-price" v-for="item in row.productList" :key="item.skuId">
                  x{{ item.count }}
                  <div style="display:inline-block;margin-left:5px;" v-if="item.afterSaleStatus">
                    <dict-tag :type="DICT_TYPE.TRADE_ORDER_ITEM_AFTER_SALE_STATUS" :value="item.afterSaleStatus" />
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="下单人" align="center">
              <template slot-scope="{ row }">
                <div v-if="!row.children">
                  <div>{{ row.userName }}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="收货人" align="center">
              <template slot-scope="{ row }">
                <div v-if="!row.children">
                  <div>{{ row.receiverName }}</div>
                  <div>{{ row.receiverMobile || '--' }}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="下单金额" align="center">
              <template slot-scope="{ row }">
                <div v-if="row.children != undefined">
                  <dict-tag :type="DICT_TYPE.MALL_ORDER_PLATFORM" :value="row.platform" />
                </div>
                <div v-if="row.children == undefined">
                  <div>{{ formatMoney(row.orderPrice) }}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="订单状态" align="center">
              <template slot-scope="{ row }">
                <div v-if="row.children == undefined">
                  <div class="status-box-">
                    <div class="status-ball" :style="{ backgroundColor: getStatusColor(row.status) }"></div>
                    <el-tag :type="row.status | orderStatusStyle">
                      {{ row.status | orderStatusInfo }} {{ row.userDeleted ? ": 用户删除" : "" }}
                    </el-tag>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="供应商" align="center">
              <template slot-scope="{ row }">
                <div>{{ row.supplierName || '--' }}</div>
              </template>
            </el-table-column>

            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template v-slot="scope">
                <el-button v-if="!scope.row.children" size="medium" type="text"
                  @click="jumpToOrderDetail(scope.row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>

        </el-descriptions-item>
      </el-descriptions>

    </template>
  </div>
</template>

<script>
import * as api from "@/api/mall/trade/purchase"
import { configMixins } from '@/views/mall/config/components/configMixin.js'
export default {
  name: "PurchaseDetail",
  mixins: [ configMixins ],
  data() {
    return {
      loading: false,
      purchase: null,
      orderList: [],
      total: 0,
      detailQueryParams: {},
      basisConfig: {},
      loading2: false
    };
  },
  computed: {
    attachments() {
      if(this.purchase) {
        let attachments = this.purchase.attachments || ''
        if(attachments) return attachments.split(',') || []
      }
      return []
    },
    bpmResult() {
      if(this.purchase) {
        return this.purchase.bpmResult
      }
      
      return {}
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getPurchaseDetail()
    },
    async getPurchaseDetail() {
      this.loading = true;
      const res = await api.getPurchaseDetail(this.$route.query.id || '');
      if (res.code === 0 && res.data) {
        res.data.orders = res.data.orders || []
        res.data.orders.forEach((item, index) => {
          item.children = [
            {
              id: item.id,
              no: item.no + index,
              trueNo: item.no,
              productList: item.items || [],
              productCount: item.productCount,
              userName: item.userName,
              receiverName: item.receiverName,
              productPrice: item.productPrice,
              orderPrice: item.orderPrice,
              status: item.status,
              submitTime: item.submitTime,
              updateTime: item.updateTime,
              userDeleted: item.userDeleted,
              platform: item.platform,
              receiverMobile: item.receiverMobile
            }
          ]
        });
        this.orderList = res.data.orders;
        delete res.data.orders;
        this.purchase = res.data;
      }
      this.loading = false;
    },
    getStatusColor(status) {
      if ([1, 2, 3, 6].includes(status)) {
        return '#FF9500';
      } else if (status === 9) {
        return '#E5E6EB';
      }
      return '#00B42A';
    },
    cellClass({ row }) {
      if (!row.hasOwnProperty('children')) {
        return 'disableheadselection';
      }
      return 'orderCode';
    },
    jumpToOrderDetail(row) {
      this.$router.push({ name: 'TradeOrderDetail', query: { id: row.id } });
    },
    handleCancelPurchase() {
      this.$confirm('确定取消采购单吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await api.cancelPurchase(this.$route.query.id || '');
        this.$modal.msgSuccess("采购单取消成功");
        this.init();
      }).catch(() => { });
    },
    handleBpmStatus() {
      let params = {
        id: this.purchase.id
      }
      this.loading2 = true
      api.pullBpmStatus(params).then(res => {
        this.$modal.msgSuccess("更新成功");
        this.init();
      }).finally(() => {
        this.loading2 = false
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.order-table {
  :deep(.el-table__expand-icon) {
    display: none;
  }

  :deep(.disableheadselection) {
    color: #8c8c8c;

    >td:first-child {
      >.cell {
        >.el-checkbox {
          display: none;
        }
      }
    }
  }

  :deep(.orderCode) {
    color: #333333;
    background-color: #f2f4f7;
  }

  .orderCodeNum {
    font-size: 12px;
    font-weight: bold;
    color: #333333;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .goodsCard {
    display: block;

    .goodsCard-item {
      display: flex;
      width: 100%;

      +.goodsCard-item {
        margin-top: 20px;
      }

      .goodsCard-item-img {
        position: relative;
      }

      .goodsCard-item-info {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-left: 8px;
        font-size: 12px;
        line-height: 20px;
        max-width: 300px;
        .goodsCard-item-info-name {
          width: 300px;
          color: #1d2129;
        }

        .goodsCard-item-info-code {
          color: #86909c;
        }
      }
    }
  }

  .unit-price {
    display: flex;
    align-items: center;
    height: 38px;

    +.unit-price {
      margin-top: 20px;
    }

    .unit-price-number {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .status-box {
    display: flex;
    align-items: center;
    justify-content: center;

    .status-ball {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      margin-right: 10px;
    }
  }
}

.description-title-1 {
  display: flex;
  align-items: center;
  justify-content: center;

  .el-form {
    margin-top: 18px;
  }
}
</style>