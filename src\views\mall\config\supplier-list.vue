<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="全称" prop="fullName">
        <el-input v-model="queryParams.fullName" placeholder="请输入全称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择供应商类型" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MALL_SUPPLIER_TYPE)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="供应商状态" clearable>
          <el-option v-for="dict in statusDictDatas" :key="parseInt(dict.value)" :label="dict.label" :value="parseInt(dict.value)"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="loadList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="list">
      <el-table-column label="ID" align="center" prop="id" width="100" />
      <el-table-column label="全称" align="center" prop="fullName" width="220"/>
      <el-table-column label="简称" align="center" prop="name" />
      <el-table-column label="联系人" align="center" prop="contactName" />
      <el-table-column label="联系人电话" align="center" prop="contactMobile" />
      <el-table-column label="类型" align="center" prop="type">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.MALL_SUPPLIER_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="SKU配额" align="center" prop="onSaleSkuLimit">
        <template v-slot="scope">
          <span v-if="scope.row.type !== 1">{{ scope.row.onSaleSkuLimit }}/{{ scope.row.skuLimit }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="运营状态" align="center" prop="status">
        <template v-slot="scope">
          <el-switch v-model="scope.row.status" active-color="#13ce66" :active-value="1" :inactive-value="2" @change="handleChangeStatus(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="接口状态" align="center" prop="openAppStatus">
        <template v-slot="scope">
          <el-switch v-if="scope.row.type !== 1" v-model="scope.row.openAppStatus" active-color="#13ce66" :active-value="0" :inactive-value="1" @change="handleChangeOpenAppStatus(scope.row)" />
          <el-tag v-else type="success">--</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sort" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">{{ scope.row.status === 0 ? '开始配置' : '修改配置' }}</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" v-if="[0,2].includes(scope.row.status)" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="loadList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="800px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="全称" prop="fullName">
          <el-input v-model="form.fullName" :maxlength="100" placeholder="请输入全称" />
        </el-form-item>
        <el-row> 
          <el-col :span="12"> 
            <el-form-item label="简称" prop="name">
              <el-input v-model="form.name" :maxlength="100" placeholder="请输入简称" />
            </el-form-item>
          </el-col>
          <el-col :span="12"> 
            <el-form-item label="编码" prop="code">
              <el-input v-model="form.code" maxlength="50" placeholder="请输入编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="Logo" prop="logoUrl">
          <imageUpload v-model="form.logoUrl" :limit="1" :fileSize="1"/>
        </el-form-item>
        <el-row> 
          <el-col :span="12"> 
            <el-form-item label="包邮金额" prop="freightThreshold">
              <el-input-number v-model="form.freightThreshold" :min="0" :max="1000" placeholder="请输入金额" />
            </el-form-item>
          </el-col>
          <el-col :span="12"> 
            <el-form-item label="邮费" prop="freight">
              <el-input-number v-model="form.freight" :min="0" :max="1000" placeholder="请输入邮费" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="起售金额" prop="saleAmountMin">
          <el-input-number v-model="form.saleAmountMin" :min="0" :max="1000" placeholder="请输入金额" />
        </el-form-item>
        <el-row> 
          <el-col :span="12"> 
            <el-form-item label="联系人" prop="contactName">
              <el-input v-model="form.contactName" :maxlength="50" placeholder="请输入联系人" />
            </el-form-item>
          </el-col>
          <el-col :span="12"> 
            <el-form-item label="联系人电话" prop="contactMobile">
              <el-input v-model="form.contactMobile" :maxlength="50" placeholder="请输入联系人电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row> 
          <el-col :span="12"> 
            <el-form-item label="客服电话" prop="servicePhone">
              <el-input v-model="form.servicePhone" :maxlength="50" placeholder="请输入客服电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12"> 
            <el-form-item label="客服名称" prop="serviceAgent">
              <el-input v-model="form.serviceAgent" maxlength="50" placeholder="请输入客服名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row> 
          <el-col :span="12"> 
            <el-form-item label="客服微信号" prop="serviceWechatId">
              <el-input v-model="form.serviceWechatId" maxlength="50" placeholder="请输入客服电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12"> 
            <el-form-item label="客服QQ号" prop="serviceQqId">
              <el-input v-model="form.serviceQqId" maxlength="50" placeholder="请输入退货地址" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="退货地址" prop="returnAddress">
          <el-input v-model="form.returnAddress" :maxlength="250" placeholder="请输入退货地址" />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.MALL_SUPPLIER_TYPE)"
                      :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import ImageUpload from '@/components/ImageUpload';
import * as api from "@/api/mall/config/supplier";
import { getDictDatas, DICT_TYPE } from '@/utils/dict'
export default {
  name: "MallConfigSupplierList",
  components: { ImageUpload },
  data() {
    return {
      showSearch: true,
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        type: undefined,
        fullName: undefined,
        status: undefined
      },
      loading: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      list: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        fullName: [{ required: true, message: "全称不能为空", trigger: "blur" }],
        name: [{ required: true, message: "简称不能为空", trigger: "blur" }],
        code: [
          { required: true, trigger: 'blur', message: '请输入编码' }
        ],
        freightThreshold: [
          { required: true, trigger: 'blur', message: '请输入包邮金额' }
        ],
        freight: [
          { required: true, trigger: 'blur', message: '请输入邮费' }
        ],
        saleAmountMin: [
          { required: true, trigger: 'blur', message: '请输入起售金额' }
        ],
        type: [{ required: true, message: "类型不能为空", trigger: "change" }]
      },
      // 数据字典
      statusDictDatas: getDictDatas(DICT_TYPE.MALL_SUPPLIER_STATUS),
      statusLoading: false,
    }
  },
  created () {
    this.handleQuery()
  },
  methods: {
    async loadList () {
      this.loading = true
      let res = await api.getSupplierList(this.queryParams)
      this.total = res.data.total
      this.list = res.data.list || []
      this.loading = false
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        fullName: undefined,
        name: undefined,
        code: undefined,
        freightThreshold: 59,
        freight: 6,
        saleAmountMin: 0,
        logoUrl: undefined,
        contactName: undefined,
        contactMobile: undefined,
        servicePhone: undefined,
        serviceAgent: '',
        serviceWechatId: '',
        serviceQqId: '',
        returnAddress: undefined,
        type: 1,
        status: 0,
        sort: 10
      };
      this.resetForm("form");
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加供应商";
    },
    handleUpdate (row) {
      this.$router.push({
        name: 'SupplierEdit',
        // 查询参数
        query: {
          id: row.id
        },
        meta: {
          title: row.name
        }
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.loadList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.id != null) {
          api.updateSupplier(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.loadList();
          });
          return;
        }
        // 添加的提交
        api.createSupplier(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.loadList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除供应商编号为"' + id + '"的数据项?').then(function() {
        return api.deleteSupplier(id);
      }).then(() => {
        this.loadList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    async handleChangeStatus(row) {
      if(this.statusLoading) {
        this.$modal.msgWarning("正在处理中，请稍后重试")
        row.status = [0,2,1][row.status]
        return
      }
      let params = {
        id: row.id,
        status: row.status
      }
      this.statusLoading = true
      try {
        await api.updateSupplierStatus(params)
        this.$modal.msgSuccess("操作成功，可能在两分钟左右生效")
      } catch(e) {
        row.status = [0,2,1][row.status]
      }
      this.statusLoading = false
    },
    async handleChangeOpenAppStatus(row) {
      let params = {
        supplierId: row.id,
        openAppStatus: row.openAppStatus
      }
      let res = await api.updateSupplierOpenAppStatus(params)
      if(res.code === 0) {
        this.$modal.msgSuccess("操作成功");
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>