import request from '@/utils/request'

// 创建物流发货信息
export function createDelivery(query) {
  return request({
    url: '/trade/delivery/create',
    method: 'get',
    params: query
  })
}

// 获得物流信息详情
export function getDelivery(id) {
  return request({
    url: '/trade/delivery/get?id=' + id,
    method: 'get'
  })
}

// 更新物流信息
export function updateDelivery(data) {
  return request({
    url: '/trade/delivery/update',
    method: 'put',
    data: data
  })
}

// 删除物流信息
export function deleteDelivery(id) {
  return request({
    url: '/trade/delivery/delete?id=' + id,
    method: 'delete'
  })
}

// 删除物流信息
export function deleteByOrder(orderNo) {
  return request({
    url: '/trade/delivery/delete-by-order?orderNo=' + orderNo,
    method: 'get'
  })
}

// 绑定订单对应的物流信息
export function productSend(data) {
  return request({
    url: '/trade/delivery/product-send',
    method: 'post',
    data
  })
}

// 无物流订单发货
export function serviceSend(data) {
  return request({
    url: '/trade/delivery/service-send',
    method: 'post',
    data
  })
}

export function selfSend(data) {
  return request({
    url: '/trade/delivery/self-send',
    method: 'post',
    data
  })
}

// 根据物流单号获得物流信息详情
export function getDeliveryDetailByNum(deliveryNum) {
  return request({
    url: '/trade/delivery/get-by-num?deliveryNum=' + deliveryNum,
    method: 'get'
  })
}

// 根据订单号获得物流信息详情
export function getDeliveryDetailByOrder(orderNo) {
  return request({
    url: '/trade/delivery/get-by-order?orderNo=' + orderNo,
    method: 'get'
  })
}
//根据物流单号和订单号获取物流信息想
export function getDeliveryDetailByNumAndOrder(deliveryNum, orderNo) {
  return request({
    url: '/trade/delivery/get-by-num-and-order?deliveryNum=' + deliveryNum + '&orderNo=' + orderNo,
    method: 'get'
  })
}

// 获得物流信息分页
export function getDeliveryPage(query) {
  return request({
    url: '/trade/delivery/page',
    method: 'get',
    params: query
  })
}

// 导出物流信息 Excel
export function exportDeliveryExcel(query) {
  return request({
    url: '/trade/delivery/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 下载导入模板
export function getImportTemplate() {
  return request({
    url: '/trade/delivery/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}
