<template>
    <div class="skuSellCountSupplierRanking" :style="{ height: height, width: width }" />
</template>

<script>
import * as api from "@/api/mall/statistics/commodity";
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '300px'
        }
    },
    data() {
        return {
            chart: null
        }
    },
    created() {
        this.getSupplierSellProductCountSummary();
    },
    methods: {
        getSupplierSellProductCountSummary() {
            // 执行查询
            api.getSupplierSellProductCountSummary({}).then(response => {
                if (response.code === 0) {
                    this.initChart(response.data)
                }
            });
        },
        initChart(data) {
            const formattedData = data.map(item => ({
                name: item.supplierName,
                value: item.skuSellTotal
            }));
            this.chart = echarts.init(this.$el, 'macarons')
            this.chart.setOption({
                title: {
                    text: '商品销量供应商排行',
                    top: 'top',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item'
                },
                series: [
                    {
                        name: '销量',
                        type: 'pie',
                        radius: '50%',
                        startAngle: 160,
                        data: formattedData,
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            })
        }
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    }
}
</script>