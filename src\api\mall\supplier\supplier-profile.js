import request from '@/utils/request'


// 查询当前供应商配置信息
export function getSupplierProfile() {
  return request({
    url: '/mall/supplier/profile/get',
    method: 'get'
  })
}

// 更新供应商配置
export function updateSupplierProfile(data) {
  return request({
    url: '/mall/supplier/profile/update',
    data: data,
    method: 'post'
  })
}

// 更新供应商配置运营状态
export function updateSupplierStatus(data) {
  return request({
    url: '/mall/supplier/profile/update-status',
    data: data,
    method: 'post'
  })
}

// 查询当前供应商的收款账号
export function getSupplierAccount() {
  return request({
    url: '/mall/supplier/profile/get-account',
    method: 'get'
  })
}


// 新增供应商收款账号
export function createSupplierAccount(data) {
  return request({
    url: '/mall/supplier/profile/create-account',
    method: 'post',
    data: data
  })
}

// 更新供应商收款账号
export function updateSupplierAccount(data) {
  return request({
    url: '/mall/supplier/profile/update-account',
    method: 'post',
    data: data
  })
}

// 查询当前供应商开放应用配置
export function getSupplierOpenApp(data) {
  return request({
    url: '/mall/supplier/profile/get-open-app',
    params: data,
    method: 'get'
  })
}

// 更新供应商开放应用配置
export function updateSupplierOpenApp(data) {
  return request({
    url: '/mall/supplier/profile/update-open-app',
    method: 'post',
    data: data
  })
}

// 刷新供应商开放应用密钥
export function refreshSupplierOpenAppSecret(data) {
  return request({
    url: '/mall/supplier/profile/refresh-open-app-secret',
    method: 'post',
    data: data
  })
}