<template>
  <div class="app-container" v-loading="loading">
     <!-- TODO 样式优化：表单宽度、表单项对齐、hr 粗细； -->
     <el-tabs v-model="activeName" class="tabs">
      <!-- 基础设置 -->
      <!-- TODO @luowenfeng：基础设置，分成基础信息、配送信息 -->
      <el-tab-pane label="基础设置" name="basic">
        <el-form ref="basic" :model="baseForm" :rules="rules" label-width="100px" style="width: 80%">
          <el-form-item label="供应商" prop="supplierId">
            <supplier-select v-model="baseForm.supplierId" :extParams="{status:1,typeList:[10,20]}" placeholder="请选择供应商" @change="handleSupplierChange" style="width: 400px"/>
          </el-form-item>
          <el-form-item label="商品品牌" prop="brandId">
            <brand-select v-model="baseForm.brandId" placeholder="请选择商品品牌" filterable @change="handleBrandChange" manualCreate style="width: 400px"/>
          </el-form-item>
          <el-form-item label="商品分类" prop="fullCategoryId">
            <span class="category-path">{{ baseForm.fullCategoryName }}</span> 
            <el-button size="small" type="text" text @click="categoryOpen = true">编辑分类</el-button>
          </el-form-item>
          <el-form-item label="商品名称" prop="spuName">
            <el-input v-model="baseForm.spuName" :maxlength="100" placeholder="请输入商品名称"/>
          </el-form-item>
          <el-form-item label="促销语" prop="sellPoint">
            <el-input v-model="baseForm.sellPoint" :maxlength="100" placeholder="请输入促销语，如免费上门服务，最新款手机，超蔳笔记本，可折叠笔记本"/>
          </el-form-item>
          <el-form-item label="单位" prop="unit" v-if="false">
            <el-input v-model="baseForm.unit" :maxlength="10" placeholder="请输入单位：台/个/匹/件"/>
          </el-form-item>
          <el-form-item label="商品主图" prop="picUrl">
            <ImageUpload v-model="baseForm.picUrl" :value="baseForm.picUrl" :limit="1" class="mall-image"/>
          </el-form-item>
          <el-form-item label="商品图册" prop="sliderPicUrls">
            <ImageUpload v-model="baseForm.sliderPicUrls" :value="baseForm.sliderPicUrls" :limit="8" sortable class="mall-image"/>
          </el-form-item>
          
          <el-form-item label="是否上架" prop="status">
            <el-radio-group v-model="baseForm.status">
              <el-radio :label="0">下架</el-radio>
              <el-radio :label="1">上架</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <!-- 商品通用属性 -->
      <el-tab-pane label="通用属性" name="spuSpec">
        <el-form ref="spuSpec" :model="spuSpecForm" :rules="rules" label-width="100px" style="width: 70%" class="spu-specs">
          <!-- 批量输入区域 -->
          <div style="margin: 0 0 20px 30px;">
            <el-button type="text" @click="showBatchInput = !showBatchInput">
              {{ showBatchInput ? '收起' : '批量输入' }}
            </el-button>
            <div v-if="showBatchInput" style="margin-top: 10px;">
              <el-input
                type="textarea"
                v-model="batchSpecInput"
                placeholder="请输入属性，格式：属性名:属性值 每行一个;例如：颜色:红色 回车换行 尺寸:XL 回车换行 材质:棉质"
                :rows="5"
                style="width: 700px;"
              />
              <div style="margin-top: 10px;">
                <el-button type="primary" size="small" @click="parseBatchSpecs">解析并添加</el-button>
                <el-button size="small" @click="batchSpecInput = ''">清空</el-button>
              </div>
            </div>
          </div>
          
          <!-- 内置属性specId大于0，自定义属性specId为0 -->
          <el-row :gutter="20" v-for="(spec, index) in spuSpecForm.specs" :key="index">
            <el-col :span="8">
              <el-form-item label="属性名称" :prop="'specs.' + index + '.specName'" :rules="[{required: true, trigger: 'blur'}]" :show-message="false">
                <el-input v-model="spec.specName" :maxlength="50" placeholder="请输入属性名称" :disabled="!!spec.specId" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="属性值" :prop="'specs.' + index + '.specValue'" :rules="[{required: true, trigger: 'blur'}]" :show-message="false">
                <el-input v-model="spec.specValue" :maxlength="100" placeholder="请输入属性值" />
              </el-form-item>
            </el-col>
            <el-col :span="1" v-if="!spec.specId || spec.specId === '0'">
              <el-button type="warning" icon="el-icon-delete" circle size="small" @click="removeSpuSpec(index)"/>
            </el-col>
          </el-row>
          <div style="padding: 10px 30px;">
            <el-button type="primary" icon="el-icon-plus" dashed size="small" @click="addSpuSpec">添加属性</el-button>
          </div>
        </el-form>
      </el-tab-pane>

      <!-- 价格库存 -->
      <!-- TODO @luowenfeng：rates=》priceStack 会更好哈 -->
      <el-tab-pane label="价格库存" name="rates" class="rates">
        <el-form ref="rates" :model="ratesForm" :rules="rules">
          <el-form-item label="启用多规格">
            <el-switch v-model="specType" :active-value="1" :inactive-value="0" @change="changeSpecType" :disabled="!specTypeCan"/>
          </el-form-item>
          <!-- 动态添加规格属性 -->
          <div v-show="specType === 1">
            <!-- 内置sku规格时 -->
            <div v-if="configSpecList && configSpecList.length">
              <el-select v-model="tmpSpecId" placeholder="请选择" size="small" :disabled="!specTypeCan">
                <el-option v-for="item in skuSpecList" :key="item.id" :label="item.name" :value="item.id"/>
              </el-select>
              <el-button type="primary" @click="addSpec" size="small" style="margin-left: 10px;" :disabled="!specTypeCan">添加规格项目</el-button>
            </div>
            <!-- 自定义sku规格时 -->
            <div v-if="!configSpecList || !configSpecList.length" class="spec-add">
              <div style="margin-right: 5px;">
                <el-input v-model.trim="tmpSpecName" placeholder="请输入规格项名称" size="small" :disabled="!specTypeCan"></el-input>
              </div>
              <el-button type="primary" @click="addSpec" size="small" style="margin-left: 10px;" :disabled="!specTypeCan">添加规格项目</el-button>
            </div>
            <div v-for="(specs, index) in dynamicSpec" :key="index" class="dynamic-spec">
              <div class="fitem">
                <!-- 删除按钮 -->
                <el-button type="warning" icon="el-icon-delete" size="small" circle @click="removeSpec(index)"/>
              </div>
              <div class="fitem spec-header">
                规格项：{{specs.specName}}
              </div>
              <div class="fitem spec-values">
                <div style="margin-right: 5px;">
                  <el-input v-model.trim="specs.tmpSpecValue" placeholder="请输入规格值" size="small"></el-input>
                </div>
                <el-button type="primary" size="small" @click="addConfigSpecValue(specs)">添加规格值</el-button>
                <div style="margin-left: 10px;">
                  <el-tag v-for="(tag, tindex) in specs.specValue" :key="tindex" :closable="!baseForm.id" type="primary" @close="removeSpecValue(specs, tindex)" style="margin:0 5px;">
                    {{tag}}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>

          <!-- 规格明细 -->
          <el-form-item label="规格明细">
            <el-table :data="ratesForm.rates" :border="true" style="width: 100%" ref="ratesTable">
              <el-table-column label="平台SKU" width="200px" prop="id" key="80" v-if=baseForm.id></el-table-column>
              <el-table-column label="外部SKU" width="200px" prop="skuInnerId" key="81" v-if="baseForm.supplierType !== 20 && baseForm.id"></el-table-column>
              <template v-if="this.specType">
                <el-table-column label="规格信息" width="140px">
                  <template v-slot="scope">
                    <span :key="index" v-for="(item, index) in dynamicSpec.filter(v => v.specName !== undefined)"> {{ scope.row.spec[index] }} </span>
                  </template>
                </el-table-column>
              </template>
              <el-table-column label="规格图片" width="150px" :render-header="addRedStar" key="90">
                <template v-slot="scope">
                  <ImageUpload v-model="scope.row.picUrl" :limit="1" :isShowTip="false" style="width: 120px; height: 50px" :disabled="!scope.row.status || specType === 0"/>
                </template>
              </el-table-column>
              <el-table-column label="市场价(元)" :render-header="addRedStar" key="92">
                <template v-slot="scope">
                  <el-form-item :prop="'rates.'+ scope.$index + '.marketPrice'" :rules="[{required: true, trigger: 'blur'}]">
                    <el-input-number v-model="scope.row.marketPrice" controls-position="right" :precision="2" :max="999999.99"  style="width:150px;" :disabled="!scope.row.status" />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="销售价(元)" :render-header="addRedStar" key="93">
                <template v-slot="scope">
                  <el-form-item :prop="'rates.'+ scope.$index + '.salePrice'"
                                :rules="[{required: true, trigger: 'blur'}]">
                    <el-input-number v-model="scope.row.salePrice" controls-position="right" :precision="2" :max="999999.99" :disabled="!!scope.row.id || !scope.row.status" style="width:150px;" />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="最低起售量" :render-header="addRedStar" key="94">
                <template v-slot="scope">
                  <el-form-item :prop="'rates.'+ scope.$index + '.lowestBuy'" :rules="[{required: true, trigger: 'blur'}]">
                    <el-input-number v-model="scope.row.lowestBuy" controls-position="right" :min="1" :max="999999" style="width:150px;" :disabled="!scope.row.status" />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="库存" :render-header="addRedStar" key="95">
                <template v-slot="scope">
                  <el-form-item :prop="'rates.'+ scope.$index + '.stock'" :rules="[{required: true, trigger: 'blur'}]">
                    <el-input-number v-model="scope.row.stock" controls-position="right" :min="0" :max="999999" :disabled="!!scope.row.id || !scope.row.status" style="width:150px;" />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="预警库存" :render-header="addRedStar" key="96">
                <template v-slot="scope">
                  <el-form-item :prop="'rates.'+ scope.$index + '.warnStock'" :rules="[{required: true, trigger: 'blur'}]">
                    <el-input-number v-model="scope.row.warnStock" controls-position="right" :min="0" :max="999999" :disabled="!!scope.row.id || !scope.row.status" style="width:150px;" />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="体积" key="97" v-if="false">
                <template v-slot="scope">
                  <el-input-number v-model="scope.row.volume" controls-position="right" :precision="2" :min="0" :max="999999.99" style="width:150px;" />
                </template>
              </el-table-column>
              <el-table-column label="重量" key="98" v-if="false">
                <template v-slot="scope">
                  <el-input-number v-model="scope.row.weight" controls-position="right" :precision="2" :min="0" :max="999999.99" style="width:150px;" />
                </template>
              </el-table-column>
              <template v-if="ratesForm.rates.length > 1">
                <el-table-column fixed="right" label="操作" width="100" key="100">
                  <template v-slot="scope">
                    <el-button size="small" v-if="!scope.row.id || isClone" @click="deleteRate(scope.$index)">删除</el-button>
                    <el-checkbox v-else v-model="scope.row.status" :true-label="1" :false-label="0">启用</el-checkbox>
                  </template>
                </el-table-column>
              </template>
            </el-table>
          </el-form-item>
          <el-form-item label="虚拟销量" prop="salesCount" v-if="false">
            <el-input-number v-model="baseForm.salesCount" placeholder="请输入虚拟销量" controls-position="right" :max="999999" />
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <!-- 商品详情 -->
      <el-tab-pane label="PC端商品详情" name="detail">
        <el-form ref="detail" :model="baseForm" :rules="rules">
          <el-form-item prop="description">
            <editor v-model="baseForm.description" :min-height="380"/>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <!-- 商品详情 -->
      <el-tab-pane label="H5端商品详情" name="detailH5">
        <el-form ref="detailH5" :model="baseForm" :rules="rules">
          <el-form-item prop="descriptionH5">
            <editor v-model="baseForm.descriptionH5" :min-height="380"/>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <!-- 销售设置 -->
      <el-tab-pane label="高级设置" name="senior" v-if="false">
        <el-form ref="senior" :model="baseForm" :rules="rules" label-width="100px" style="width: 95%">
          <el-form-item label="排序字段" style="width: 400px;">
            <el-input v-model="baseForm.sort" placeholder="请输入排序字段" oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"/>
          </el-form-item>
          <el-form-item label="是否展示库存" prop="showStock" v-if="false">
            <el-radio-group v-model="baseForm.showStock">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <div class="buttons" v-if="!isCreate">
      <el-button type="info" round @click="cancel" style="width:120px;margin-left:30px;">取消</el-button>
      <el-button type="success" round @click="submit" :loading="submitLoading" style="width:120px;margin-left:30px;">保存</el-button>
    </div>

    <div class="buttons" v-if="isCreate">
      <el-button type="info" round @click="stepClick('prev')" v-if="canPrev" style="width:120px;margin-left:30px;">上一步</el-button>
      <el-button type="success" round @click="stepClick('next')" :loading="submitLoading" style="width:120px;margin-left:30px;">{{ canNext ? '下一步' : '提交' }}</el-button>
    </div>

    <category-dialog :open.sync="categoryOpen" @change="handleCategoryChange"/>
  </div>
</template>

<script>
import * as spuApi from "@/api/mall/product/spu.js"
import * as spuUtil from "@/views/mall/product/spu/components/spuUtil.js"
import Editor from "@/components/Editor";
import ImageUpload from "@/components/ImageUpload";
import SupplierSelect from "@/views/mall/product/spu/components/supplier-select";
import BrandSelect from "@/views/mall/product/spu/components/brand-select";
import CategoryDialog from "@/views/mall/product/spu/components/category-dialog";
export default {
  name: "ProductForm",
  components: {
    Editor,
    ImageUpload,
    SupplierSelect,
    BrandSelect,
    CategoryDialog
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      categoryOpen: false,
      specType: 0,
      tabList: ['basic', 'spuSpec', 'rates', 'detail', 'detailH5'],
      activeName: "basic",
      // 基础设置
      tmpSpecId: null,
      tmpSpecName: "",
      updateSkus: null,
      showBatchInput: false,
      batchSpecInput: '',
      baseForm: {
        id: null,
        supplierId: null,
        supplierName: '',
        supplierType: null,
        brandId: null,
        brandName: '',
        spuName: null,
        sellPoint: null,
        fullCategoryId: '',
        fullCategoryName: '',
        category1Id: null,
        category2Id: null,
        category3Id: null,
        category1Name: '',
        category2Name: '',
        category3Name: '',
        sort: 0,
        unit: '',
        description: null,
        descriptionH5: null,
        picUrl: null,
        // 多张图片地址以逗号分隔
        sliderPicUrls: null,
        salePrice: 0,
        status: 0
      },
      // 通用属性
      spuSpecForm: {
        specs: []
      },
      // 价格库存
      ratesForm: {
        // 规格明细
        rates: [{}]
      },
      dynamicSpec: [],
      configSpecList: [],

      // 表单校验
      rules: {
        spuName: [{required: true, message: "商品名称不能为空", trigger: "blur"},],
        description: [{required: true, message: "PC端商品描述不能为空", trigger: "blur"},],
        fullCategoryId: [{required: true, message: "商品分类不能为空", trigger: "change"},],
        status: [{required: true, message: "商品状态不能为空", trigger: "change"}],
        supplierId: [{required: true, message: "供应商不能为空", trigger: "change"}],
        brandId: [{required: true, message: "商品品牌不能为空", trigger: "change"}],
        picUrl: [{required: true, message: "商品封面图片不能为空", trigger: "blur"}],
        sliderPicUrls: [{ required: true, message: "商品轮播图不能为空", trigger: "blur"}],
      },
    };
  },
  computed: {
    skuSpecList() {
      if(this.configSpecList) {
        return this.configSpecList.filter(item => item.specType === 2)
      }
      return []
    },
    spuSpecList() {
      if(this.configSpecList) {
        return this.configSpecList.filter(item => item.specType === 1)
      }
      return []
    },
    isClone() {
      return parseInt(this.$route.query.clone || 0) === 1
    },
    isJD() {
      return this.baseForm.supplierType === 1
    },
    specTypeCan() {
      return !this.baseForm.id || this.isClone
    },
    isCreate() {
      return !this.baseForm.id || this.isClone
    },
    canPrev() {
      return this.curTabIndex > 0
    },
    canNext() {
      return this.curTabIndex < this.tabList.length - 1
    },
    curTabIndex() {
      return this.tabList.findIndex(tab => tab === this.activeName) || 0
    }
  },
  watch: {
    'baseForm.picUrl': {
      handler(newVal) {
        if(!this.baseForm.id && this.specType === 0 && this.ratesForm.rates.length) {
          // 新增且为单规格时，sku的图片同spu的主图一样
          this.ratesForm.rates[0].picUrl = newVal
        }
      }
    },
    'specType': {
      handler(newVal) {
        if(!this.baseForm.id && newVal === 0 && this.ratesForm.rates.length) {
          // 新增且为单规格时，sku的图片同spu的主图一样
          this.ratesForm.rates[0].picUrl = this.baseForm.picUrl
        }
      }
    }
  },
  created() {
    let id = this.$route.params.spuId || this.$route.query.spuId
    if(id) {
      this.baseForm.id = id
      this.loadSpuDetail(id)
    } else {
      let sku = {
        status: 1
      }
      this.init4Sku(sku)
      this.ratesForm.rates = [sku]
    }
  },
  methods: {
    stepClick(direction) {
      if(direction === 'prev' && this.canPrev) {
        this.activeName = this.tabList[this.curTabIndex - 1]
        return
      }

      if(direction === 'next') {
        if(this.canNext) {
          this.$refs[this.activeName].validate((valid) => {
            if(!valid) {
              return
            }
            this.activeName = this.tabList[this.curTabIndex + 1]
            return
          })
        } else {
          this.submit()
        }
      }
    },
    resetCategoryIdNames() {
      this.baseForm.category1Id = null
      this.baseForm.category2Id = null
      this.baseForm.category3Id = null
      this.baseForm.category1Name = ''
      this.baseForm.category2Name = ''
      this.baseForm.category3Name = ''
      this.baseForm.fullCategoryId = ''
      this.baseForm.fullCategoryName = ''
    },
    handleSupplierChange(val, obj) {
      if(obj) {
        this.baseForm.supplierName = obj.name
        this.baseForm.supplierId = obj.id
      }
    },
    handleBrandChange(val, obj) {
      this.baseForm.brandName = obj ? obj.name : ''
    },
    async loadSpuDetail(id) {
      this.loading = true
      let res = await spuApi.getSpuDetail(id)
      if(res.code === 0) {
        Object.keys(this.baseForm).forEach(key => {
          this.baseForm[key] = res.data[key]
        })
        this.spuSpecForm.specs = res.data.spuSpecValueList
        this.specType = res.data.specType
        this.updateSkus = res.data.skus
        this.buildDynamicSpec(res.data.skus)
        this.cleanInalidaRates()
        this.fillForm4JDWhenClone()
      }
      this.loading = false
    },
    async fillForm4JDWhenClone() {
      if(!this.isClone || !this.isJD) {
        return
      }

      let skuId = this.updateSkus[0].skuInnerId
      let jdSkuDetail = await spuUtil.fetchJDSkuInfo(skuId)

      this.spuSpecForm.specs = jdSkuDetail.spuSpecs || []
      this.baseForm.sliderPicUrls = jdSkuDetail.imageList
      // this.baseForm.description = jdSkuDetail.introduce
    },
    handleCloneMode() {
      if(!this.isClone) {
        return
      }
      // 将相关ID擦除
      this.baseForm.id = undefined
      if(this.ratesForm.rates) {
        this.ratesForm.rates.forEach(rate => {
          rate.id = undefined
          rate.skuInnerId = undefined
          rate.spuId = undefined
        })
      }
    },
    handleCategoryChange(val, pathInfo) {
      console.log('category-change: ', val, pathInfo)
      this.resetCategoryIdNames()
      this.baseForm.fullCategoryId = val.join("-")
      this.baseForm.fullCategoryName = pathInfo.labels.join("/")
      if(val && val.length) {
        if(val.length >= 1) {
          this.baseForm.category1Id = pathInfo.ids[0]
          this.baseForm.category1Name = pathInfo.labels[0]
        }
        if(val.length >= 2) {
          this.baseForm.category2Id = pathInfo.ids[1]
          this.baseForm.category2Name = pathInfo.labels[1]
        } 
        if(val.length >= 3) {
          this.baseForm.category3Id = pathInfo.ids[2]
          this.baseForm.category3Name = pathInfo.labels[2]
        } 

        this.loadConfigSpecList(val[val.length - 1])
      }
    },
    /** 查询规格 */
    async loadConfigSpecList(categoryId) {
      let res = await spuApi.getProductCategorySpecs({productCategoryId: categoryId})
      if(res.code === 0) {
        this.configSpecList = res.data
        if(!this.baseForm.id) {
          this.spuSpecForm.specs = this.spuSpecList.map(item => {
            return {
              specId: item.id,
              specName: item.name,
              specValue: ''
            }  
          })
        }
        console.log('this.spuSpecForm.specs', this.spuSpecForm.specs)
      }
    },
    parseBatchSpecs() {
      if (!this.batchSpecInput.trim()) {
        this.$modal.msgWarning("请输入属性内容")
        return
      }
      
      const lines = this.batchSpecInput.trim().split('\n');
      const newSpecs = []
      
      for (let line of lines) {
        line = line.trim()
        if (!line) continue
        
        // 支持中文冒号（：）和英文冒号（:）
        let colonIndex = line.indexOf(':');
        if (colonIndex === -1) {
          colonIndex = line.indexOf('：');
        }
        if (colonIndex === -1) {
          this.$modal.msgWarning(`格式错误：${line}，请使用"属性名:属性值 换行"格式`);
          return;
        }
        
        const specName = line.substring(0, colonIndex).trim()
        const specValue = line.substring(colonIndex + 1).trim()
        
        if (!specName || !specValue) {
          this.$modal.msgWarning(`格式错误：${line}，属性名和属性值不能为空`);
          return
        }
        
        // 检查是否已存在相同属性名
        const existingIndex = this.spuSpecForm.specs.findIndex(spec => spec.specName === specName)
        if (existingIndex >= 0) {
          this.$modal.msgWarning(`属性"${specName}"已存在，请勿重复添加`)
          return;
        }
        
        newSpecs.push({
          specId: 0, // 自定义属性
          specName: specName,
          specValue: specValue
        })
      }
      
      // 添加到现有属性列表
      this.spuSpecForm.specs.push(...newSpecs)
      
      // 清空输入框并隐藏
      this.batchSpecInput = ''
      this.showBatchInput = false
      
      this.$modal.msgSuccess(`成功添加 ${newSpecs.length} 个属性`);
    },
    buildDynamicSpec(skus) {
      let dic = {}
      skus.forEach(sku => {
        let specValueList = sku.specValueList || []
        specValueList.forEach(specVal => {
          let key = specVal.specName
          if(!dic[key]) {
            dic[key] = {
              specId: specVal.specId,
              specName: specVal.specName,
              specValue: [specVal.specValue]
            }
          } else {
            let spec = dic[key]
            if(!spec.specValue.includes(specVal.specValue)) {
              spec.specValue.push(specVal.specValue)
            }
          }
        })
      })

      this.dynamicSpec = []
      for(let key in dic) {
        this.dynamicSpec.push(dic[key]); 
      }
      this.ratesForm.rates = []
      this.buildRatesFormRates();
    },
    fillRatesFormSku(skus) {
      if(!this.ratesForm.rates.length && skus.length) {
        this.ratesForm.rates = skus
        return
      }
      let func = (spec) => {
        return skus.find(sku => {
          if(sku.specValueList) {
            let arr1 = sku.specValueList?.map(item => item.specValue)
            return spec.toString() === arr1.toString()
          } else if(sku.spec) {
            return spec.toString() === sku.spec.toString()
          }
          return false
        })
      }
      this.ratesForm.rates.forEach((rate, index) => {
        let sku = func(rate.spec)
        if(sku) {
          Object.assign(rate, sku)
          sku.spec = rate.spec
        } else {
          sku = rate
          sku.status = 1
        }
        this.init4Sku(sku)
        this.$set(this.ratesForm.rates, index, sku)
      })
    },
    init4Sku(sku) {
      sku.lowestBuy = 1
      sku.stock = 100
      sku.warnStock = 10
    },
    cleanInalidaRates() {
      if(this.ratesForm.rates) {
        this.ratesForm.rates = this.ratesForm.rates.filter(rate => rate.id)
      }
    },
    deleteRate(index) {
      this.ratesForm.rates.splice(index, 1)
    },
    addSpuSpec() {
      this.spuSpecForm.specs.push({
        specId: 0,
        specName: '',
        specValue: ''
      })
    },
    removeSpuSpec(index) {
      this.spuSpecForm.specs.splice(index, 1);
    },
    removeSpec(index) {
      this.dynamicSpec.splice(index, 1);
      this.changeSpecType()
    },
    // 必选标识
    addRedStar(h, {column}) {
      return [
        h('span', {style: 'color: #F56C6C'}, '*'),
        h('span', ' ' + column.label)
      ];
    },
    changeSpecType() {
      this.$refs.ratesTable.doLayout();
      if (this.specType === 0) {
        if(!this.updateSkus) {
          let sku = {
            status: 1
          }
          this.init4Sku(sku)
          this.ratesForm.rates = [sku]
        } else {
          this.buildDynamicSpec(this.updateSkus)
        }
      } else {
        if (this.dynamicSpec.length > 0) {
          this.buildRatesFormRates()
        } else {
          this.ratesForm.rates = []
        }
      }
    },
    // 构建规格明细笛卡尔积
    buildRatesFormRates() {
      let rates = [];
      if(this.dynamicSpec.length) {
        this.dynamicSpec.map(v => v.specValue)
        .reduce((last, current) => {
          const array = [];
          last.forEach(par1 => {
            current.forEach(par2 => {
              let v
              // 当两个对象合并时，需使用[1,2]方式生成数组，而当数组和对象合并时，需使用concat
              if (par1 instanceof Array) {
                v = par1.concat(par2)
              } else {
                v = [par1, par2];
              }
              array.push(v)
            });
          });
          return array;
        })
        .forEach(v => {
          let spec = v;
          // 当v为单个规格项时，会变成字符串。造成表格只截取第一个字符串，而不是数组的第一个元素
          if (typeof v == 'string') {
            spec = Array.of(v)
          }
          rates.push({spec: spec, status: 1, name: Array.of(v).join()})
        });
      }
      
      let filledRates = this.ratesForm.rates
      this.ratesForm.rates = rates
      console.log('rates====', this.ratesForm.rates)
      if(this.updateSkus) {
        this.fillRatesFormSku(this.updateSkus);
      } else if(filledRates) {
        this.fillRatesFormSku(filledRates);
      }
    },
    // 取消按钮
    cancel() {
      var currentView = this.$store.state.tagsView.visitedViews[0]
      for (currentView of this.$store.state.tagsView.visitedViews) {
        if (currentView.path === this.$route.path) {
          break
        }
      }
      this.$store.dispatch('tagsView/delView', currentView).then(() => {
        const obj = { path: "/product/spu", name: "ProductSpu" };
        this.$tab.closeOpenPage(obj).then(() => {
          this.$tab.refreshPage();
        })
      })
    },
    submit() {
      this.$refs[this.activeName].validate((valid) => {
        console.log('baseForm:', this.baseForm)
        if (!valid) {
          return
        }
        if(this.specType === 0 && this.ratesForm.rates.length > 1) {
          this.$message.warning('单规格只允许一个SKU')
          return
        }
        this.handleCloneMode()
        let rates = JSON.parse(JSON.stringify(this.ratesForm.rates))
        // 价格元转分
        rates.forEach(r => {
          r.marketPrice = r.marketPrice * 1
          r.salePrice = r.salePrice * 1
        })

        // 动态规格调整字段
        if (this.specType) {
          let minSalePrice = rates[0].salePrice
          rates.forEach(r => {
            if(minSalePrice < r.salePrice) {
              minSalePrice = r.salePrice
            }
            let properties = []
            Array.of(r.spec).forEach(s => {
              let obj
              if (s instanceof Array) {
                obj = s
              } else {
                obj = Array.of(s)
              }
              obj.forEach((v, i) => {
                let propertie = {}
                propertie.specId = this.dynamicSpec[i].specId
                propertie.specName = this.dynamicSpec[i].specName
                propertie.specValue = v
                properties.push(propertie)
              })
            })
            r.specValueList = properties
            r.skuName = this.baseForm.spuName
            delete r.spec
          })
          this.baseForm.salePrice = minSalePrice
        } else {
          rates[0].skuName = this.baseForm.spuName
          rates[0].status = this.baseForm.status
          this.baseForm.salePrice = rates[0].salePrice
        }
        let form = {...this.baseForm}
        form.spuSpecValueList = this.spuSpecForm.specs
        
        if (form.sliderPicUrls && !(form.sliderPicUrls instanceof Array)) {
          if(form.sliderPicUrls.indexOf(',')) {
            form.sliderPicUrls = form.sliderPicUrls.split(',')
          } else {
            form.sliderPicUrls = Array.of(form.sliderPicUrls)
          }
        } 
        form.skus = rates;
        form.specType = this.specType;

        this.submitLoading = true
        if (form.id == null) {
          spuApi.createSpu(form).then(() => {
            this.$modal.msgSuccess("新增成功");
          }).then(()=>{
            this.cancel();
          }).finally(() => {
            this.submitLoading = false
          })
        } else {
          spuApi.updateSpu(form).then(() => {
            this.$modal.msgSuccess("修改成功");
          }).then(()=>{
            this.cancel();
          }).finally(() => {
            this.submitLoading = false
          })
        }
      });

    },
    // 添加规格项目
    addSpec() {
      let spec;
      // 判断是否为内置规格项
      if(this.configSpecList && this.configSpecList.length) {
        if(!this.tmpSpecId) {
          this.$modal.msgWarning("请选择规格");
          return
        }

        let configSepc = this.skuSpecList.find(item => item.id === this.tmpSpecId)
        if(!configSepc) {
          this.$modal.msgWarning("所选规格无效，请重试");
          return
        }

        if(this.dynamicSpec) {
          let index = this.dynamicSpec.findIndex(item => item.specName === configSepc.name)
          if(index >= 0) {
            this.$modal.msgWarning("规格不能重复");
            return
          }
        }
        spec = configSepc
        this.tmpSpecId = null
      } else {
        if(!this.tmpSpecName) {
          this.$modal.msgWarning("请输入规格项名称");
          return
        }

        if(this.dynamicSpec) {
          let index = this.dynamicSpec.findIndex(item => item.specName === this.tmpSpecName)
          if(index >= 0) {
            this.$modal.msgWarning("规格不能重复");
            return
          }
        }
        spec = {
          id: 0,
          name: this.tmpSpecName
        }
        this.tmpSpecName = null
      }
      
      this.dynamicSpec.push({
        specId: spec.id,
        specName: spec.name,
        specValue: []}); 
      this.ratesForm.rates = []
      
      this.buildRatesFormRates();
    },
    removeSpecValue(spec, index) {
      spec.specValue.splice(index, 1);
      this.changeSpecType();
    },
    addConfigSpecValue(spec) {
      if(spec.tmpSpecValue) {
        let counts = 0;
        this.dynamicSpec.forEach(ditem => {
          counts += ditem.specValue.filter(sitem => sitem === spec.tmpSpecValue).length
        })
        if(counts >= 1) {
          this.$modal.msgWarning("规格值不能重复");
          return
        }
        spec.specValue.push(spec.tmpSpecValue)
        spec.tmpSpecValue = ''
        this.buildRatesFormRates();
      }
    }
  },
};
</script>

<style lang="scss">
.app-container{
  padding: 20px;
  min-height: 800px;
  width: 100%;

  .spec-add {
    display: flex;
    align-items: center;
    justify-content: start;
  }

  .spu-specs {
    margin: 10px 0 20px;
    .el-form-item {
      margin-bottom: 5px;;
    }
  }

  .category-path {
    margin: 0 10px;
    color: #4c5067fa;
    font-weight: 500;
  }

  .dynamic-spec {
    display: flex;
    align-items: center;
    justify-content: start;
    background-color: #80808038;
    border-radius: 5px;
    margin: 10px 0;
    padding: 8px;
    .fitem {
      margin: 0 10px;
    }
    .fitem:first {
      margin: 0;
    }
    .spec-header {
      width: 120px;
      word-break: break-all;
    }
    .spec-values {
      display: flex;
      align-items: center;
      justify-content: start;
    }
  }

  .mall-image {
    .el-upload--picture-card {
      width: 80px;
      height: 80px;
      line-height: 90px;
    }

    .el-upload-list__item {
      width: 80px;
      height: 80px;
    }
  }
  // 库存价格图片样式修改
  .rates {
    .component-upload-image {
      margin: auto;
    }

    .el-upload--picture-card {
      width: 100px;
      height: 50px;
      line-height: 60px;
      margin: auto;
    }

    .el-upload-list__item {
      width: 100px !important;
      height: 50px !important;
    }
  }
  
}
</style>
