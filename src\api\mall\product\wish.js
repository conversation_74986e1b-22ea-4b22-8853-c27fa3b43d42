import request from '@/utils/request'


// 删除心愿单
export function deleteWish(id) {
  return request({
    url: '/product/wish/delete?id=' + id,
    method: 'delete'
  })
}

// 获得心愿单
export function getWish(id) {
  return request({
    url: '/product/wish/get?id=' + id,
    method: 'get'
  })
}

// 获得心愿单分页
export function getWishPage(query) {
  return request({
    url: '/product/wish/page',
    method: 'get',
    params: query
  })
}

// 导出心愿单 Excel
export function exportWishExcel(query) {
  return request({
    url: '/product/wish/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 修改心愿单状态
export function updateWishStatus(data) {
  return request({
    url: '/product/wish/update-status' ,
    method: 'post',
    data: data
  })
}