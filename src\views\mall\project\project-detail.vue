<template>
  <div class="app-container project-rule" v-loading="loading">
    <template v-if="detailData">
      <!-- 固资建档信息 -->
      <el-descriptions title="" :column="1" border>
        <el-descriptions-item label="项目名称编号">名称：{{ detailData.name }}  编号： {{ detailData.no }} </el-descriptions-item>
        <el-descriptions-item label="项目类型名称代码">名称：{{ detailData.typeName }}  代码：{{ detailData.typeCode }} </el-descriptions-item>
        <el-descriptions-item label="负责人姓名工号">姓名：{{ detailData.chargeName }}  工号：{{ detailData.chargeNo }} </el-descriptions-item>
        <el-descriptions-item label="部门名称编号">名称：{{ detailData.departmentName }}  编号：{{ detailData.departmentNo }} </el-descriptions-item>
        <el-descriptions-item label="余额">{{ formatMoneyV2(balance || 0) }} </el-descriptions-item>
        <el-descriptions-item label="一级大类">{{ detailData.rootClass }} </el-descriptions-item>
        <el-descriptions-item label="允许经济分类">{{ detailData.yesEconomyClass || '--' }} </el-descriptions-item>
        <el-descriptions-item label="禁用经济分类">{{ detailData.noEconomyClass || '--' }} </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(detailData.createTime) || '--' }}</el-descriptions-item>
      </el-descriptions>
    </template>
  </div>
</template>
  
<script>
import * as api from "@/api/mall/project/project.js"
export default {
  name: "MallProjectDetail",
  data() {
    return {
      loading: false,
      id: null,
      detailData: null,
      balance: 0
    }
  },
  methods: {
    init(row) {
      this.id = row.id
      this.balance = row.balance
      this.loadDetailData()
    },
    loadDetailData() {
      this.loading = true
      api.getZcProject(this.id).then(res => {
        this.detailData = res.data
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>
<style lang="scss">
.project-rule {
  .el-descriptions-item__label {
    word-break: keep-all;
  }
}
.el-cascader-menu__wrap {
  height: auto;
}
</style>
  
