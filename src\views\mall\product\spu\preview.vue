<template>
  <div class="app-container" v-show="myOpen">
    <el-dialog title="商品SKU预览" :visible.sync="myOpen" @close="close" width="1200px">
      <el-radio-group v-model="curSkuId" size="medium" @input="updateIframe">
        <el-radio-button v-for="(item,index) in skus" :key="index" :label="item.id" ></el-radio-button>
      </el-radio-group>
      <div style="margin-top:10px;">
        <el-link :href="skuPreviewUrl" target="_blank">{{ skuPreviewUrl }}</el-link>
      </div>
      <div class="preview-iframe">
        <iframe :src="iframeUrl" v-if="iframeUrl"></iframe> 
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as configApi from "@/api/mall/config/basisConfig.js"
import * as skuApi from "@/api/mall/product/sku.js"
export default {
  name: 'ProductSkuPreview',
  props: {
    spuId: {
      type: [Number, String],
      default: () => null
    },
    skuId: {
      type: [Number, String],
      default: () => null
    },
    open: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      loading: false,
      myOpen: false,
      basisConfig: {},
      skus: [],
      curSkuId: null,
      iframeUrl: null
    }
  },
  computed: {
    curSku() {
      if(this.curSkuId) {
        return this.skus.find(item => item.id === this.curSkuId) || {}
      }
      return {}
    },
    skuPreviewUrl() {
      if(this.basisConfig.domain && this.curSku.id) {
        let domain = this.basisConfig.domain
        if(domain.indexOf(",")) {
          domain = domain.split(",")[0]
        }
        let rn = Math.random()
        let skuId = this.curSkuId
        return `https://${domain}/#/detail/${this.curSku.supplierId}/${skuId}?rn=${rn}`
      }
      return null
    }
  },
  watch: {
    open(newVal, oldVal) {
      this.myOpen = newVal
      if(newVal) {
        this.loadSkuList()
      }
    }
  },
  created() {
    this.loadConfig()
  },
  methods: {
    close() {
      this.$emit('update:open', false)
    },
    async loadSkuList() {
      if(!this.spuId) return
      this.loading = true
      let res = await skuApi.getSkuListBySpu({spuId: this.spuId})
      if(res.code === 0) {
        if(this.skuId) {
          res.data = (res.data || []).filter(item => item.id === this.skuId)
        }
        this.skus = res.data || []
        if(this.skus.length) {
          this.curSkuId = this.skuId || this.skus[0].id
          this.updateIframe()
        }
      }
      this.loading = false
    },
    async loadConfig() {
      let res = await configApi.getBasisConfig()
      if(res.code === 0) {
        this.basisConfig = res.data || {}
      }
    },
    updateIframe() {
      this.iframeUrl = null
      setTimeout(() => {
        this.iframeUrl = this.skuPreviewUrl
      }, 500)
      
    },
    cancel() {
      this.close()
    }
  }
}
</script>

<style lang="scss" scoped>
.preview-iframe {
  width: 100%;
  height: 600px;
  padding: 10px 0;
  iframe {
    width: 100%;
    height: 100%;
    overflow: scroll;
  }
}

</style>