<template>
  <el-row :gutter="40" class="panel-group">
    <el-col :xs="24" :sm="24" :lg="24" class="select-supplier-col">
      <el-select v-model="selectedSupplier" placeholder="选择供应商" @change="handleSupplierChange" class="select-supplier">
        <el-option v-for="supplier in suppliers" :key="supplier.supplierName" :label="supplier.supplierName"
          :value="supplier.supplierName" />
      </el-select>
    </el-col>
    <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
      <div class="supplier-card-panel">
        <div class="card-panel-icon-wrapper icon-sku-count">
          <svg-icon icon-class="chart" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            sku总数
          </div>
          <count-to :start-val="0" :end-val="skuTtotal" :duration="2600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
      <div class="supplier-card-panel">
        <div class="card-panel-icon-wrapper icon-list-count">
          <svg-icon icon-class="color" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            上架总数
          </div>
          <count-to :start-val="0" :end-val="listingTotal" :duration="3000" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
      <div class="supplier-card-panel">
        <div class="card-panel-icon-wrapper icon-delist-count">
          <svg-icon icon-class="date" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            下架总数
          </div>
          <count-to :start-val="0" :end-val="delistingTotal" :duration="3200" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
      <div class="supplier-card-panel">
        <div class="card-panel-icon-wrapper icon-amount">
          <svg-icon icon-class="icon" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            总销量
          </div>
          <count-to :start-val="0" :end-val="sellCount" :duration="3200" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
      <div class="supplier-card-panel">
        <div class="card-panel-icon-wrapper icon-count">
          <svg-icon icon-class="money" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            销售额
          </div>
          <count-to :start-val="0" :end-val="sellAmount" :duration="3200" :decimals="decimals" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
      <div class="supplier-card-panel">
        <div class="card-panel-icon-wrapper icon-average-price">
          <svg-icon icon-class="order" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            平均单价
          </div>
          <count-to :start-val="0" :end-val="averagePrice" :duration="3200" :decimals="decimals"
            class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
      <div class="supplier-card-panel">
        <div class="card-panel-icon-wrapper icon-after-sale-amount">
          <svg-icon icon-class="map" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            售后金额
          </div>
          <count-to :start-val="0" :end-val="afterSaleAmount" :duration="3200" :decimals="decimals"
            class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
      <div class="supplier-card-panel">
        <div class="card-panel-icon-wrapper icon-after-sale-count">
          <svg-icon icon-class="merchant" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            售后单数
          </div>
          <count-to :start-val="0" :end-val="afterSaleCount" :duration="3200" class="card-panel-num" />
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import * as api from "@/api/mall/statistics/supplier";
import CountTo from 'vue-count-to'

export default {
  components: {
    CountTo
  },
  data() {
    return {
      decimals: 2,
      skuTtotal: 0,
      delistingTotal: 0,
      listingTotal: 0,
      sellCount: 0,
      sellAmount: 0,
      afterSaleAmount: 0,
      afterSaleCount: 0,
      averagePrice: 0,
      suppliers: [],
      selectedSupplier: ''
    };
  },
  created() {
    this.getSupplierList();
  },
  methods: {
    handleSupplierChange() {
      this.skuTtotal = 0;
      this.listingTotal = 0;
      this.delistingTotal = 0;
      this.sellCount = 0;
      this.sellAmount = 0;
      this.afterSaleAmount = 0;
      this.afterSaleCount = 0;
      this.averagePrice = 0;
      this.getSupplierSkuTotal();
      this.getOrderSummaryBySupplier();
      this.getAfterSaleOrderSummary();
    },
    getSupplierList() {
      api.getSupplierSkuTotal({}).then(response => {
        if (response.code === 0) {
          this.suppliers = response.data;
          if (this.suppliers.length > 0) {
            this.selectedSupplier = this.suppliers[0].supplierName;
            this.handleSupplierChange();
          }
        }
      });
    },
    getSupplierSkuTotal() {
      api.getSupplierSkuTotal({}).then(response => {
        if (response.code === 0) {
          for (var i = 0; i < response.data.length; i++) {
            var item = response.data[i]
            if (item.supplierName == this.selectedSupplier) {
              this.skuTtotal = item.skuSummaryRespVO.total
              this.listingTotal = item.skuSummaryRespVO.listingTotal
              this.delistingTotal = item.skuSummaryRespVO.delistingTotal
              break
            }
          }
        }
      });
    },
    getOrderSummaryBySupplier() {
      api.getOrderSummaryBySupplier({}).then(response => {
        if (response.code === 0) {
          for (var i = 0; i < response.data.length; i++) {
            var item = response.data[i]
            if (item.supplierName == this.selectedSupplier) {
              this.sellAmount = item.orderAmount
              this.sellCount = item.orderCount
              this.averagePrice = item.orderAmount / item.orderCount
              break
            }
          }
        }
      });
    },
    getAfterSaleOrderSummary() {
      api.getAfterSaleOrderSummary({}).then(response => {
        if (response.code === 0) {
          for (var i = 0; i < response.data.length; i++) {
            var item = response.data[i]
            if (item.supplierName == this.selectedSupplier) {
              this.afterSaleAmount = item.orderAmount
              this.afterSaleCount = item.orderCount
              break
            }
          }
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.panel-group {
  margin-top: 18px;

  .select-supplier-col {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 20px;
    width: 100%;
  }

  .select-supplier {
    width: 200px;
  }

  .card-panel-col {
    margin-bottom: 32px;
  }

  .supplier-card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #ffffff;
      }

      .icon-sku-count {
        background: #5def62;
      }

      .icon-list-count {
        background: #e3ba68;
      }

      .icon-delist-count {
        background: #6f59e8;
      }

      .icon-amount {
        background: #1699ea;
      }

      .icon-count {
        background: #da80ce;
      }

      .icon-after-sale-amount {
        background: #3fb1c5;
      }

      .icon-after-sale-count {
        background: #711665;
      }

      .icon-average-price {
        background: #e26c5d;
      }
    }

    .card-panel-icon-wrapper {
      color: #ffffff;
    }

    .icon-sku-count {
      color: #5def62;
    }

    .icon-list-count {
      color: #e3ba68;
    }

    .icon-delist-count {
      color: #6f59e8;
    }

    .icon-amount {
      color: #1699ea;
    }

    .icon-count {
      color: #da80ce;
    }

    .icon-after-sale-amount {
      color: #3fb1c5;
    }

    .icon-after-sale-count {
      color: #711665;
    }

    .icon-average-price {
      color: #e26c5d;
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px 26px 26px 0;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}
</style>