import request from "@/utils/request";

// 创建用户反馈
export function createUserFeedback(data) {
  return request({
    url: "/mall/user-feedback/create",
    method: "post",
    data: data,
  });
}

// 更新用户反馈
export function updateUserFeedback(data) {
  return request({
    url: "/mall/user-feedback/update",
    method: "put",
    data: data,
  });
}

// 删除用户反馈
export function deleteUserFeedback(id) {
  return request({
    url: "/mall/user-feedback/delete?id=" + id,
    method: "delete",
  });
}

// 获得用户反馈
export function getUserFeedback(id) {
  return request({
    url: "/mall/user-feedback/get?id=" + id,
    method: "get",
  });
}

// 获得用户反馈分页
export function getUserFeedbackPage(query) {
  return request({
    url: "/mall/user-feedback/page",
    method: "get",
    params: query,
  });
}

// 导出用户反馈 Excel
export function exportUserFeedbackExcel(query) {
  return request({
    url: "/mall/user-feedback/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}
