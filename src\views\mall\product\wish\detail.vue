<template>
  <div class="app-container" v-loading="loading">
    <template v-if="detailData">
      <!-- 心愿单信息 -->
      <el-descriptions title="" :column="2" border>
        <el-descriptions-item label="商品分类">{{ detailData.categoryNames }}</el-descriptions-item>
        <el-descriptions-item label="品牌">{{ detailData.brand || '--' }}</el-descriptions-item>
        <el-descriptions-item label="型号">{{ detailData.model || '--' }}</el-descriptions-item>
        <el-descriptions-item label="数量">{{ detailData.quantity || '--' }}</el-descriptions-item>
        <el-descriptions-item label="预计下单时间">{{ detailData.orderTime ? parseTime(detailData.orderTime, '{y}-{m}-{d}') : '--' }}</el-descriptions-item>
        <el-descriptions-item label="产品链接">
          <el-link :href="detailData.productLink" target="_blank">{{detailData.productLink}}</el-link>
        </el-descriptions-item>
        <el-descriptions-item label="参数描述">{{ detailData.productMemo || '--' }}</el-descriptions-item>
        <el-descriptions-item label="联系人">{{ detailData.contact }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ detailData.phone }}</el-descriptions-item>
        <el-descriptions-item label="用户名">{{ detailData.userName }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="detailData.status === 0 ? 'info' : 'primary'">{{ detailData.status === 0 ? '待回复' : '已回复'}}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="回复内容">{{ detailData.replyContent || '--' }}</el-descriptions-item>
        <el-descriptions-item label="扩展参数">{{ detailData.extParams || '--' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(detailData.createTime) || '--' }}</el-descriptions-item>
      </el-descriptions>
    </template>
  </div>
</template>
  
<script>
import * as api from "@/api/mall/product/wish";
export default {
  name: "ProductWishDetail",
  data() {
    return {
      loading: false,
      detailData: null
    }
  },
  methods: {
    init(id) {
      this.loading = true
      api.getWish(id).then(res => {
        this.detailData = res.data
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>
  
