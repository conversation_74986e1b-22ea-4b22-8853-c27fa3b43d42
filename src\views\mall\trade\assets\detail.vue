<template>
  <div class="app-container" v-loading="loading">
    <template v-if="detailData">
      <!-- 固资建档信息 -->
      <el-descriptions title="" :column="2" border>
        <el-descriptions-item label="订单号">
          <el-link type="primary" @click="jumpToOrderDetail">{{ detailData.orderNo }}</el-link>
          <el-tag :type="detailData.assetStatus | orderAssetStatusStyle">{{ detailData.assetStatus | orderAssetStatusInfo }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="订单明细ID">{{ detailData.orderItemId }}</el-descriptions-item>
        <el-descriptions-item label="资产系统" v-if="detailData.sysCode"><dict-tag :type="DICT_TYPE.MALL_INTEGRATION_ASSETS_TYPE" :value="detailData.sysCode"/></el-descriptions-item>
        <el-descriptions-item label="商品名称" :labelStyle="{ width: '110px' }"  :contentStyle="{ width: '40%' }">{{ detailData.skuName
          }}</el-descriptions-item>
        <el-descriptions-item label="商品价格" :labelStyle="{ width: '110px' }">{{ formatMoney(detailData.price) }}</el-descriptions-item>
        <el-descriptions-item label="数量">{{ detailData.quantity }}</el-descriptions-item>
        <el-descriptions-item label="规格">{{ detailData.spec || '--' }}</el-descriptions-item>
        <el-descriptions-item label="型号">{{ detailData.model || '--' }}</el-descriptions-item>
        <el-descriptions-item label="用户">{{ detailData.userName || '--'}} / {{ detailData.userNo || '--' }}</el-descriptions-item>
        <el-descriptions-item label="部门">{{ detailData.deptName || '--'}} / {{ detailData.deptNo || '--' }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ detailData.phone || '--' }}</el-descriptions-item>
        <el-descriptions-item label="发票号码">{{ detailData.invoiceNo || '--' }}</el-descriptions-item>
        <el-descriptions-item label="购买时间">{{ parseTime(detailData.buyDate) || '--' }}</el-descriptions-item>
        <el-descriptions-item label="建档开始时间">{{ parseTime(detailData.createTime) || '--' }}</el-descriptions-item>
        <el-descriptions-item label="建档完成时间">{{ parseTime(detailData.assetFinishTime) || '--' }}</el-descriptions-item>
        
        <el-descriptions-item label="采购原因" v-if="detailData.purchaseReason">{{ detailData.purchaseReason }}</el-descriptions-item>
        <template v-if="detailData.projectNo">
          <el-descriptions-item label="项目编号">{{ detailData.projectNo }}</el-descriptions-item>
          <el-descriptions-item label="项目名称">{{ detailData.projectName }}</el-descriptions-item>
          <el-descriptions-item label="项目所属部门">{{ detailData.projectDepartmentName }} / {{ detailData.projectDepartmentNo }}</el-descriptions-item>
        </template>
      </el-descriptions>

      <el-descriptions title="" :column="1" direction="vertical" border style="margin-top:20px;">
        <el-descriptions-item label="相关附件">
          <div class="flex-start"> 
            <div v-for="item in assetFiles" :key="item" class="flex-start" style="margin: 10px 0">
              <div class="flex-start">
                <el-image 
                  v-if="isImage(item)"
                  style="width: 100px; height: 100px"
                  :src="item" 
                  :preview-src-list="assetFileImages">
                </el-image>
                <el-tag type="primary" v-else>{{ item }}</el-tag>
                <el-link type="primary" :href="item" target="_blank">查看</el-link>
              </div>
            </div>
          </div>
          <AcceptDetail :orderItemId="detailData.orderItemId"></AcceptDetail>
        </el-descriptions-item>
      </el-descriptions>

      <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 30px;">
        <div style="font-size: 16px; font-weight: bold;">固资建档明细</div>
        <div style="float: right; margin: 10px;">
          <el-button v-hasPermi="['trade:order-item-assets:push-sys']" v-if="detailData.assetStatus === 0" type="primary" :loading="submitLoading3" @click="pushAsset2Sys">推送到资产</el-button>
          <el-button v-hasPermi="['trade:order-item-assets:push-ycrh']" v-if="detailData.assetStatus === 4" type="primary" :loading="submitLoading1" @click="pushAsset2Ycrh">推送到财务</el-button>
          <el-button v-hasPermi="['trade:order-item-assets:update']" v-if="![2,4].includes(detailData.assetStatus)" type="primary" :loading="submitLoading2" @click="updateNotAsset">无须建档</el-button>
          <el-button v-hasPermi="['trade:order-item-assets:update']" v-if="![2,4].includes(detailData.assetStatus)" type="primary" @click="updateComplete">建档完成</el-button>
          <el-button v-hasPermi="['trade:order-item-assets:update']" v-if="detailList.length > 0 && !editing" type="primary" @click="editCategories">修改类别</el-button>
        </div>
        
        <span v-hasPermi="['trade:order-item-assets:update']" v-if="editing">
          <el-button type="success" style="float: right; margin: 10px;" @click="saveAndPush">保存并推送</el-button>
          <el-button type="warning" style="float: right; margin: 10px;" @click="cancelEdit">取消修改</el-button>
        </span>
      </div>
      <el-descriptions :colon="false" direction="vertical">
        <el-descriptions-item label="">
          <el-table :data="detailList" border max-height="500px">
            <el-table-column prop="assetAcceptNo" label="验收单号" width="180"></el-table-column>
            <el-table-column prop="assetNo" label="资产编号" width="180"></el-table-column>
            <el-table-column prop="quantity" label="数量" width="80"></el-table-column>
            <el-table-column prop="totalPrice" label="金额" width="100">
              <template v-slot="scope">
                <span>{{ formatMoney(scope.row.totalPrice) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="approveStatus" label="审批状态" width="100">
              <template v-slot="scope">
                <el-tag :type="scope.row.approveStatus | orderAssetStatusStyle">{{ scope.row.approveStatus |
                  orderAssetStatusInfo }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="approveMemo" label="审批备注" show-overflow-tooltip></el-table-column>
            <el-table-column prop="approveTime" label="审批时间" width="170">
              <template v-slot="scope">
                <span>{{ parseTime(scope.row.approveTime || scope.row.createTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="6大类别">
              <template v-slot="scope">
                <el-input v-if="editing" v-model="scope.row.category6" placeholder="请输入6大类别" />
                <span v-else>{{ scope.row.category6 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="16大类别">
              <template v-slot="scope">
                <el-input v-if="editing" v-model="scope.row.category16" placeholder="请输入16大类别" />
                <span v-else>{{ scope.row.category16 }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-descriptions-item>
      </el-descriptions>
    </template>
    <AssetsCompleteForm ref="assetsCompleteForm" @on-update="reloadAndEmits"></AssetsCompleteForm>
  </div>

</template>

<script>
import * as api from "@/api/mall/trade/assets.js"
import { configMixins } from '@/views/mall/config/components/configMixin.js'
import AssetsCompleteForm from '@/views/mall/trade/assets/components/completeForm'
import AcceptDetail from '@/views/mall/trade/order/components/acceptDetail'
import { isImage } from '@/utils/ruoyi.js'
export default {
  name: "TradeOrderAssetsDetail",
  mixins: [ configMixins ],
  components: { AssetsCompleteForm, AcceptDetail },
  data() {
    return {
      id: null,
      loading: false,
      detailData: null,
      detailList: [],
      originDetailList: null,
      editing: false,
      submitLoading1: false,
      submitLoading2: false,
      submitLoading3: false,
    }
  },
  computed: {
    assetFiles() {
      let files = []
      if(this.detailData.assetProofFile) {
        files = files.concat(this.detailData.assetProofFile.split(','))
      }

      return files
    },
    assetFileImages() {
      let files = this.assetFiles
      if(files && files.length) {
        return files.filter(url => this.isImage(url))
      }
      return []
    }
  },
  watch: {
    detailData(newVal) {
      if (newVal && newVal.detailList) {
        this.detailList = newVal.detailList
      }
    }
  },
  methods: {
    isImage(filename) {
      return isImage(filename)
    },
    init(id) {
      this.id = id
      this.editing = false
      this.loadAssetDetail()
    },
    loadAssetDetail() {
      this.loading = true
      api.getOrderAssetsDetail({
        id: this.id
      }).then(res => {
        this.detailData = res.data
      }).finally(() => {
        this.loading = false
      })
    },
    editCategories() {
      this.originDetailList = JSON.parse(JSON.stringify(this.detailList))
      this.editing = true
    },
    saveAndPush() {
      if (this.isDetailListModified()) {
        const dataToPush = this.detailList.map(item => ({
          id: item.id,
          category6: item.category6,
          category16: item.category16
        }))
        api.updateOrderAssetsCategory(dataToPush).then(() => {
          this.editing = false
          this.$message.success('类别修改成功并已推送')
        })
      } else {
        this.$message.info('未修改类别信息')
        this.cancelEdit()
      }
    },
    cancelEdit() {
      this.detailList = JSON.parse(JSON.stringify(this.originDetailList))
      this.originDetailList = null
      this.editing = false
    },
    isDetailListModified() {
      for (let i = 0; i < this.detailList.length; i++) {
        if (this.detailList[i].category6 !== this.originDetailList[i].category6 ||
          this.detailList[i].category16 !== this.originDetailList[i].category16) {
          return true
        }
      }
      return false
    },
    jumpToOrderDetail() {
      if(!this.detailData.orderId) {
        return
      }
      this.$emit('on-hide')
      this.$router.push({ name: 'TradeOrderDetail', query: { id: this.detailData.orderId } });
    },
    pushAsset2Ycrh() {
      let self = this
      let param = {
        orderId: this.detailData.orderId
      }
      this.$modal.confirm('确认执行此操作吗?').then(function () {
        self.submitLoading1 = true
        return api.pushAsset2Ycrh(param);
      }).then((res) => {
        if(res.data) {
          this.loadAssetDetail()
          this.$message.success('操作成功')
        } else {
          this.$message.warning('处理失败，请稍后重试。')
        }
      }).finally(() => { 
        self.submitLoading1 = false
      });
    },
    pushAsset2Sys() {
      let self = this
      let param = {
        orderItemId: this.detailData.orderItemId
      }
      this.$modal.confirm('确认执行此操作吗?').then(function () {
        self.submitLoading3 = true
        return api.pushAsset2Sys(param);
      }).then((res) => {
        if(res.data) {
          this.loadAssetDetail()
          this.$message.success('操作成功')
        } else {
          this.$message.warning('处理失败，请稍后重试。')
        }
      }).finally(() => { 
        self.submitLoading3 = false
      });
    },
    reloadAndEmits() {
      this.loadAssetDetail()
      this.$emit('on-update')
    },
    updateNotAsset() {
      let self = this
      let param = {
        orderItemId: this.detailData.orderItemId
      }
      this.$modal.confirm('确认执行此操作吗?').then(function () {
        self.submitLoading2 = true
        return api.updateNotAsset(param);
      }).then((res) => {
        this.reloadAndEmits()
        this.$message.success('操作成功')
      }).finally(() => {
        self.submitLoading2 = false
       });
    },
    updateComplete() {
      this.$refs.assetsCompleteForm.show(this.detailData)
    }
  }
}
</script>
<style lang="scss" scoped>
.el-descriptions-item__label {
  word-break: keep-all;
}
</style>