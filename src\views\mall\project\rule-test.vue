<template>
  <div class="flex-vertical-start" style="padding: 20px;">
    <div>
      <el-form :model="form" ref="form" :rules="rules" size="small" :inline="true" label-width="100px">
        <el-form-item label="用户工号" prop="userNo">
          <el-input v-model="form.userNo" placeholder="请输入用户工号" clearable />
        </el-form-item>
        <el-form-item label="项目编号" prop="projectNo">
          <el-input v-model="form.projectNo" placeholder="请输入项目编号" clearable />
        </el-form-item>
        <el-form-item label="项目类型" prop="projectType">
          <el-input v-model="form.projectType" placeholder="请输入项目类型" clearable />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" clearable />
        </el-form-item>
        <el-form-item label="项目部门编号" prop="projectDeptNo">
          <el-input v-model="form.projectDeptNo" placeholder="请输入项目所属部门编号" clearable />
        </el-form-item>
        <el-form-item label="商品分类" prop="categoryIds">
          <el-input v-model="form.categoryIds" placeholder="请输入商品分类ID，多个以逗号分隔" clearable />
        </el-form-item>
        <el-form-item label="商品SKU" prop="skuIds">
          <el-input v-model="form.skuIds" placeholder="请输入SKU，多个以逗号分隔" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submit" :loading="submitLoading">提交</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div style="margin-top:20px;width:100%"> 
      <h3>检测结果：</h3>
      <el-table :data="testData" style="width: 100%">
        <el-table-column prop="name" label="规则名称"></el-table-column>
        <el-table-column prop="ruleType" label="规则类型">
          <template v-slot="scope">
            <dict-tag :type="DICT_TYPE.MALL_PROJECT_RULE_TYPE" :value="scope.row.ruleType"/>
          </template>
        </el-table-column>
        <el-table-column prop="inProjectScope" label="规则匹配状态">
          <template v-slot="scope">
            <el-tag :type="scope.row.inProjectScope ? 'success' : 'info'">{{ scope.row.inProjectScope ? '匹配' : '不匹配' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="项目可用状态">
          <template v-slot="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">{{ scope.row.status === 1 ? '可用' : '不可用' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="memo" label="备注说明"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import * as api from "@/api/mall/project/projectRule";
export default {
  name: 'ProjectRuleTest',
  data() {
    return {
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userNo: [{ required: true, message: "用户工号不能为空", trigger: "blur" }],
        projectNo: [{ required: true, message: "项目编号不能为空", trigger: "blur" }],
        categoryIds: [{ required: true, message: "商品分类不能为空", trigger: "blur" }],
      },
      testData: [],
      submitLoading: false,
    }
  },
  created() {
    this.resetForm()
  },
  methods: {
    init() {
      
    },
    resetForm() {
      this.form = {
        userNo: '',
        projectNo: '',
        projectType: '',
        projectName: '',
        projectDeptNo: '',
        projectDeptName: '',
        categoryIds: '',
        skuIds: ''
      }
    },
    submit() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        let data = Object.assign({}, this.form)
        data.categoryIds = []
        if(this.form.categoryIds) {
          data.categoryIds = this.form.categoryIds.split(',')
        }
        data.skuIds = []
        if(this.form.skuIds) {
          data.skuIds = this.form.skuIds.split(',')
        }

        this.submitLoading = true
        api.testProjectRule(data).then(res => {
          this.$modal.msgSuccess("检测完成");
          this.testData = res.data;
        }).finally(() => {
          this.submitLoading = false
        });
      });
    }
  }
}
</script>

<style>

</style>