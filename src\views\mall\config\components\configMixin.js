
import { mapState } from 'vuex'

export const configMixins = {
  data() {
    return {

    }
  },
  computed: {
     ...mapState({
      basisConfig: state => state.mall.basisConfig
    }),
    isPurchaseOn() {
      return this.isBpm || this.isProject
    },
    isBpm() {
      return this.basisConfig.approveSwitch
    },
    isProject() {
      return this.basisConfig.projectSwitch
    },
    isAssetSwitch() {
      return this.basisConfig.assetsSwitch
    },
    isAcceptSwitch() {
      return this.getExtConfigItem('acceptSwitch')
    },
    payMethods() {
      let arr = []
      if(this.basisConfig.payMethod) {
        arr = this.basisConfig.payMethod.split(',')
        arr = arr.map(item => parseInt(item))
      }
      return arr
    },
    payMethodOpts() {
      if(this.payMethods.length) {
        let dicOpts = this.getDictDatas(this.DICT_TYPE.MALL_PAYMENT_METHOD)
        return dicOpts.filter(opt => this.payMethods.includes(parseInt(opt.value)))
      } else {
        return []
      }
    }
  },
  methods: {
    getExtConfig() {
      if(this.basisConfig.extConfig) {
        return JSON.parse(this.basisConfig.extConfig)
      }
      return []
    },
    getExtConfigItem(key) {
      let config = this.getExtConfig()
      return config ? config[key] : null
    }
  }
}