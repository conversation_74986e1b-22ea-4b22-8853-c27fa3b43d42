import request from '@/utils/request'


// 查询配置
export function getSsoConfig() {
  return request({
    url: '/mall/sso-config/get',
    method: 'get'
  })
}

// 查询配置
export function getCasAuthUrl() {
  return request({
    url: '/mall/sso-config/cas/get-auth-url',
    method: 'get'
  })
}

// 查询配置
export function getSsoAuthCallbackUrl() {
  return request({
    url: '/mall/sso-config/ycrh/get-callback-url',
    method: 'get'
  })
}


// 更新配置
export function updateSsoConfig(data) {
  return request({
    url: '/mall/sso-config/update',
    method: 'put',
    data: data
  })
}

// 更新配置
export function createSsoConfig(data) {
  return request({
    url: '/mall/sso-config/create',
    method: 'post',
    data: data
  })
}

// 清除配置
export function cleanSsoConfig() {
  return request({
    url: '/mall/sso-config/clean',
    method: 'post'
  })
}
