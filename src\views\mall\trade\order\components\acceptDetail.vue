<template>
  <div class="accept-info-con" v-loading="loading"> 
    <div class="accept-info-block"> 
      <div class="title">证明材料</div>
      <div class="flex-start">
        <div v-for="(item,index) in proofFiles" :key="index" class="flex-start" style="margin: 10px 0">
          <div class="flex-vertical-center" style="margin: 0 15px 15px 0">
            <el-image 
              v-if="isImage(item)"
              style="width: 100px; height: 100px"
              :src="item" 
              :preview-src-list="proofImages">
            </el-image>
            <el-tag type="primary" v-else>{{ item }}</el-tag>
            <div> 
              <el-link type="primary" :href="item" target="_blank">查看</el-link>
              <el-link type="warning" style="margin-left: 10px;" @click="deleteFileItem(index,'1')" v-loading="loading2"
                v-hasPermi="['trade:order-item-assets:update']" v-if="proofFiles.length > 1">删除</el-link>
            </div>
          </div>
        </div>
      </div>
      <div v-hasPermi="['trade:order-item-assets:update']" v-if="proofFiles.length < 10"> 
        <el-button size="small" @click="showFileDialog('1')">补充材料上传</el-button>
      </div>
    </div>
    <div class="accept-info-block"> 
      <div class="title">实物照片</div>
      <div class="flex-start">
        <div v-for="(item,index) in otherFiles" :key="index" class="flex-start" style="margin: 10px 0;">
          <div class="flex-vertical-center" style="margin: 0 15px 15px 0">
            <el-image 
              v-if="isImage(item)"
              style="width: 100px; height: 100px"
              :src="item" 
              :preview-src-list="otherImages">
            </el-image>
            <el-tag type="primary" v-else>{{ item }}</el-tag>
            <div> 
              <el-link type="primary" :href="item" target="_blank">查看</el-link>
              <el-link type="warning" style="margin-left: 10px;" @click="deleteFileItem(index,'2')" v-loading="loading2"
                  v-hasPermi="['trade:order-item-assets:update']" v-if="otherFiles.length > 1">删除</el-link>
            </div>
          </div>
        </div>
      </div>
      <div v-hasPermi="['trade:order-item-assets:update']" v-if="proofFiles.length < 10"> 
        <el-button size="small" @click="showFileDialog('2')">补充材料上传</el-button>
      </div>
    </div>
    <div class="accept-info-block" v-if="acceptInfo && acceptInfo.memo"> 
      <div class="title">备注说明</div>
      <div>{{ acceptInfo.memo }}</div>
    </div>

    <el-dialog :title="form.type === '1' ? '补充报销材料' : '补充现场照片'" :visible.sync="open" width="700px" append-to-body>
      <div> 
        <fileUpload v-model="form.manualFiles" :limit="5" :fileSize="10" :fileType="['pdf','png', 'jpg', 'jpeg']" />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import * as api from '@/api/mall/trade/acceptance'
import { isImage } from '@/utils/ruoyi.js'
import FileUpload from '@/components/FileUpload'
export default {
  name: 'OrderAcceptDetailCom',
  components: { FileUpload },
  props: {
    orderItemId: {
      default() {
        return null
      }
    }
  },
  data() {
    return {
      loading: false,
      loading2: false,
      submitLoading: false,
      open: false,
      acceptInfo: null,
      form: {
        type: '1',
        manualFiles: null
      }
    }
  },
  computed: {
    proofFiles() {
      let files = []
      if(this.acceptInfo && this.acceptInfo.proofFile) {
        if(this.acceptInfo.proofFile) {
          files = files.concat(this.acceptInfo.proofFile.split(','))
        }
      }

      return files
    },
    proofImages() {
      let files = this.proofFiles
      if(files && files.length) {
        return files.filter(url => isImage(url))
      }
      return []
    },
    otherFiles() {
      let files = []
      if(this.acceptInfo && this.acceptInfo.otherFile) {
        if(this.acceptInfo.otherFile) {
          files = files.concat(this.acceptInfo.otherFile.split(','))
        }
      }

      return files
    },
    otherImages() {
      let files = this.otherFiles
      if(files && files.length) {
        return files.filter(url => isImage(url))
      }
      return []
    }
  },
  watch: {
    orderItemId: {
      immediate: true,
      handler(newVal) {
        this.loadAcceptInfo()
      }
    }
  },
  methods: {
    isImage(url) {
      return isImage(url)
    },
    async loadAcceptInfo() {
      if(!this.orderItemId) {
        return
      }
      this.loading = true
      let params = {
        orderItemId: this.orderItemId
      }
      let res = await api.getOrderAcceptanceByItem(params)
      this.acceptInfo = res.data
      this.loading = false
    },
    showFileDialog(type) {
      this.form.type = type || '1'
      this.form.manualFiles = null
      this.open = true
    },
    cancel() {
      this.open = false
    },
    submitForm() {
      if(!this.form.manualFiles) {
        this.$modal.msgWarning("请上传文件")
        return
      }
      let params = {
        proofFile: this.acceptInfo ? this.acceptInfo.proofFile : null,
        otherFile: this.acceptInfo ? this.acceptInfo.otherFile : null,
        orderItemId: this.orderItemId
      }

      if(this.form.type === '1') {
        params.proofFile = this.form.manualFiles
        if(this.acceptInfo && this.acceptInfo.proofFile) {
          params.proofFile = this.acceptInfo.proofFile + ',' + params.proofFile
        }
      } else if(this.form.type === '2') {
        params.otherFile = this.form.manualFiles
        if(this.acceptInfo && this.acceptInfo.otherFile) {
          params.otherFile = this.acceptInfo.otherFile + ',' + params.otherFile
        }
      } else {
        console.error('--type-invalid-', type)
      }

      this.submitLoading = true
      api.saveAcceptanceInfoByItem(params).then(res => {
        this.open = false
        this.loadAcceptInfo()
      }).finally(() => {
        this.submitLoading = false
      })
    },
    deleteFileItem(index, type) {
      let files1 = [...this.proofFiles]
      let files2 = [...this.otherFiles]
      // 验证索引有效性
      if (index < 0 || (type === '1' && index >= files1.length) || (type === '2' && index >= files2.length)) {
        console.error(`Invalid index: ${index}`)
        return
      }
      // 删除指定索引的文件
      if(type === '1') files1.splice(index, 1)
      if(type === '2') files2.splice(index, 1)
      
      let params = {
        proofFile: files1.join(','),
        otherFile: files2.join(','),
        orderItemId: this.orderItemId
      }
      this.loading2 = true
      this.$modal.confirm('您确认执行此操作吗?').then(function () {
        return api.saveAcceptanceInfoByItem(params);
      }).then((res) => {
         this.loadAcceptInfo()
         this.loading2 = false
      }).catch(() => {
         this.loading2 = false
      })
    }
  }

}
</script>

<style lang="scss" scoped>
.accept-info-con {
  .accept-info-block {
    padding: 10px;
    margin: 10px 0;
    .title {
      font-weight: 500;
    }
  }
}
</style>