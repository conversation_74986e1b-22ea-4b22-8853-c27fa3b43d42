import request from '@/utils/request'

// 获得交易订单分页
export function getOrderList(params) {
  return request({
    url: '/trade/order/page',
    method: 'get',
    params
  })
}

// 获得交易订单详情
export function getOrderDetail(params) {
  return request({
    url: '/trade/order/get-detail',
    method: 'get',
    params
  })
}

// 获得交易订单父订单详情
export function getOrderParentDetail(params) {
  return request({
    url: '/trade/order/get-parent-detail',
    method: 'get',
    params
  })
}

// 导出交易订单
export function exportOrderList(params) {
  return request({
    url: '/trade/order/export',
    // responseType: 'blob',
    method: 'get',
    params
  })
}

// 获取订单审批信息
export function getAuditResult(params) {
  return request({
    url: '/trade/order/get-audit-result',
    method: 'get',
    params
  })
}

// 确认订单
export function confirmOrder(data) {
  return request({
    url: '/trade/order/confirmOrder',
    method: 'post',
    data
  })
}

// 订单签收
export function receiveOrder(data) {
  return request({
    url: '/trade/order/receiveOrder',
    method: 'post',
    data
  })
}

// 完成订单
export function completeOrder(data) {
  return request({
    url: '/trade/order/completeOrder',
    method: 'post',
    data
  })
}

// 取消订单
export function cancelOrder(data) {
  return request({
    url: '/trade/order/cancelOrder',
    method: 'post',
    data
  })
}

// 获得订单状态总数
export function getOrderStatus(params) {
  return request({
    url: '/trade/order/get-order-status',
    method: 'get',
    params
  })
}


// 查询结算列表
export function queryOrderSettlePage(params) {
  return request({
    url: '/trade/orderSettle/queryOrderSettlePage',
    method: 'get',
    params
  })
}

// 结算
export function settleByOrderIds(data) {
  return request({
    url: '/trade/orderSettle/settleByOrderIds',
    method: 'post',
    data
  })
}

// 导出结算
export function exportSettleOrder(params) {
  return request({
    url: '/trade/orderSettle/exportSettleOrder',
    method: 'get',
    params
  })
}

// 开票申请
export function invoiceApply(data) {
  return request({
    url: '/trade/orderSettle/invoiceApply',
    method: 'post',
    data
  })
}

// 发票更新
export function invoiceUpdate(data) {
  return request({
    url: '/trade/orderSettle/invoiceUpdate',
    method: 'post',
    data
  })
}

// 发票上传
export function uploadVoucher(data) {
  return request({
    url: '/trade/orderSettle/uploadVoucher',
    method: 'post',
    data
  })
}

// 发票验真
export function checkInvoice(data) {
  return request({
    url: '/trade/orderSettle/checkInvoice',
    method: 'post',
    data
  })
}

// 发票导出
export function downloadInvoice(data) {
  return request({
    url: '/trade/orderSettle/downloadInvoice',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 获得订单操作日志记录分页
export function getOrderOperateLogPage(query) {
  return request({
    url: '/trade/order-operate-log/page',
    method: 'get',
    params: query
  })
}

// 创建订单固资建档信息
export function createOrderAssets(data) {
  return request({
    url: '/trade/order/createOrderAssets',
    method: 'post',
    data
  })
}

// 回滚订单固资建档信息
export function resetOrderAssets(data) {
  return request({
    url: '/trade/order/resetOrderAssets',
    method: 'post',
    data
  })
}

// 订单详情添加备注
export function setRemark(data) {
  return request({
    url: '/trade/order/setRemark',
    method: 'post',
    data
  })
}

// 订单项放开售后
export function openAfterSale(data) {
  return request({
    url: '/trade/order/openAfterSale',
    method: 'post',
    data
  })
}

// 业财订单状态同步，会做补偿
export function syncOrderYcrh(data) {
  return request({
    url: '/trade/order/syncYcrhStatus',
    method: 'post',
    data
  })
}

// 业财订单状态刷新
export function refreshYcrhStatus(data) {
  return request({
    url: '/trade/order/refreshYcrhStatus',
    method: 'post',
    data
  })
}

// 业财订单取消
export function cancelOrderYcrh(data) {
  return request({
    url: '/trade/order/cancel4Ycrh',
    method: 'post',
    data
  })
}

// 设置线下结算
export function setOfflineSettlement(data) {
  return request({
    url: '/trade/order/setOfflineSettlement',
    method: 'post',
    data
  })
}