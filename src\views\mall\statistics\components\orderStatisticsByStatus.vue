<template>
    <div class="tradeStatisticsByStatus" :style="{ height: height, width: width }" />
</template>

<script>
import * as api from "@/api/mall/statistics/trade";
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '300px'
        }
    },
    data() {
        return {
            chart: null
        }
    },
    created() {
        this.getOrderSummary();
    },
    methods: {
        getOrderSummary() {
            api.getOrderSummary({}).then(response => {
                if (response.code === 0) {
                    this.initChart(response.data)
                }
            });
        },
        initChart(data) {
            const names = ['待审批', '已审批', '待结算', '已结算', '已完成', '已取消', '已售后'];
            const values = [data.auditingCount, data.auditedCount,
            data.notSettlementCount, data.hasSettlementCount,
            data.completeCount, data.cancelCount, data.afterSellCount];

            // 定义一个颜色数组，与names和values数组的顺序对应
            const colors = ['#5793f3', '#d14a61', '#675bba', '#50a3ba', '#e53935', '#388e3c', '#fdd835'];

            this.chart = echarts.init(this.$el, 'macarons');
            this.chart.setOption({
                title: {
                    text: '订单状态统计',
                    top: 'top',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '10%',
                    right: '10%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: [
                    {
                        type: 'category',
                        name: '订单状态',
                        data: names.reverse()
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        name: '销售额'
                    }
                ],
                series: [
                    {
                        name: 'Direct',
                        type: 'bar',
                        barWidth: '60%',
                        itemStyle: {
                            // 使用颜色数组，颜色根据数据值变化
                            color: function (params) {
                                return colors[params.dataIndex];
                            }
                        },
                        data: values.reverse(),
                    }
                ]
            });
        }
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    }
}
</script>