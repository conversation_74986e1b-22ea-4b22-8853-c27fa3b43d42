<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户id" prop="userId">
        <el-input v-model="queryParams.userId" placeholder="请输入用户id" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="用户类型" prop="userType">
        <el-select v-model="queryParams.userType" placeholder="请选择用户类型" clearable size="small">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="模板编码" prop="templateCode">
        <el-input v-model="queryParams.templateCode" placeholder="请输入模板编码" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="发送人" prop="templateNickname">
        <el-input v-model="queryParams.templateNickname" placeholder="请输入发送人" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="模版类型" prop="templateType">
        <el-select v-model="queryParams.templateType" placeholder="请选择模版类型" clearable size="small">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="发送状态" prop="sendStatus">
        <el-select v-model="queryParams.sendStatus" placeholder="请选择发送状态" clearable>
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_SOCIAL_MESSAGE_SEND_STATUS)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="发送时间" prop="sendTime">
        <el-date-picker v-model="queryParams.sendTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <!-- <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['system:social-message:create']">新增</el-button> -->
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          :loading="exportLoading" v-hasPermi="['system:social-message:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="编号" align="center" prop="id"/>
      <el-table-column label="用户id" align="center" prop="userId"/>
      <el-table-column label="用户类型" align="center" prop="userType">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.USER_TYPE" :value="scope.row.userType" />
        </template>
      </el-table-column>
      <el-table-column label="模板编码" align="center" prop="templateCode" />
      <el-table-column label="发送人" align="center" prop="templateNickname" />
      <el-table-column label="模版内容" align="center" prop="templateContent" />
      <el-table-column label="模版类型" align="center" prop="templateType">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_SOCIAL_MESSAGE_TEMPLATE_TYPE" :value="scope.row.templateType" />
        </template>
      </el-table-column>
      <el-table-column label="发送状态" align="center" width="180">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_SOCIAL_MESSAGE_SEND_STATUS" :value="scope.row.sendStatus"/>
          <div>{{ parseTime(scope.row.sendTime) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="发送时间" align="center" prop="sendTime">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.sendTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)"
                     v-hasPermi="['system:social-message:query']">详细</el-button>
          <el-button size="mini" type="text" icon="el-icon-s-promotion" @click="resend(scope.row)"
          v-hasPermi="['system:social-message-send:resend']">重发</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 三方消息详细-->
    <el-dialog :title="title" :visible.sync="open" width="700px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" label-width="160px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="日志主键：">{{ form.id }}</el-form-item>
            <el-form-item label="发送时间：">{{ parseTime(form.createTime) }}</el-form-item>
            <el-form-item label="用户编号：">{{ form.userId }}</el-form-item>
            <el-form-item label="用户类型：">
              <dict-tag :type="DICT_TYPE.USER_TYPE" :value="form.userType"/>
            </el-form-item>
            <el-form-item label="模板编码：">{{ form.templateCode }}</el-form-item>
            <el-form-item label="模板类型：">
              <dict-tag :type="DICT_TYPE.SYSTEM_SOCIAL_MESSAGE_TEMPLATE_TYPE" :value="form.templateType" />
            </el-form-item>
            <el-form-item label="模版发送人名称：">{{ form.templateNickname }}</el-form-item>
            <el-form-item label="消息内容：">{{ form.templateContent }}</el-form-item>
            <el-col :span="24">
            <el-form-item label="发送状态：">
              <dict-tag :type="DICT_TYPE.SYSTEM_SOCIAL_MESSAGE_SEND_STATUS" :value="form.sendStatus"/>
            </el-form-item>
          </el-col>
            <el-form-item label="发送时间：">{{ parseTime(form.sendTime) }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSocialMessage, getSocialMessagePage, exportSocialMessageExcel,resendSocialMessage } from "@/api/system/social/message";

export default {
  name: "SocialMessage",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 三方消息列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        userId: null,
        userType: null,
        templateId: null,
        templateCode: null,
        templateNickname: null,
        templateContent: null,
        templateType: null,
        templateParams: null,
        sendStatus: null,
        sendTime: [],
        createTime: [],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [{ required: true, message: "用户id不能为空", trigger: "blur" }],
        userType: [{ required: true, message: "用户类型不能为空", trigger: "change" }],
        templateId: [{ required: true, message: "模版编号不能为空", trigger: "blur" }],
        templateCode: [{ required: true, message: "模板编码不能为空", trigger: "blur" }],
        templateNickname: [{ required: true, message: "模版发送人名称不能为空", trigger: "blur" }],
        templateContent: [{ required: true, message: "模版内容不能为空", trigger: "blur" }],
        templateType: [{ required: true, message: "模版类型不能为空", trigger: "change" }],
        templateParams: [{ required: true, message: "模版参数不能为空", trigger: "blur" }],
        sendStatus: [{ required: true, message: "发送状态不能为空", trigger: "blur" }],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getSocialMessagePage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        userId: undefined,
        userType: undefined,
        templateId: undefined,
        templateCode: undefined,
        templateNickname: undefined,
        templateContent: undefined,
        templateType: undefined,
        templateParams: undefined,
        sendStatus: undefined,
        sendTime: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有三方消息数据项?').then(() => {
        this.exportLoading = true;
        return exportSocialMessageExcel(params);
      }).then(response => {
        this.$download.excel(response, '三方消息.xls');
        this.exportLoading = false;
      }).catch(() => { });
    },
    /** 详细按钮操作 */
    handleView(row) {
      this.open = true;
      this.form = row;
    },
    async resend(row) {
      await resendSocialMessage(row.id);
      this.$modal.msgSuccess('重发成功');
      this.getList();
  }
  }
};
</script>
