<template>
  <div class="app-container">
    <el-empty v-if="!configInfo.id && formMode === 'init'">
      <el-button type="text" icon="el-icon-plus" @click="beginConfig">开始配置</el-button>
    </el-empty>
    <div v-if="configInfo.id && formMode === 'init'">
      <el-descriptions title="配置信息" :column="2" :border="true">
        <template slot="extra">
          <el-button type="primary" size="small" @click="editConfig">修改</el-button>
        </template>
        <el-descriptions-item label="全称">{{ configInfo.fullName || '--' }}</el-descriptions-item>
        <el-descriptions-item label="简称">{{ configInfo.name || '--' }}</el-descriptions-item>
        <el-descriptions-item label="编码">{{ configInfo.code || '--' }}</el-descriptions-item>
        <el-descriptions-item label="LOGO">
          <el-image v-if="configInfo.logoUrl" style="width: 100px;" :src="configInfo.logoUrl" fit="contain"></el-image>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item label="联系人">{{ configInfo.contactName || '--' }}</el-descriptions-item>
        <el-descriptions-item label="联系人电话">{{ configInfo.contactMobile || '--' }}</el-descriptions-item>
        <el-descriptions-item label="包邮金额">
          <el-tag v-if="configInfo.freightThreshold" type="primary">订单金额满{{ formatMoney(configInfo.freightThreshold) }}元包邮</el-tag>
          <el-tag v-else type="success">包邮无限制</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="运费">
          <el-tag v-if="configInfo.freight" type="primary">低于包邮金额运费为{{ formatMoney(configInfo.freight) }}元</el-tag>
          <el-tag v-else type="success">无运费</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="起售金额">
          <el-tag v-if="configInfo.saleAmountMin" type="primary">订单金额最少{{ formatMoney(configInfo.saleAmountMin) }}元起售</el-tag>
          <el-tag v-else type="success">无限制</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="sku配额" v-if="configInfo.type !== 1">
          <el-tag>总数：{{ configInfo.skuLimit || '未设置' }}</el-tag>
          <el-tag style="margin-left:10px;">上架数量：{{ configInfo.onSaleSkuLimit || '未设置' }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="推荐指数"><el-tag>{{ configInfo.recommendIndex || '--' }}</el-tag></el-descriptions-item>
        <el-descriptions-item label="客服电话">{{ configInfo.servicePhone || '--' }}</el-descriptions-item>
        <el-descriptions-item label="客服名称">{{ configInfo.serviceAgent || '--' }}</el-descriptions-item>
        <el-descriptions-item label="客服微信号">{{ configInfo.serviceWechatId || '--' }}</el-descriptions-item>
        <el-descriptions-item label="客服QQ号">{{ configInfo.serviceQqId || '--' }}</el-descriptions-item>
        <el-descriptions-item label="退货地址">{{ configInfo.returnAddress || '--' }}</el-descriptions-item>
        <el-descriptions-item label="类型"><dict-tag :type="DICT_TYPE.MALL_SUPPLIER_TYPE" :value="configInfo.type" /></el-descriptions-item>
        <el-descriptions-item label="运营状态"><dict-tag :type="DICT_TYPE.MALL_SUPPLIER_STATUS" :value="configInfo.status" /></el-descriptions-item>
        <el-descriptions-item label="排序"><el-tag>{{ configInfo.sort || '--' }}</el-tag></el-descriptions-item>
        <el-descriptions-item label="订单消息订阅" v-if="configInfo.supplierType !== 1">
          <el-tag :type="configInfo.subMessage ? 'primary' : 'info'" >{{ configInfo.subMessage ? '打开' : '关闭' }}</el-tag>
          <span> 如果关闭，则不会推送订单消息</span>
        </el-descriptions-item>
        <el-descriptions-item label="多规格商品显示开关" v-if="configInfo.supplierType !== 1">
          <el-tag :type="configInfo.multipleSpec ? 'primary' : 'info'" >{{ configInfo.multipleSpec ? '打开' : '关闭' }}</el-tag>
          <span> 如果关闭，则不会显示多规格商品</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <el-form v-if="formMode === 'edit'" ref="configForm" label-position="left" :model="configForm" :rules="configRules"
      label-width="150px" style="width:1000px">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="全称" prop="fullName">
            <el-input v-model="configForm.fullName" maxlength="100" placeholder="请输入全称" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="简称" prop="name">
            <el-input v-model="configForm.name" maxlength="100" placeholder="请输入简称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="编码" prop="code">
            <el-input v-model="configForm.code" maxlength="50" placeholder="请输入编码" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="Logo" prop="logoUrl">
        <imageUpload v-model="configForm.logoUrl" :limit="1" :fileSize="1"/>
        <span>(尺寸: 200 * 100)</span>
      </el-form-item>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="包邮金额" prop="freightThreshold">
            <el-input-number v-model="configForm.freightThreshold" :min="0" :max="1000" placeholder="请输入金额" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮费" prop="freight">
            <el-input-number v-model="configForm.freight" :min="0" :max="1000" placeholder="请输入邮费" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="起售金额" prop="saleAmountMin">
            <el-input-number v-model="configForm.saleAmountMin" :min="0" :max="1000" placeholder="请输入金额" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" v-if="configForm.type !== 1">
        <el-col :span="12">
          <el-form-item label="授权sku总数" prop="skuLimit">
            <el-input-number v-model="configForm.skuLimit" :min="0" :max="100000" placeholder="请输入sku总数" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="授权上架sku数量" prop="onSaleSkuLimit">
            <el-input-number v-model="configForm.onSaleSkuLimit" :min="0" :max="configForm.skuLimit || 2000" placeholder="请输入上架sku数量" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactName">
            <el-input v-model="configForm.contactName" maxlength="20" placeholder="请输入联系人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系人电话" prop="contactMobile">
            <el-input v-model="configForm.contactMobile" maxlength="20" placeholder="请输入联系人电话" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="客服电话" prop="servicePhone">
            <el-input v-model="configForm.servicePhone" maxlength="20" placeholder="请输入客服电话" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客服名称" prop="serviceAgent">
            <el-input v-model="configForm.serviceAgent" maxlength="50" placeholder="请输入客服名称" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="客服微信号" prop="serviceWechatId">
            <el-input v-model="configForm.serviceWechatId" maxlength="50" placeholder="请输入客服电话" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客服QQ号" prop="serviceQqId">
            <el-input v-model="configForm.serviceQqId" maxlength="50" placeholder="请输入退货地址" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="退货地址" prop="returnAddress">
            <el-input v-model="configForm.returnAddress" maxlength="200" placeholder="请输入退货地址" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="类型" prop="type">
            <el-radio-group v-model="configForm.type" :disabled="!!configForm.id">
              <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.MALL_SUPPLIER_TYPE)"
                        :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="推荐指数" prop="recommendIndex">
            <el-input-number v-model="configForm.recommendIndex" :min="0" :max="1000" placeholder="请输入推荐指数" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="运营状态" prop="status">
            <el-radio-group v-model="configForm.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="2">停用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序(越小越靠前)" prop="sort">
            <el-input-number v-model="configForm.sort" :min="0" :max="1000" placeholder="请输入排序" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="订单消息订阅" prop="subMessage" v-if="configInfo.supplierType !== 1">
        <el-switch v-model="configForm.subMessage" active-text="打开" inactive-text="关闭" />
      </el-form-item>

      <el-form-item label="多规格商品显示开关" prop="multipleSpec" v-if="configInfo.supplierType !== 1">
        <el-switch v-model="configForm.multipleSpec" active-text="打开" inactive-text="关闭" />
      </el-form-item>

      <el-form-item>
        <el-button type="default" @click="cancelForm">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">提 交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import ImageUpload from '@/components/ImageUpload';
import * as api from "@/api/mall/config/supplier";
export default {
  name: "MallConfigSupplierBasis",
  components: {ImageUpload},
  props: {
    id: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      configInfo: {},
      formMode: 'init',
      submitLoading: false,
      configForm: {
        id: undefined,
        type: undefined,
        fullName: '',
        name: '',
        code: '',
        freightThreshold: 68,
        freight: 10,
        saleAmountMin: 0,
        onSaleSkuLimit: 5000,
        skuLimit: 2000,
        contactName: '',
        contactMobile: '',
        servicePhone: '',
        serviceAgent: '',
        serviceWechatId: '',
        serviceQqId: '',
        returnAddress: '',
        status: 1,
        sort: 10,
        recommendIndex: 100,
        subMessage: true,
        multipleSpec: false
      },
      configRules: {
        type: [
          { required: true, trigger: 'change', message: '请选择类型' }
        ],
        fullName: [
          { required: true, trigger: 'blur', message: '请输入全称' }
        ],
        name: [
          { required: true, trigger: 'blur', message: '请输入简称' }
        ],
        code: [
          { required: true, trigger: 'blur', message: '请输入编码' }
        ],
        freightThreshold: [
          { required: true, trigger: 'blur', message: '请输入包邮金额' }
        ],
        freight: [
          { required: true, trigger: 'blur', message: '请输入邮费' }
        ],
        saleAmountMin: [
          { required: true, trigger: 'blur', message: '请输入起售金额' }
        ],
        onSaleSkuLimit: [
          { required: true, trigger: 'blur', message: '请输入上架商品最大数量' }
        ],
        recommendIndex: [
           { required: true, trigger: 'change', message: '请输入推荐指数' },
           { type: 'number', max: 1000, trigger: 'change', message: '推荐指数不能超过1000' }
        ],
        skuLimit: [
          { required: true, trigger: 'blur', message: '请输入商品最大数量' }
        ],
        contactName: [
          { required: true, trigger: 'blur', message: '请输入联系人' }
        ],
        contactMobile: [
          { required: true, trigger: 'blur', message: '请输入联系人电话' }
        ],
        servicePhone: [
          { required: true, trigger: 'blur', message: '请输入客服电话' }
        ],
        status: [
          { required: true, trigger: 'blur', message: '请选择运营状态' }
        ],
        sort: [
          { required: true, trigger: 'blur', message: '请输入排序' }
        ]
      }
    }
  },
  filters: {
    statusInfo(val) {
      const dic = [
        { key: 0, label: '未配置' },
        { key: 1, label: '启用' },
        { key: 2, label: '停用' }
      ]
      let item = dic.find(item => item.key === val)
      return item ? item.label : '--'
    }
  },
  methods: {
    beginConfig() {
      this.formMode = 'edit'
    },
    async loadConfig() {
      if(!this.id) {
        return
      }
      let res = await api.getSupplierInfo(this.id);
      this.configInfo = res.data || {}
    },
    editConfig() {
      this.formMode = 'edit'
      Object.assign(this.configForm, this.configInfo)
    },
    cancelForm() {
      this.formMode = 'init'
    },
    async doSubmit() {
      this.submitLoading = true
      try {
        if (this.configForm.id !== undefined) {
          await api.updateSupplier(this.configForm)
          this.$modal.msgSuccess("修改成功");
        } 
        this.submitLoading = false
        this.formMode = 'init'
        this.loadConfig()
      } catch(e) {
        this.$modal.msgError("保存失败，请稍后重试")
        this.submitLoading = false
      }
    },
    submitForm() {
      this.$refs["configForm"].validate(valid => {
        if (!valid) {
          return;
        }
        this.doSubmit()
      });
    }
  }
}
</script>

<style lang="scss" scoped></style>