import request from '@/utils/request'

// 查询配置分组列表
export function getConfigGroupList() {
  return request({
    url: '/system/tenant-config-group/list-all',
    method: 'get',
  })
}

// 创建配置分组
export function createConfigGroup(data) {
  return request({
    url: '/system/tenant-config-group/create',
    method: 'post',
    data: data
  })
}

// 更新配置分组
export function updateConfigGroup(data) {
  return request({
    url: '/system/tenant-config-group/update',
    method: 'put',
    data: data
  })
}

// 查询配置分组详细
export function getConfigGroup(groupId) {
  return request({
    url: '/system/tenant-config-group/get?id=' + groupId,
    method: 'get'
  })
}


// 删除租户
export function deleteConfigGroup(id) {
  return request({
    url: '/system/tenant-config-group/delete?id=' + id,
    method: 'delete'
  })
}

// 查询配置项列表
export function getConfigItemList() {
  return request({
    url: '/system/tenant-config-item/list-all',
    method: 'get',
  })
}

// 查询配置项详细
export function getConfigItem(itemId) {
  return request({
    url: '/system/tenant-config-item/get?id=' + itemId,
    method: 'get'
  })
}

// 批量创建配置项
export function createBatchConfigItem(data) {
  return request({
    url: '/system/tenant-config-item/create-batch',
    method: 'post',
    data: data
  })
}

// 批量更新配置分组
export function updateBatchConfigItem(data) {
  return request({
    url: '/system/tenant-config-item/update-batch',
    method: 'put',
    data: data
  })
}

// 创建配置项
export function createConfigItem(data) {
  return request({
    url: '/system/tenant-config-item/create',
    method: 'post',
    data: data
  })
}

// 更新配置分组
export function updateConfigItem(data) {
  return request({
    url: '/system/tenant-config-item/update',
    method: 'put',
    data: data
  })
}

// 删除配置项
export function deleteConfigItem(id) {
  return request({
    url: '/system/tenant-config-item/delete?id=' + id,
    method: 'delete'
  })
}
