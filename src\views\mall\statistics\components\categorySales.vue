<template>
  <div class="app-container">
    <el-alert title="展示近30天的各级商品分类的销量和销售额" type="info" style="margin-bottom: 24px;"></el-alert>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="商品分类" prop="createTime">
        <el-select v-model="queryParams.categoryLevel" @change="getList">
          <el-option :value="1" label="一级"></el-option>
          <el-option :value="2" label="二级"></el-option>
          <el-option :value="3" label="三级"></el-option>
        </el-select>
      </el-form-item>
    </el-form>

    <div id="category-echart" class="category-echart"></div>
    <div id="sales-echart" style="margin-top: 16px;" class="category-echart"></div>
  </div>
</template>

<script>
import * as api from "@/api/mall/statistics/commodity";
import SupplierSelect from "@/views/mall/product/spu/components/supplier-select";
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  components: {
    SupplierSelect
  },
  data() {
    return {
      showSearch: true,
      // 查询参数
      queryParams: {
        categoryLevel: 1
      },
      chart: null,
      chart1: null
    };
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      if (!this.queryParams.categoryLevel) {
        this.$message.error('请选择商品分类')
        return
      }
      api.getSalesAndRevenueInLastDaysByProductCategory({
        days: 30,
        categoryLevel: this.queryParams.categoryLevel
      }).then(response => {
        if (response.code === 0) {
          if (response.data.length > 0) {
            this.initChart(response.data)
          } else {
            if (this.chart) {
              this.chart.dispose()
              this.chart = null
              this.$message.info('当前范围内没有数据')
            }
            if (this.chart1) {
              this.chart1.dispose()
              this.chart1 = null
              this.$message.info('当前范围内没有数据')
            }
          }
        }
      })
    },
    initChart(data) {
      const chartDom = document.getElementById('category-echart');
      this.chart = echarts.init(chartDom, 'macarons')
      this.chart.setOption({
        legend: {
          data: ['销量']
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        grid: {
          left: '1%',
          right: '1%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.map(x => x.categoryName)
        },
        yAxis: {
          type: 'value'
        },
        series: [
          { 
            name: '销量',
            type: 'bar',
            data: data.map(x => x.salesVolume)
          }
        ]
      })
      const chartDom1 = document.getElementById('sales-echart');
      this.chart1 = echarts.init(chartDom1, 'macarons')
      this.chart1.setOption({
        legend: {
          data: ['销售额']
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        grid: {
          left: '1%',
          right: '1%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.map(x => x.categoryName)
        },
        yAxis: {
          type: 'value'
        },
        series: [
          { 
            name: '销售额',
            type: 'bar',
            data: data.map(x => x.salesRevenue),
            itemStyle: {  
              color: '#ffa500'
            }
          }
        ]
      })
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    if (this.chart1) {
      this.chart1.dispose()
      this.chart1 = null
    }
  }
};
</script>

<style lang="scss" scoped>
.category-echart {
  width: 100%;
  height: 400px;
}
</style>