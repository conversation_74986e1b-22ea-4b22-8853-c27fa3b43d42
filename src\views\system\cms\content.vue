<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" class="order-form" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="分类">
        <el-cascader ref="category" :show-all-levels="true" :props="cascaderProps" v-model="queryParams.categoryIds"
          :options="categoryList" clearable></el-cascader>
      </el-form-item>
      <el-form-item label="标题">
        <el-input v-model="queryParams.title" clearable placeholder="请输入标题"></el-input>
      </el-form-item>
      <el-form-item label="内容编码">
        <el-input v-model="queryParams.code" clearable placeholder="请输入内容编码"></el-input>
      </el-form-item>
      <el-form-item label="发布状态">
        <el-select v-model="queryParams.publishStatus" clearable>
          <el-option v-for="item in statusList1" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
      <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addContent" v-hasPermi="['system:cms-content:create']">新增
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="contentPageList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="list" border>
      <el-table-column label="分类" align="center" prop="categoryNames" width="280">
        <template v-slot="scope">
          <span>{{ scope.row.categoryNames | formatCategoryName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="标题" align="center" prop="title">
        <template v-slot="scope">
          <el-tag v-if="scope.row.sort === 1" type="warning" size="small">置顶</el-tag>
          <el-button type="text" @click="previewContent(scope.row.id)">{{scope.row.title}}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="内容编码" align="center" prop="code"></el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="200">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发布状态" align="center" prop="publishStatus" width="160">
        <template v-slot="scope">
          <el-tag :type="scope.row.publishStatus === 0 ? 'info' : ''">{{scope.row.publishStatus | publishStatusInfo}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="开启状态" align="center" prop="status" width="160">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" class-name="small-padding fixed-width" width="220">
        <template v-slot="scope">
          <el-button v-if="scope.row.sort != 1" size="medium" type="text" @click="updateContentSort(scope.row, 1)">置顶</el-button>
          <el-button v-if="scope.row.sort === 1" size="medium" type="text" @click="updateContentSort(scope.row, 5)">取消置顶</el-button>
          <el-button v-if="scope.row.publishStatus === 0" size="medium" type="text" @click="edit(scope.row.id)">编辑</el-button>
          <el-button v-if="scope.row.publishStatus === 0" size="medium" type="text" @click="deleteContent(scope.row.id)">删除</el-button>
          <el-button v-if="scope.row.publishStatus !== 1" size="medium" type="text" @click="updatePublishStatus(scope.row)">发布</el-button>
          <el-button v-if="scope.row.publishStatus === 1" size="medium" type="text" @click="updatePublishStatus(scope.row)">下架</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
      @pagination="contentPageList" />

    <Preview ref="contentPreview" :open.sync="previewOpen" :contentId="curContentId" />
  </div>
</template>

<script>
import * as categoryApi from "@/api/system/cms/category.js";
import * as api from "@/api/system/cms/content.js";
import Preview from "@/views/system/cms/preview";
export default {
  name: "CmsContent",
  components: { Preview },
  data() {
    return {
      previewOpen: false,
      curContentId: null,
      cascaderProps: {
        label: 'categoryName',
        value: 'id',
        checkStrictly: true
      },
      // 遮罩层
      loading: false,
      showSearch: true,
      // 总条数
      total: 0,
      list: [],
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        title: '',
        code: '',
        publishStatus: '',
        status: '',
        categoryIds: []
      },
      categoryList: [],
      statusList1: [{
        label: '未发布',
        value: 0
      }, {
        label: '已发布',
        value: 1
      }],
      statusList2: [{
        label: '停用',
        value: 0
      }, {
        label: '启用',
        value: 1
      }]
    };
  },
  filters: {
    publishStatusInfo (val) {
      let dic = {0: '未发布', 1: '已发布'}
      return dic[val] || '未知'
    },
    formatCategoryName (names) {
      if (names && names.length) {
        return names.join("/")
      }
      return ""
    }
  },
  created() {
    this.queryCategoryList()
    this.contentPageList();
  },
  methods: {
    async queryCategoryList() {
      this.loading = true;
      const res = await categoryApi.getCmsCategoryList({status: 0})
      if (res.code === 0 && res.data) {
        this.categoryList = this.handleTree(res.data, "id", "parentId");
      } else {
        this.$message.error(res.msg)
      }
      this.loading = false
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.contentPageList();
    },
    resetQuery() {
      this.$refs.category.$refs.panel.checkedValue = [];//也可以是指定的值，默认返回值是数组，也可以返回单个值
      this.$refs.category.$refs.panel.activePath = [];
      this.$refs.category.$refs.panel.syncActivePath();
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        title: '',
        publishStatus: '',
        status: '',
        categoryIds: [] 
      }
      this.contentPageList();
    },
    previewContent(cid) {
      this.curContentId = cid
      this.previewOpen = true
    },
    addContent() {
      this.$router.push('addContent')
    },
    edit(id) {
      this.$router.push({
        path: 'editContent',
        query: {
          id
        } 
      })
    },
    async updateContentSort(row, sort) {
      let params = {
        id: row.id,
        sort: sort
      }
      let res = await api.updateCmsContentSort(params)
      if(res.code === 0) {
        this.$modal.msgSuccess("操作成功");
        this.contentPageList();
      }
    },
    async deleteContent(id) {
      this.$modal
        .confirm('是否确认删除内容编号为"' + id + '"的数据项?')
        .then(function () {
          return api.deleteCmsContent(id);
        }).then(() => {
          this.contentPageList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    async updatePublishStatus(row) {
      let params = {
        id: row.id,
        publishStatus: row.publishStatus === 0 ? 1 : 0
      }
      const res =await api.updateCmsContentPublish(params)
      if (res.code === 0) {
        this.contentPageList();
        this.$message.success('操作成功')
      }
    },
    async contentPageList() {
      this.loading = true;
      const params = {
        pageNo: this.queryParams.pageNo,
        pageSize: this.queryParams.pageSize
      }
      if (this.queryParams.title) {
        params.title = this.queryParams.title
      }
      if (this.queryParams.code) {
        params.code = this.queryParams.code
      }
      params.categoryIdPath = JSON.stringify(this.queryParams.categoryIds);
      if (this.queryParams.status !== '') {
        params.status = this.queryParams.status
      }
      if (this.queryParams.publishStatus !== '') {
        params.publishStatus = this.queryParams.publishStatus
      }
      const res = await api.getCmsContentPage(params)
      if (res.code === 0) {
        this.list = res.data.list
        this.total = Number(res.data.total)
      } else {
        this.list = []
        this.total = 0
        this.$message.error(res.msg)
      }
      this.loading = false
    },
    formatStatus1(row, column, cellValue) {
      const status = ['未发布', '已发布' || 0]
      return status[cellValue]
    }
  }
}
</script>
<style lang="scss" scoped>
.app-container {
  position: relative;
  background-color: #fff;
}
</style>