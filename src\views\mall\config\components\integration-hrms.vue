<template>
  <div class="app-container">
    <el-empty v-if="!configInfo.id && formMode === 'init'">
      <el-button type="text" icon="el-icon-plus" @click="beginConfig">开始配置</el-button>
    </el-empty>
    <div v-if="configInfo.id && formMode === 'init'">
      <el-descriptions title="配置信息" :column="1" :border="true">
        <template slot="extra">
          <el-button type="primary" size="small" @click="editConfig">修改</el-button>
        </template>
        <el-descriptions-item label="系统名称">{{ configInfo.sysName || '--' }} / {{ configInfo.sysCode || '--' }}</el-descriptions-item>
        <el-descriptions-item label="状态"><dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="configInfo.status" /></el-descriptions-item>
        <el-descriptions-item label="接口地址">{{ configInfo.serverUrl || '--' }}</el-descriptions-item>
        <el-descriptions-item label="应用接入密钥1">{{ configInfo.key1 || '--' }}</el-descriptions-item>
        <el-descriptions-item label="应用接入密钥2">{{ configInfo.key2 || '--' }}</el-descriptions-item>
        <el-descriptions-item label="应用接入密钥3">{{ configInfo.key3 || '--' }}</el-descriptions-item>
        <el-descriptions-item label="上级部门编码">{{ configInfo.rootDeptCode || '--' }}</el-descriptions-item>
        <el-descriptions-item label="扩展参数1">{{ configInfo.paramExt1 || '--' }}</el-descriptions-item>
        <el-descriptions-item label="扩展参数2">{{ configInfo.paramExt2 || '--' }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ configInfo.memo || '--' }}</el-descriptions-item>
        
      </el-descriptions>
    </div>
    <el-form v-if="formMode === 'edit'" ref="configForm" :model="configForm" :rules="configRules"
      label-width="150px" style="width:700px">
      <el-form-item label="对接系统" prop="sysCode">
        <el-select v-model="configForm.sysCode" placeholder="请选择系统" style="width:100%">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MALL_INTEGRATION_HRMS_TYPE)"
                  :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="configForm.status">
          <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
        </el-radio>
      </el-radio-group>
      </el-form-item>
      <el-form-item label="接口地址" prop="serverUrl">
        <el-input v-model="configForm.serverUrl" :maxlength="100" placeholder="请输入接口地址" />
      </el-form-item>
      <el-form-item label="应用接入密钥1" prop="key1">
        <el-input v-model="configForm.key1" :maxlength="100" placeholder="请输入应用接入密钥1" />
      </el-form-item>
      <el-form-item label="应用接入密钥2" prop="key2">
        <el-input v-model="configForm.key2" :maxlength="100" placeholder="请输入应用接入密钥2" />
      </el-form-item>
      <el-form-item label="应用接入密钥3" prop="key3">
        <el-input v-model="configForm.key3" :maxlength="100" placeholder="请输入应用接入密钥3" />
      </el-form-item>
      <el-form-item label="上级部门编码" prop="rootDeptCode">
        <el-input v-model="configForm.rootDeptCode" :maxlength="50" placeholder="请输入上级部门编码" />
      </el-form-item>
      <el-form-item label="扩展参数1" prop="paramExt1">
        <el-input v-model="configForm.paramExt1" :maxlength="100" placeholder="请输入扩展参数1" />
      </el-form-item>
      <el-form-item label="扩展参数2" prop="paramExt2">
        <el-input v-model="configForm.paramExt2" :maxlength="100" placeholder="请输入扩展参数2" />
      </el-form-item>
      <el-form-item label="备注" prop="memo">
        <el-input v-model="configForm.memo" :maxlength="150" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item>
        <el-button type="default" @click="cancelForm">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">提 交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import * as api from "@/api/mall/config/hrmsConfig";
export default {
  name: "MallHrmsConfig",
  components: {},
  data() {
    return {
      configInfo: {},
      formMode: 'init',
      submitLoading: false,
      configForm: {
        id: undefined,
        sysName: '',
        sysCode: '',   
        serverUrl: '',     
        status: 0,
        memo: '',
        rootDeptCode: null,
        key1: null,
        key2: null,
        key3: null,
        paramExt1: null,
        paramExt2: null
      },
      configRules: {
        sysCode: [
          { required: true, trigger: 'change', message: '请选择系统' }
        ],
        serverUrl: [
          { required: true, trigger: 'blur', message: '请输入接口地址' },
          { type: 'url', trigger: 'blur', message: '接口地址格式不正确' }
        ]
      },
    }
  },
  computed: {
    sysName() {
      return this.getDictDataLabel(this.DICT_TYPE.MALL_INTEGRATION_HRMS_TYPE, this.configForm.sysCode) || ''
    }
  },
  created() {
    this.loadConfig()
  },
  methods: {
    beginConfig() {
      this.formMode = 'edit'
    },
    async loadConfig() {
      let res = await api.getHrmsConfig();
      this.configInfo = res.data || {}
    },
    editConfig() {
      this.formMode = 'edit'
      Object.assign(this.configForm, this.configInfo)
    },
    cancelForm() {
      this.formMode = 'init'
    },
    async doSubmit() {
      this.submitLoading = true
      try {
        this.configForm.sysName = this.sysName
        if (this.configForm.id !== undefined) {
          await api.updateHrmsConfig(this.configForm)
          this.$modal.msgSuccess("修改成功");
        } else {
          await api.createHrmsConfig(this.configForm)
          this.$modal.msgSuccess("保存成功");
        }
        this.submitLoading = false
        this.formMode = 'init'
        this.loadConfig()
      } catch(e) {
        this.submitLoading = false
      }
    },
    submitForm() {
      this.$refs["configForm"].validate(valid => {
        if (!valid) {
          return;
        }
        this.doSubmit()
      });
    }
  }
}
</script>

<style lang="scss" scoped></style>