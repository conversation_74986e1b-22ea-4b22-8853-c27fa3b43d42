<template>
  <div class="app-container">
    <el-card>
    <el-empty v-if="!configInfo.id && formMode === 'init'">
      <el-button type="text" icon="el-icon-add" @click="beginConfig">开始配置</el-button>
    </el-empty>
    <el-row>
      <el-col :span="24" :offset="0">
        <BasisConfigView ref="basisConfigView" :configInfo="configInfo" v-if="formMode === 'init'" @edit="editConfig" />
        <BasisConfigForm ref="basisConfigForm" @update="updateConfig" v-if="formMode === 'edit'" @cancel="cancelForm" />
      </el-col>
    </el-row>
    </el-card>
  </div>
</template>

<script>
import * as api from "@/api/mall/config/basisConfig"
import BasisConfigView from '@/views/mall/config/components/basis-config-view'
import BasisConfigForm from '@/views/mall/config/components/basis-config-form'
export default {
  name: "MallBasisConfig",
  components: { BasisConfigView, BasisConfigForm },
  data() {
    return {
      configInfo: {},
      formMode: "init"
    };
  },
  created() {
    this.loadConfig()
  },
  methods: {
    beginConfig() {
      this.formMode = "edit"
    },
    cancelForm() {
      this.formMode = "init"
    },
    editConfig() {
      this.formMode = "edit"
      this.$nextTick(() => {
        this.$refs.basisConfigForm.init(this.configInfo)
      })
    },
    updateConfig() {
      this.formMode = "init"
      this.loadConfig()
    },
    async loadConfig() {
      let res = await api.getBasisConfig()
      this.configInfo = res.data || {}

      if (this.configInfo.payMethod) {
        this.configInfo.payMethodList = this.configInfo.payMethod.split(",")
      } else {
        this.configInfo.payMethodList = []
      }

      if (this.configInfo.loginMethod) {
        this.configInfo.loginMethodList = this.configInfo.loginMethod.split(",")
      } else {
        this.configInfo.loginMethodList = []
      }

      if (this.configInfo.productField) {
        this.configInfo.productField = this.configInfo.productField.split(",")
      } else {
        this.configInfo.productField = []
      }

      if (this.configInfo.domain) {
        this.configInfo.domainList = this.configInfo.domain.split(",")
      } else {
        this.configInfo.domainList = []
      }

      if (this.configInfo.extConfig) {
        this.configInfo.extConfigItems = JSON.parse(this.configInfo.extConfig)
      } else {
        this.configInfo.extConfigItems = []
      }
    }
  },
};
</script>
