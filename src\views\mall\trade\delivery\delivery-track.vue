<template>
  <div class="app-container">
    <el-descriptions class="margin-top" title="物流详情" :column="3" border>
      <el-descriptions-item>
        <template slot="label"> 物流公司名称 </template>
        {{ form.name || '--' }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label"> 物流公司编码 </template>
        {{ form.com || '--' }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label"> 订单号 </template>
        {{ form.orderNo || '--' }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label"> 物流单号 </template>
        {{ form.num || '--' }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          来源
        </template>
        {{ form.source || '--' }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          签收标记
        </template>
        {{ checkList[form.isCheck] || '--' }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          签收标记
        </template>
        {{ stateList[form.state] || '--' }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          快递单状态
        </template>
        {{ stateList[form.state] || '--' }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          订阅状态
        </template>
        {{ subList[form.subscribe] || '--' }}
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="订单商品" :column="1">
      <el-descriptions-item labelClassName="no-colon">
        <el-table :data="form.skuList">
          <el-table-column label="商品SKU" align="center" prop="skuId" />
          <el-table-column label="商品图片" align="center" prop="picUrl">
            <template slot-scope="{row}">
              <img :src="row.picUrl" alt="" style="width: 48px; height: 48px">
            </template>
          </el-table-column>
          <el-table-column label="商品名称" align="center" prop="skuName" width="400px" show-overflow-tooltip />
          <el-table-column label="下单时间" align="center" prop="createTime" width="300px">
            <template slot-scope="{ row }">
              <div>{{ parseTime(row.createTime) }}</div>
            </template>
          </el-table-column>
        </el-table>
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="包裹详情" :column="1">
      <el-descriptions-item labelClassName="no-colon">
        <!-- 包裹详情 -->
        <el-timeline v-if="form.trackList && form.trackList.length > 0">
          <el-timeline-item v-for="(activity, index) in form.trackList" :key="index"
            :timestamp="parseTime(activity.time)">
            {{ activity.content }}（{{ activity.status }}）
          </el-timeline-item>
        </el-timeline>
        <div v-else>暂无数据</div>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import { getDeliveryDetailByNumAndOrder } from "@/api/mall/trade/delivery";
import { formatDate } from "@/utils/dateUtils";

export default {
  name: "DeliveryTrack",
  data() {
    return {
      // 表单参数
      form: {
        skuList: [],
        trackList: []
      },
      checkList: ['未签收', '已签收'],
      stateList:  ['在途', '揽收', '疑难', '签收', '退签', '派件', '', '', '清关', '', '', '', '', '', '拒签'],
      subList: ['未订阅', '已订阅'],
      deliveryObj: []
    };
  },
  created() {
    this.getData();
  },
  methods: {
    async getData() {
      const res = await getDeliveryDetailByNumAndOrder(this.$route.query.num,this.$route.query.orderNo)
      if (res.code == 0) {
        this.form = res.data
      }
    },
    formatterTime(row) {
      return formatDate(new Date(row.createTime) || new Date(), 'yyyy-MM-dd')
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-descriptions) {
  &:not(:nth-child(1)) {
    margin-top: 20px;
  }
  .el-descriptions-item__container {

    .no-colon {
      margin: 0;

      &::after {
        content: ''
      }
    }
  }
}
</style>