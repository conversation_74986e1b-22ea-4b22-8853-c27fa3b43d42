<template>
  <div>
    <el-dialog
      title="商品SKU"
      :visible.sync="dialogVisible"
      :width="width"
      >
      <div>
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
          <el-form-item label="商品名称" prop="skuName">
            <el-input v-model="queryParams.skuName" placeholder="请输入商品名称" clearable @keyup.enter.native="handleQuery"/>
          </el-form-item>
          <el-form-item label="平台SKU" prop="id">
            <el-input v-model="queryParams.id" oninput="value=value.replace(/^(0+)|[^\d]+/g,'')" placeholder="请输入商品SKU" clearable @keyup.enter.native="handleQuery"/>
          </el-form-item>
          <el-form-item label="三方SKU" prop="skuInnerId">
            <el-input v-model="queryParams.skuInnerId" placeholder="请输入三方SKU" clearable @keyup.enter.native="handleQuery"/>
          </el-form-item>
          <el-form-item label="供应商" prop="supplierId">
            <supplier-select size="small" v-model="queryParams.supplierId" placeholder="请选择供应商" @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="商品分类" prop="categoryIds">
            <category-select v-model="queryParams.categoryIds" :extProps="{checkStrictly: true}" placeholder="请选择商品分类" @keyup.enter.native="handleQuery" />
          </el-form-item>
          <!-- <el-form-item label="商品品牌" prop="brandId">
            <brand-select size="small" v-model="queryParams.brandId" placeholder="请选择商品品牌" @keyup.enter.native="handleQuery" />
          </el-form-item> -->
          <el-form-item label="列出状态" prop="showStatus">
            <el-select v-model="queryParams.showStatus" clearable placeholder="请选择" @keyup.enter.native="handleQuery">
              <el-option :value="1" label="打开"></el-option>
              <el-option :value="0" label="关闭"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="checkPermi(['product:spu:update'])" label="SKU状态" prop="status">
            <el-select v-model="queryParams.status" clearable placeholder="请选择" @keyup.enter.native="handleQuery">
              <el-option :value="0" label="下架"></el-option>
              <el-option :value="1" label="上架"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 列表 -->
        <el-table v-loading="loading" :data="list" 
        @selection-change="handleSelectionChange" 
        @sort-change="handleSortChange"
        :default-sort = "{prop: 'createTime', order: 'descending'}">
          <el-table-column type="selection" align="center" width="55"></el-table-column>
          <el-table-column label="商品信息" align="center" width="260">
            <template v-slot="scope">
              <div class="product-info">
                <img v-if="scope.row.picUrl" :src="scope.row.picUrl" alt="分类图片" class="img-height" />
                <div :title="scope.row.skuName" class="message">{{ scope.row.skuName }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="平台/三方SKU" align="center" prop="id" width="180">
            <template v-slot="scope">
              <span>{{ scope.row.id }}</span><br>
              <span v-if="scope.row.skuInnerId">{{ scope.row.skuInnerId }}</span>
            </template>
          </el-table-column>

          <el-table-column label="销售价格" align="center" prop="salePrice" sortable="custom">
            <template v-slot="scope">
              <span>{{ formatMoney(scope.row.salePrice) }}</span>
          </template>
          </el-table-column>
          <el-table-column label="供应商" align="center" prop="supplierName"/>
          <el-table-column label="品牌" align="center" prop="brandName"/>
          <el-table-column label="商品分类" align="center" prop="fullCategoryName"/>
          <el-table-column label="列出状态" align="center" prop="showStatus">
            <template v-slot="scope">
              <el-tag>{{  scope.row.showStatus ? '显示' : '隐藏' }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="SKU状态" align="center" prop="status">
            <template v-slot="scope">
              <dict-tag :type="DICT_TYPE.PRODUCT_SPU_STATUS" :value="scope.row.status"/>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                    @pagination="getList"/>

      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getSkuPage2 } from '@/api/mall/product/sku'
import SupplierSelect from "@/views/mall/product/spu/components/supplier-select";
import BrandSelect from "@/views/mall/product/spu/components/brand-select";
import CategorySelect from "@/views/mall/product/spu/components/category-select";
export default {
  name: 'ProductSkuSelect',
  components: { SupplierSelect, BrandSelect, CategorySelect},
  props: {
    width: {
      type: String,
      default: () => '1350px'
    },
    radio: {
      type: Boolean,
      default() {
        return false
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商品spu列表
      list: [],
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        skuInnerId: null,
        skuName: null,
        seoStatus: 0,
        id: null,
        categoryIds: [],
        supplierId: null,
        showStatus: null,
        brandId: null,
        status: null
      },
      multipleSelection: []
    }
  },
  methods: {
    init(extraParam = {}) {
      Object.assign(this.queryParams, extraParam)
      this.dialogVisible = true
      this.handleQuery()
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 处理查询参数
      let params = {...this.queryParams};
      if(!params.skuId)  delete params.skuId
      if(!params.skuInnerId) delete params.skuInnerId
      if(params.categoryIds && params.categoryIds.length) {
        params.fullCategoryId = params.categoryIds.join('-')
      }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, "createTime");
      // 执行查询
      getSkuPage2(params).then((response) => {
        this.list = ((response.data && response.data.list) || []).map(item => {
          if (item.showStatus === undefined) {
            item.showStatus = 1
          }
          return item
        });
        this.total = parseInt(response.data.total);
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleSortChange({ prop, order }) {
      this.queryParams.sortType = 10
      if(prop === 'createTime' && order === 'descending') {
        this.queryParams.sortType = 10
      } else if(prop === 'createTime' && order === 'ascending') {
        this.queryParams.sortType = 11
      } else if(prop === 'salesCount' && order === 'descending') {
        this.queryParams.sortType = 20
      } else if(prop === 'salesCount' && order === 'ascending') {
        this.queryParams.sortType = 21
      } else if(prop === 'salePrice' && order === 'descending') {
        this.queryParams.sortType = 40
      } else if(prop === 'salePrice' && order === 'ascending') {
        this.queryParams.sortType = 41
      } 
      console.log(prop, order)
      this.handleQuery()
    },
    submit() {
      if(this.radio && this.multipleSelection.length !== 1) {
        this.$message.warning("请仅选择一个商品")
        return;
      }
      if (!this.multipleSelection.length) {
        this.$message.warning("请至少选择一个商品")
        return;
      }
      this.$emit('on-confirmed', this.multipleSelection)
      this.dialogVisible = false
    }
  }
}
</script>

<style>

</style>