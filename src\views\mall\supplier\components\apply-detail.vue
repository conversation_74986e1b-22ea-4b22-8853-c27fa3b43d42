<template>
  <div class="app-container" v-loading="loading">
    <el-card>
    <div class="flex-center"> 
      <ApplySteps ref="applySteps" :status="approveStatus" style="width:95%;margin:20px 0 30px"></ApplySteps>
    </div>
    <div v-if="approveStatus === 3" style="margin: 10px 0"> 
      <el-alert title="审批驳回" type="error" show-icon
        :description="`您的入驻申请被驳回，驳回原因：${detailData.approveMemo}`" >
      </el-alert>
    </div>
    <el-descriptions :column="1" border :labelStyle="{width: '150px'}">
      <template slot="extra">
        <div v-hasPermi="['mall:supplier-form:user']" v-if="role === 'user'"> 
          <el-button :loading="loading2"  v-if="[0,3].includes(approveStatus)" type="primary" size="small" @click="edit">继续修改</el-button>
          <el-button :loading="loading2"  v-if="[0].includes(approveStatus)" type="primary" size="small" @click="submit">确认并提交</el-button>
        </div>
        <div v-hasPermi="['mall:supplier-form:approve']"> 
          <el-button :loading="loading2" v-if="approveStatus === 1" type="warning" size="small" @click="approve(3)">驳回申请</el-button>
          <el-button :loading="loading2" v-if="approveStatus === 1" type="primary" size="small" @click="approve(2)">通过申请</el-button>
        </div>
      </template>
      <el-descriptions-item label="统一社会信用代码">{{ detailData.enterpriseUnifiedId }}</el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag :type="approveStatus | supplierFormStatusStyle">{{ approveStatus | supplierFormStatusInfo }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="企业全称">{{ detailData.enterpriseFullName }}</el-descriptions-item>
      <el-descriptions-item label="企业简称">{{ detailData.enterpriseShortName }}</el-descriptions-item>
      <el-descriptions-item label="联系人">{{ detailData.contact }}</el-descriptions-item>
      <el-descriptions-item label="联系电话">{{ detailData.phone }}</el-descriptions-item>
      <el-descriptions-item label="提交时间" v-if="detailData.submitTime">{{ parseTime(detailData.submitTime) || '--'}}</el-descriptions-item>
      <el-descriptions-item label="审批时间" v-if="detailData.approveTime">{{ parseTime(detailData.approveTime) || '--'}}</el-descriptions-item>
      <el-descriptions-item label="申请入驻说明">{{ detailData.remark }}</el-descriptions-item>
      <el-descriptions-item label="法人身份证照片" v-if="detailData.fileLegalRep">
        <el-image v-for="(img, index) in detailData.fileLegalRep" :key="index" 
          style="height: 100px" :src="img.url"  fit="fit"
          :preview-src-list="mapUrlArray(detailData.fileLegalRep)">
        </el-image>
      </el-descriptions-item>
      <el-descriptions-item label="营业执照照片">
        <div v-if="detailData.fileLicense && detailData.fileLicense.length">
          <el-image v-for="(img, index) in detailData.fileLicense" :key="index" 
            style="height: 100px" :src="img.url" 
            fit="fit"
            :preview-src-list="mapUrlArray(detailData.fileLicense)">
          </el-image>
        </div>
        <div v-else>未上传</div>
      </el-descriptions-item>
      <el-descriptions-item label="行业资质证明">
        <div v-if="detailData.fileQualification && detailData.fileQualification.length">
          <el-image v-for="(img, index) in detailData.fileQualification" :key="index" 
            style="height: 100px" :src="img.url" 
            fit="fit"
            :preview-src-list="mapUrlArray(detailData.fileQualification)">
          </el-image>
        </div>
        <div v-else>未上传</div>
      </el-descriptions-item>
      <el-descriptions-item label="其它补充材料" v-if="detailData.fileOther">
        <div v-if="detailData.fileOther && detailData.fileOther.length">
          <el-image v-for="(img, index) in detailData.fileOther" :key="index" 
            style="height: 100px" :src="img.url" 
            fit="fit"
            :preview-src-list="mapUrlArray(detailData.fileOther)">
          </el-image>
        </div>
        <div v-else>未上传</div>
      </el-descriptions-item>
    </el-descriptions>

    </el-card>
  </div>
</template>

<script>
import * as api from '@/api/mall/supplier/supplier-form'
import ApplySteps from '@/views/mall/supplier/components/apply-steps'
export default {
  name: 'SupplierApplyFormDetail',
  components: { ApplySteps },
  props: {
    role: {
      type: String,
      default() {
        return 'user'
      }
    },
    id: {
      type: Number,
      default() {
        return null
      }
    }
  },
  data() {
    return {
      loading: false,
      loading2: false,
      detailData: {},
    }
  },
  computed: {
    approveStatus() {
      return this.detailData.approveStatus
    }
  },
  watch: {
    id: {
      immediate: true,
      handler() {
        this.loadSupplierApplyInfo()
      }
    }
  },
  methods: {
    mapUrlArray(items) {
      if(items) {
        return items.map(item => item.url)
      }
      return []
    },
    async loadSupplierApplyInfo() {
      if(!this.id) {
        return
      }
      let params = {
        id: this.id
      }
      let func = api.getUserApplyFormDetail
      if(this.role === 'admin') {
        func = api.getAdminApplyFormDetail
      }
      let res = await func(params)
      this.detailData = res.data || {}
    },
    edit() {
      this.$emit('on-edit', 1)
    },
    async submit() {
      let params = this.id
      this.loading2 = true
      this.$modal.confirm('您确认提交吗?').then(function () {
        return api.submitApplyForm(params);
      }).then((res) => {
        this.loadSupplierApplyInfo()
        this.loading2 = false
        this.$modal.msgSuccess("提交成功")
      }).catch(() => {
        this.loading2 = false
      })
    },
    approve(status) {
      if(!this.id) {
        return
      }
      let params = {
        status: status,
        id: this.id
      }

      if(status === 2) {
        this.loading2 = true
        this.$modal.confirm('您确认审批通过吗?').then(function () {
          return api.approveApplyForm(params);
        }).then((res) => {
          this.loadSupplierApplyInfo()
          this.loading2 = false
          this.$modal.msgSuccess("审批操作成功")
          this.$emit('on-update', 1)
        }).catch(() => {
          this.loading2 = false
        })
      } else if(status === 3) {
        this.$prompt('请输入驳回原因', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^.{2,100}$/,
          inputErrorMessage: '品牌名称长度必须在2-100个字符内'
        }).then(({ value }) => {
          params.memo = value
          this.loading2 = true
          api.approveApplyForm(params).then(res => {
            this.loadSupplierApplyInfo()
            this.$modal.msgSuccess("审批操作成功")
            this.$emit('on-update', 1)
          }).finally(() => {
            this.loading2 = false
          })
       })
      }
    }
  }

}
</script>

<style>

</style>