<template>
    <div class="skuSellCountRanking" :style="{ height: height, width: width }" />
</template>

<script>
import * as api from "@/api/mall/statistics/commodity";
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '300px'
        }
    },
    data() {
        return {
            chart: null
        }
    },
    created() {
        this.getSellProductTotalSummary();
    },
    methods: {
        getSellProductTotalSummary() {
            // 执行查询
            api.getSellProductTotalSummary({}).then(response => {
                if (response.code === 0) {
                    this.initChart(response.data)
                }
            });
        },
        initChart(data) {
            const names = data.map(item => item.productName)
            const values = data.map(item => item.sellTotal)
            this.chart = echarts.init(this.$el, 'macarons')
            this.chart.setOption({
                title: {
                    text: '商品销量Top10',
                    top: 'top',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                yAxis: [
                    {
                        type: 'category',
                        name: '商品',
                        data: names.reverse(),
                        axisTick: {
                            alignWithLabel: true
                        },
                        axisLabel: {
                            formatter: function (value) {
                                return value.length > 10 ? value.substring(0, 10) + '...' : value;
                            }
                        }
                    }
                ],
                xAxis: [
                    {
                        type: 'value',
                        name: '数量',
                        right: 'left'
                    }
                ],
                series: [
                    {
                        name: 'Direct',
                        type: 'bar',
                        barWidth: '60%',
                        data: values.reverse(),
                        itemStyle: {
                            normal: {
                                color: '#ea7ccc'
                            }
                        }
                    }
                ]
            })
        }
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    }
}
</script>