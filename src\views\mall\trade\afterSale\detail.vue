<template>
   <div class="app-container" v-loading="loading">
    <template v-if="detailData">
      <el-tabs  type="card"  style="margin-top: -20px;">
        <el-tab-pane label="售后申请信息"  >
          <el-descriptions :column="1" border :labelStyle="{width: '120px'}">
            <template slot="extra">
              <el-button v-if="detailData.status === 10" type="primary" size="small" @click="approveSuccess">通过申请</el-button>
              <el-button v-if="detailData.status === 10" type="warning" size="small" @click="showRefuseForm">驳回申请</el-button>
              <el-button v-if="[20,30].includes(detailData.status)" type="primary" size="small" @click="approveSuccess">确认收货</el-button>
              <el-button v-if="detailData.status === 30" type="warning" size="small" @click="showRefuseForm">拒绝收货</el-button>
            </template>
            <el-descriptions-item label="售后单号">{{ detailData.no }}</el-descriptions-item>
            <el-descriptions-item label="订单号">
              <el-link type="primary" @click="toOrderDetail">{{ detailData.orderNo }}</el-link>
            </el-descriptions-item>
            <el-descriptions-item label="订单项ID">{{ detailData.orderItemId }}</el-descriptions-item>
            <el-descriptions-item label="申请时间">{{ parseTime(detailData.createTime) || '--'}}</el-descriptions-item>
            <el-descriptions-item label="商品名称">
              <el-image style="height: 30px" :src="detailData.picUrl" fit="fit"></el-image>
              {{ detailData.spuName }}
            </el-descriptions-item>
            <el-descriptions-item label="商品SKU">{{ detailData.skuId }}</el-descriptions-item>

            <el-descriptions-item label="买家用户名">{{ detailData.userName }}</el-descriptions-item>
            <el-descriptions-item label="售后状态">
              <dict-tag :type="DICT_TYPE.TRADE_AFTER_SALE_STATUS" :value="detailData.status" />
            </el-descriptions-item>
            <el-descriptions-item label="售后方式">
              <dict-tag :type="DICT_TYPE.TRADE_AFTER_SALE_WAY" :value="detailData.way" />
            </el-descriptions-item>
            <el-descriptions-item label="售后类型">
              <dict-tag :type="DICT_TYPE.TRADE_AFTER_SALE_TYPE" :value="detailData.type" />
            </el-descriptions-item>

            <el-descriptions-item label="联系人">{{ detailData.contact }}</el-descriptions-item>
            <el-descriptions-item label="电话">{{ detailData.phone }}</el-descriptions-item>
            <el-descriptions-item label="退款商品数量"><el-tag>{{ detailData.count }}</el-tag></el-descriptions-item>
            <el-descriptions-item label="退款金额">{{ formatMoney(detailData.refundPrice) }}</el-descriptions-item>
            <el-descriptions-item label="售后原因">
              <dict-tag :type="DICT_TYPE.TRADE_AFTER_SALE_REASON" :value="detailData.applyReason" />
            </el-descriptions-item>
            <el-descriptions-item label="原因描述">{{ detailData.applyDescription }}</el-descriptions-item>
            <el-descriptions-item label="相关图片" v-if="detailData.applyPicUrls">
              <el-image v-for="(picUrl, index) in detailData.applyPicUrls" :key="index" 
                style="height: 100px" :src="picUrl" 
                fit="fit"
                :preview-src-list="detailData.applyPicUrls">
              </el-image>
            </el-descriptions-item>

            <template v-if="detailData.logisticsName">
              <el-descriptions-item label="物流公司">{{ detailData.logisticsName }}</el-descriptions-item>
              <el-descriptions-item label="物流单号">{{ detailData.logisticsNo }}</el-descriptions-item>
              <el-descriptions-item label="退货时间">{{ parseTime(detailData.deliveryTime) || '--' }}</el-descriptions-item>
            </template>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="运营受理信息"  >
          <el-descriptions title="" :column="1" border :labelStyle="{width: '100px'}">
            <el-descriptions-item label="审批人">{{ detailData.auditUserName || '--' }}</el-descriptions-item>
            <el-descriptions-item label="审批时间">{{ parseTime(detailData.auditTime) || '--' }}</el-descriptions-item>
            <el-descriptions-item label="审批备注">{{ detailData.auditReason || '--' }}</el-descriptions-item>
            <template v-if="detailData.logisticsName">
              <el-descriptions-item label="收货时间">{{ parseTime(detailData.receiveTime) }}</el-descriptions-item>
              <el-descriptions-item label="收货备注">{{ detailData.receiveReason || '--' }}</el-descriptions-item>
            </template>
            <el-descriptions-item label="退款单ID">{{ detailData.payRefundId || '--' }}</el-descriptions-item>
            <el-descriptions-item label="退款时间">{{ parseTime(detailData.refundTime) || '--' }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="售后单操作日志"  >
          <el-table
            :data="logList"
            style="width: 100%">
            <el-table-column prop="userName" label="用户名" width="120"></el-table-column>
            <el-table-column prop="userType" label="用户类型" width="100">
              <template v-slot="scope">
                <dict-tag :type="DICT_TYPE.USER_TYPE" :value="scope.row.userType" />
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="操作时间" width="170">
              <template v-slot="scope">
                <span>{{ parseTime(scope.row.createTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="beforeStatus" label="操作前状态" width="120">
              <template v-slot="scope">
                <dict-tag v-if="scope.row.beforeStatus" :type="DICT_TYPE.TRADE_AFTER_SALE_STATUS" :value="scope.row.beforeStatus" />
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column prop="afterStatus" label="操作后状态" width="120">
              <template v-slot="scope">
                <dict-tag :type="DICT_TYPE.TRADE_AFTER_SALE_STATUS" :value="scope.row.afterStatus" />
              </template>
            </el-table-column>
            <el-table-column prop="content" label="操作备注"></el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <!--售后驳回弹框 -->
    <el-dialog :title="refuseTitile" :visible.sync="refuseOpen" width="500px" v-dialogDrag append-to-body>
      <el-form ref="form" label-position="top" :model="refuseForm" :rules="rules" label-width="120px">
        <el-form-item label="原因描述" prop="memo">
          <el-input type="textarea" :maxlength="200" :autosize="{ minRows: 4, maxRows: 6}" 
          v-model="refuseForm.memo" placeholder="请输入原因描述" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
      
    </template>
  </div>
</template>

<script>
import * as api from "@/api/mall/trade/afterSale";
export default {
  name: "AfterSaleDetail",
  data() {
    return {
      loading: false,
      detailData: null,
      logList: [],
      submitLoading: false,
      refuseTitile: '',
      refuseOpen: false,
      refuseForm: {},
      rules: {
        memo: [
          { required: true, message: '请输入原因描述', trigger: 'blue'}
        ]
      }
    }
  },
  computed: {
    orderItemId() {
      return this.$route.query.orderItemId
    },
  },
  methods: {
    init(id) {
      this.loading = true
      let params = {
        id: id,
        orderItemId: this.orderItemId
      }
      api.getAfterSale(params).then(res => {
        this.detailData = res.data
        this.loadLogList()
      }).finally(() => {
        this.loading = false
      })
    },
    cancel() {
      this.refuseOpen = false
    },
    async loadLogList() {
      let params = {
        afterSaleId: this.detailData.id
      }
      let res = await api.getAfterSaleLogList(params)
      if(res.code === 0) {
        this.logList = res.data || []
      }
    },
    reload() {
      this.init(this.detailData.id)
      this.loadLogList()
      this.$emit('on-update')
    },
    approveSuccess() {
      let title = ''
      let func = null
      if(this.detailData.status === 10) {
        title = '您确认通过售后申请吗？'
        func = api.agreeApply
      } else if([20,30].includes(this.detailData.status)) {
        title = '您确认收货吗'
        func = api.receiveDelivery
      } else {
        console.log('售后单状态校验失败，无法通过:', this.detailData.status)
        return
      }
      const id = this.detailData.id;
      this.$modal.confirm(title).then(function () {
        return func(id);
      }).then(() => {
        this.reload()
        this.$modal.msgSuccess("操作成功");
      }).catch(() => {
      });
    },
    showRefuseForm() {
      if(this.detailData.status === 10) {
        this.refuseTitile = '驳回售后申请'
      } else if(this.detailData.status === 30) {
        this.refuseTitile = '拒绝收货'
      } 
      this.refuseOpen = true
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        
        let func = null
        let params = {
          id: this.detailData.id
        }
        if(this.detailData.status === 10) {
          func = api.disagreeApply
          params.auditReason = this.refuseForm.memo
        } else if([20,30].includes(this.detailData.status)) {
          func = api.refuseDelivery
          params.refuseMemo = this.refuseForm.memo
        } else {
          console.log('售后单状态校验失败，无法驳回:', this.detailData.status)
          return
        }
        func(params).then(res => {
          this.$modal.msgSuccess("驳回成功");
          this.reload()
          this.refuseOpen = false
        }).finally(() => {
          this.submitLoading = false
        })
      })
    },
    toOrderDetail() {
      this.$emit('on-close')
      this.$router.push({ name: 'TradeOrderDetail', query: { id: this.detailData.orderId } })
    }
  }

}
</script>

<style>

</style>