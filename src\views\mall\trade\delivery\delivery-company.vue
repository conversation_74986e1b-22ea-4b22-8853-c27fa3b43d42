<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="公司编码" prop="com">
        <el-input v-model="queryParams.com" placeholder="请输入公司编码" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="公司名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入公司名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['trade:deliverycompany:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['trade:deliverycompany:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="公司名称" align="center" prop="name" />
      <el-table-column label="公司编码" align="center" prop="com" />
      <el-table-column label="创建时间" align="center" prop="createTime">
        <template slot-scope="{ row }">
          <div>{{ parseTime(row.createTime) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['trade:deliverycompany:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="500px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" label-width="1px">
        <el-form-item label="">
          <el-autocomplete
            style="width: 400px;"
            v-model="form.name"
            :fetch-suggestions="querySearch"
            placeholder="请输入快递公司编码或名称"
            :trigger-on-focus="false"
            @select="handleSelect"
          ></el-autocomplete>
        </el-form-item>
      </el-form>
      <div>
        <el-tag style="margin-right: 8px;margin-bottom: 8px;" v-for="item in multipleSelection" :key="item.id" closable @close="handleClose(item)">{{ item.value }}</el-tag>
      </div>
      <el-table v-loading="loading" :data="companyTable" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column label="公司名称" align="center" prop="value" />
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as api from "@/api/mall/trade/deliveryCompany";
export default {
  name: "DeliveryCompany",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 快递公司编码列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        com: null,
        name: null,
        type: null,
        createTime: [],
      },
      // 表单参数
      form: {},
      companyTable: [],
      multipleSelection: []
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      api.getDeliveryCompanyPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        name: undefined,
      };
      this.companyTable = []
      this.multipleSelection = []
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增快递公司";
    },
    /** 提交按钮 */
    submitForm() {
      if (!this.multipleSelection || this.multipleSelection.length === 0) {
        this.$message.info('请选择物流公司！')
        return
      }
      const ids = this.multipleSelection.map(item => item.id).join(",");
      api.batchAdd(ids).then(response => {
        this.$modal.msgSuccess("添加成功");
        this.open = false;
        this.getList();
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      const name = row.name
      this.$modal.confirm('是否确认删除快递公司名称为"' + name + '"的数据项?').then(function() {
        return api.deleteDeliveryCompany(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有快递公司编码数据项?').then(() => {
        this.exportLoading = true;
        return api.exportDeliveryCompanyExcel(params);
      }).then(response => {
        this.$download.excel(response, '快递公司编码.xls');
        this.exportLoading = false;
      }).catch(() => {});
    },
    async querySearch(queryString, cb) {
      const res = await api.getConfigCompanyPage({
        pageSize: 10,
        pageNo: 1,
        type: '国内运输商',
        name: queryString
      })
      if (res.code == 0) {
        const arr = (res.data.list || []).map(x => {
          return {
            value: x.name,
            id: String(x.id)
          }
        })
        this.companyTable = arr
        cb(arr);
      } else {
        this.companyTable = []
        cb([])
      }
    },
    handleSelect(val) {
      this.form.name = ''
    },
    handleSelectionChange(val) {
      if (this.multipleSelection.length === 0) {
        this.multipleSelection = val;
      } else {
        val.forEach(item => {
          if (this.multipleSelection.findIndex(x => x.id === item.id) === -1) {
            this.multipleSelection.push(item);
          }
        })
      }
    },
    handleClose(tag) {
      this.multipleSelection.splice(this.multipleSelection.indexOf(tag), 1);
    }
  }
};
</script>
