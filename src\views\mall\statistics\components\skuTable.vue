<template>
  <div id="suppliersEchart" class="suppliers-echart"></div>
</template>

<script>
import * as api from "@/api/mall/statistics/commodity";
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  data() {
    return {
      chart: null
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      // 执行查询
      api.getSupplierSkuTotal({}).then(response => {
        if (response.code === 0) {
          this.initChart(response.data);
        }
      });
    },
    initChart(data) {
      const chartDom = document.getElementById('suppliers-echart');
      this.chart = echarts.init(chartDom, 'macarons')
      this.chart.setOption({
        legend: {},
        tooltip: {},
        grid: {
          left: '1%',
          right: '1%',
          bottom: '3%',
          containLabel: true
        },
        dataset: {
          dimensions: ['product', 'sku总数', '上架总数', '下架总数'],
          source: data.map(item => {
            return {
              product: item.supplierName,
              'sku总数': item.skuSummaryRespVO.total,
              '上架总数': item.skuSummaryRespVO.listingTotal,
              '下架总数': item.skuSummaryRespVO.delistingTotal,
              warn: item.warn,
              supplierId: item.supplierId
            }
          })
        },
        xAxis: {
          type: 'category',
          axisLabel: {
            interval: 0,
            rotate: 40
          }
        },
        yAxis: {},
        series: [
          { type: 'bar' },
          {
            type: 'bar',
            itemStyle: {
              color: (params) => {
                if (params.data.warn) {
                  return 'red'
                }
                return '#b6a2de'
              }
            }
          },
          { type: 'bar' }]
      })
      this.chart.on('click', (params) => {
        if (params.value.supplierId) {
          this.$router.push({
            path: '/statistics/supplierEchart',
            query: {
              supplierId: params.value.supplierId
            }
          })
        }
      })
    },
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  }
};
</script>

<style lang="scss" scoped>
.suppliers-echart {
  width: 100%;
  height: 500px;
}
</style>
