import request from '@/utils/request'


// 查询配置
export function getAssetsConfigInfo(id) {
  return request({
    url: '/mall/assets-config/get?id=' + id,
    method: 'get'
  })
}

// 查询配置列表
export function getAssetsConfigList() {
  return request({
    url: '/mall/assets-config/page',
    method: 'get'
  })
}

// 查询回调地址
export function getCallbackUrl() {
  return request({
    url: '/mall/assets-config/get-callback-url',
    method: 'get'
  })
}

// 更新配置
export function updateAssetsConfig(data) {
  return request({
    url: '/mall/assets-config/update',
    method: 'put',
    data: data
  })
}

// 创建配置
export function createAssetsConfig(data) {
  return request({
    url: '/mall/assets-config/create',
    method: 'post',
    data: data
  })
}

// 清除配置
export function deleteAssetsConfig(id) {
  return request({
    url: '/mall/assets-config/delete?id=' + id,
    method: 'delete'
  })
}

// 查询配置规则
export function getAssetsRule(id) {
  return request({
    url: '/mall/assets-config/rule/get?id=' + id,
    method: 'get'
  })
}

// 查询配置规则列表
export function getAssetsRuleList(params) {
  return request({
    url: '/mall/assets-config/rule/list-all',
    params: params,
    method: 'get'
  })
}

// 更新配置规则
export function updateAssetsRule(data) {
  return request({
    url: '/mall/assets-config/rule/update',
    method: 'put',
    data: data
  })
}

// 创建配置规则
export function createAssetsRule(data) {
  return request({
    url: '/mall/assets-config/rule/create',
    method: 'post',
    data: data
  })
}

// 清除配置规则
export function deleteAssetsRule(id) {
  return request({
    url: '/mall/assets-config/rule/delete?id=' + id,
    method: 'delete'
  })
}

// 查询配置规则分类明细
export function getAssetsRuleCategory(id) {
  return request({
    url: '/mall/assets-config/rule-category/get?id=' + id,
    method: 'get'
  })
}

// 查询配置规则分类明细列表
export function getAssetsRuleCategoryList(params) {
  return request({
    url: '/mall/assets-config/rule-category/page',
    params: params,
    method: 'get'
  })
}

// 更新配置规则分类明细
export function updateAssetsRuleCategory(data) {
  return request({
    url: '/mall/assets-config/rule-category/update',
    method: 'put',
    data: data
  })
}

// 创建配置规则分类明细
export function createAssetsRuleCategory(data) {
  return request({
    url: '/mall/assets-config/rule-category/create',
    method: 'post',
    data: data
  })
}

// 创建配置规则分类明细
export function createAssetsRuleCategoryBatch(data) {
  return request({
    url: '/mall/assets-config/rule-category/create-batch',
    method: 'post',
    data: data
  })
}

// 清除配置规则分类明细
export function deleteAssetsRuleCategory(id) {
  return request({
    url: '/mall/assets-config/rule-category/delete?id=' + id,
    method: 'delete'
  })
}

// 清除配置规则分类明细
export function deleteAllByRule(ruleId) {
  return request({
    url: '/mall/assets-config/rule-category/delete-by-rule?ruleId=' + ruleId,
    method: 'delete'
  })
}

// 更新配置规则分类明细
export function updateAllPrice(data) {
  return request({
    url: '/mall/assets-config/rule-category/update-price',
    method: 'put',
    data: data
  })
}

// 查询固资入口
export function getAssetsEntry(id) {
  return request({
    url: '/mall/assets-config/entry/get?id=' + id,
    method: 'get'
  })
}

// 查询固资入口列表
export function getAssetsEntryList(params) {
  return request({
    url: '/mall/assets-config/entry/page',
    params: params,
    method: 'get'
  })
}

// 更新固资入口
export function updateAssetsEntry(data) {
  return request({
    url: '/mall/assets-config/entry/update',
    method: 'put',
    data: data
  })
}

// 创建固资入口
export function createAssetsEntry(data) {
  return request({
    url: '/mall/assets-config/entry/create',
    method: 'post',
    data: data
  })
}


// 删除入口
export function deleteAssetsEntry(id) {
  return request({
    url: '/mall/assets-config/entry/delete?id=' + id,
    method: 'delete'
  })
}