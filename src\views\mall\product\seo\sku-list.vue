<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="商品名称" prop="skuName">
        <el-input v-model="queryParams.skuName" placeholder="请输入商品名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="平台SKU" prop="id">
        <el-input v-model="queryParams.id" oninput="value=value.replace(/^(0+)|[^\d]+/g,'')" placeholder="请输入商品SKU" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="三方SKU" prop="skuInnerId">
        <el-input v-model="queryParams.skuInnerId" placeholder="请输入三方SKU" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="供应商" prop="supplierId">
        <supplier-select size="small" v-model="queryParams.supplierId" placeholder="请选择供应商" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="商品分类" prop="categoryIds">
        <category-select v-model="queryParams.categoryIds" :extProps="{checkStrictly: true}" placeholder="请选择商品分类" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="商品品牌" prop="brandId">
        <brand-select size="small" v-model="queryParams.brandId" placeholder="请选择商品品牌" @keyup.enter.native="handleQuery" />
      </el-form-item> -->
      <el-form-item label="列出状态" prop="showStatus">
        <el-select v-model="queryParams.showStatus" clearable placeholder="请选择" @keyup.enter.native="handleQuery">
          <el-option :value="1" label="打开"></el-option>
          <el-option :value="0" label="关闭"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="SKU状态" prop="status">
        <el-select v-model="queryParams.status" clearable placeholder="请选择" @keyup.enter.native="handleQuery">
          <el-option :value="0" label="下架"></el-option>
          <el-option :value="1" label="上架"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="商品销量" class="form-item-range">
        <el-col :span="11" style="padding-left:0">
          <el-form-item prop="salesCountMin">
            <el-input v-model="queryParams.salesCountMin" placeholder="最低销量" clearable oninput="value=value.replace(/^(0+)|[^\d]+/g,'')" @keyup.enter.native="handleQuery"/>
          </el-form-item>
        </el-col>
        <el-col :span="2" style="text-align: center;">-</el-col>
        <el-col :span="11" style="padding-left:5px">
          <el-form-item prop="salesCountMax">
            <el-input v-model="queryParams.salesCountMax" placeholder="最高销量" clearable oninput="value=value.replace(/^(0+)|[^\d]+/g,'')" @keyup.enter.native="handleQuery"/>
          </el-form-item>
        </el-col>
      </el-form-item>
      <el-form-item label="商品价格" prop="salePrice" class="form-item-range">
        <el-col :span="11" style="padding-left:0">
          <el-form-item prop="salePriceMin">
            <el-input v-model="queryParams.salePriceMin" placeholder="最低价格" clearable oninput="value= value.match(/\d+(\.\d{0,2})?/) ? value.match(/\d+(\.\d{0,2})?/)[0] : ''" @keyup.enter.native="handleQuery"/>
          </el-form-item>
        </el-col>
        <el-col :span="2" style="text-align: center;">-</el-col>
        <el-col :span="11" style="padding-left:5px">
          <el-form-item prop="salePriceMax">
            <el-input v-model="queryParams.salePriceMax" placeholder="最高价格" clearable oninput="value= value.match(/\d+(\.\d{0,2})?/) ? value.match(/\d+(\.\d{0,2})?/)[0] : ''" @keyup.enter.native="handleQuery"/>
          </el-form-item>
        </el-col>
      </el-form-item>
      <el-form-item label="商品标签" prop="tags">
        <tag-select size="small" v-model="queryParams.tags" placeholder="请选择标签" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="标签状态" prop="tagStatus">
        <el-select v-model="queryParams.tagStatus" clearable placeholder="请选择" @keyup.enter.native="handleQuery">
          <el-option :value="1" label="有标签"></el-option>
          <el-option :value="0" label="无标签"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <!-- <el-button v-hasPermi="['product:sku:seo:import']" type="primary" plain icon="el-icon-download" size="mini" @click="handleImport">导入商品</el-button> -->
        <el-button v-hasPermi="['product:sku:tag:import']" type="primary" plain icon="el-icon-collection-tag" size="mini" @click="handleImport2()">标签导入</el-button>
        <el-button v-hasPermi="['product:sku:seo:update']" type="primary" plain icon="el-icon-plus" size="mini" @click="addProductSku">添加商品</el-button>
        <el-button v-hasPermi="['product:sku:seo:update']" type="primary" plain icon="el-icon-edit" size="mini" @click="updateSeoInfo()">更新SEO字段</el-button>
        <el-button v-hasPermi="['product:sku:seo:update']" type="primary" plain icon="el-icon-edit" size="mini" @click="multipleChangeStatus">更新列出状态</el-button>

        <el-button v-hasPermi="['product:sku:add-tag']" type="primary" plain icon="el-icon-collection-tag" size="mini" @click="editTagBatch()">编辑标签</el-button>
        <el-button v-hasPermi="['product:sku:clear-tag']" type="primary" plain icon="el-icon-collection-tag" size="mini" @click="clearTagBatch()">清除标签</el-button>
        <el-button v-hasPermi="['product:sku:add-tag']" type="primary" plain icon="el-icon-collection-tag" size="mini" @click="rewriteOrderTags()">重写订单标签</el-button>

        <el-button v-hasPermi="['product:sku:seo:export']"  type="primary" plain icon="el-icon-files" size="mini" @click="exportSku">商品导出</el-button>
        <el-button v-hasPermi="['product:sku:seo:reset']" type="primary" plain icon="el-icon-delete" size="mini" @click="cancelSeo()">移除SEO</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"/>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list" 
    @selection-change="handleSelectionChange" 
    @sort-change="handleSortChange"
    :default-sort = "{prop: 'createTime', order: 'descending'}">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column label="商品信息" align="center" width="280">
        <template v-slot="scope">
          <div class="product-info">
            <img v-if="scope.row.picUrl" :src="scope.row.picUrl" alt="分类图片" class="img-height" />
            <div :title="scope.row.skuName" class="message">【{{ scope.row.supplierName }}】{{ scope.row.skuName }}</div>
          </div>
        </template>
        <!-- TODO 前端优化：可以有个 + 号，点击后，展示每个 sku -->
      </el-table-column>
      <el-table-column label="平台/三方SKU" align="center" prop="id" width="190">
        <template v-slot="scope">
          <el-link @click="handleView(scope.row)" type="primary"><span>{{ scope.row.id }}</span></el-link><br>
          <span v-if="scope.row.skuInnerId">{{ scope.row.skuInnerId }}</span>
        </template>
      </el-table-column>

      <el-table-column label="销售价格" align="center" prop="salePrice" sortable="custom">
        <template v-slot="scope">
          <span>{{ formatMoney(scope.row.salePrice) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="初始销量" align="center" prop="initSalesCount" sortable="custom">
        <template v-slot="scope">
          <span>{{ scope.row.initSalesCount }}</span>
          <span v-hasPermi="['product:sku:seo:update']" style="margin-left: 5px;" @click="updateSeoInfo(scope.row)"><i class="el-icon-edit" style="cursor: pointer;"></i></span>
        </template>
      </el-table-column>
      <el-table-column label="销量" align="center" prop="salesCount" sortable="custom"/>
      <el-table-column label="总销量" align="center" prop="totalSalesCount" sortable="custom" />
      <!-- <el-table-column label="供应商" align="center" prop="supplierName"/> -->
      <!-- <el-table-column label="品牌" align="center" prop="brandName"/> -->
      <el-table-column label="商品分类" align="center" prop="fullCategoryName"/>
      <!-- <el-table-column label="排序" align="center" prop="sort"/> -->
      <el-table-column label="标签" align="center" prop="tagList">
        <template v-slot="scope">
          <div v-if="scope.row.tagList && scope.row.tagList.length" class="row-tag-container">
            <el-tag v-for="tag in scope.row.tagList" :key="tag.id" type="primary" size="small">{{ tag.name }}</el-tag>
          </div>
          <div v-else>--</div>
        </template>
      </el-table-column>
      <el-table-column v-if="checkPermi(['product:spu:update-show-status'])" label="列出状态" align="center" prop="showStatus">
        <template v-slot="scope">
          <el-switch v-model="scope.row.showStatus" :active-value="1" :inactive-value="0" @change="handleShowStatus(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column v-if="checkPermi(['product:spu:update-platform-status'])" label="平台状态" align="center" prop="platformStatus">
        <template v-slot="scope">
          <el-switch v-model="scope.row.platformStatus" :active-value="1" :inactive-value="0" @change="handlePlatformStatus(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="100" sortable="custom">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="SKU状态" align="center" prop="status">
        <template v-slot="{row}">
          <dict-tag :type="DICT_TYPE.PRODUCT_SPU_STATUS" :value="row.status"/>
          <el-popover
            style="margin-left:5px;"
            v-if="row.statusError"
            title="不可售"
            width="200"
            trigger="click"
            :content="row.indexStatusDetail.reason">
            <i slot="reference" class="el-icon-warning"></i>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <!-- 自建供应商的商品才有上下架 -->
          <el-button size="mini" type="text" :icon="scope.row.status === 1 ? 'el-icon-bottom' : 'el-icon-top'" @click="handleUpdateStatus(scope.row)"
                      v-if="checkPermi(['product:spu:update'])">{{ scope.row.status === 1 ? "下架" : "上架"}}</el-button>
          <el-button size="mini" type="text" icon="el-icon-view" @click="handlePreview(scope.row)"
                      v-hasPermi="['product:spu:query']">预览</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="cancelSeo(scope.row)"
                      v-hasPermi="['product:sku:seo:reset']">移除</el-button>           
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <SkuSeoForm ref="skuSeoForm" @update-success="getList"/>
    <Preview ref="spuPreview" :open.sync="previewOpen" :spuId="curSpuId" :skuId="curSkuId"/>

    <!-- 运营商品导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="550px" append-to-body>
      <div class="flex-vertical-center">
        <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
          :action="upload.url" :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip text-center" slot="tip">
            <span>仅允许导入xls、xlsx格式文件，建议单次导入不超过5000条</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
          </div>
        </el-upload>
        <ImportAlert v-if="upload.open" ref="importAlert" @on-complete="getList"></ImportAlert>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="importLoading" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 标签导入导入对话框 -->
    <el-dialog :title="upload2.title" :visible.sync="upload2.open" width="550px" append-to-body>
      <div class="flex-vertical-center">
        <el-upload ref="upload2" :limit="1" accept=".xlsx, .xls" :headers="upload2.headers"
          :action="upload2.url" :disabled="upload2.isUploading"
          :on-progress="handleFileUploadProgress2" :on-success="handleFileSuccess2" :auto-upload="false" drag>
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip text-center" slot="tip">
            <span>仅允许导入xls、xlsx格式文件，建议单次导入不超过5000条</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate2">下载模板</el-link>
          </div>
        </el-upload>
        <ImportAlert v-if="upload2.open" ref="importAlert2" @on-complete="getList"></ImportAlert>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="importLoading2" @click="submitFileForm2">确 定</el-button>
        <el-button @click="upload2.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <ProductSkuSelect ref="productSkuSelect" @on-confirmed="handleAddCallback"></ProductSkuSelect>
    <TagDialog ref="tagDialog" @on-update="saveTags"></TagDialog>

    <ExportAlert ref="exportAlert"></ExportAlert>
  </div>
</template>

<script>
import { getSeoSkuPage, getSkuDetail2, updateSkuStatus, updateShowStatus, updatePlatformStatus, 
  getImportTemplate, getTagImportTemplate, resetSkuSeoStatus, addSkuSeoStatus, querySkuIndexStatus,
  addSkuTags, clearSkuTags, refreshOrderTags, exportSeoSkuList } from "@/api/mall/product/sku";
import SupplierSelect from "@/views/mall/product/spu/components/supplier-select"
import BrandSelect from "@/views/mall/product/spu/components/brand-select"
import CategorySelect from "@/views/mall/product/spu/components/category-select"
import SkuSeoForm from "@/views/mall/product/seo/components/seo-form"
import Preview from "@/views/mall/product/spu/preview"
import ProductSkuSelect from "@/views/mall/product/spu/components/sku-select"
import TagSelect from "@/views/mall/product/seo/components/tag-select"
import TagDialog from '@/components/TagDialog'
import {getBaseHeader} from "@/utils/request"
import ImportAlert from '@/components/AsyncTaskAlert/import'
import ExportAlert from '@/components/AsyncTaskAlert/export'
export default {
  name: "ProductSeoSkuList",
  components: { SupplierSelect, BrandSelect, CategorySelect, SkuSeoForm, Preview, ProductSkuSelect, TagSelect, TagDialog, ImportAlert, ExportAlert },
  data() {
    return {
      // 遮罩层
      loading: true,
      importLoading: false,
      importLoading2: false,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商品spu列表
      list: [],
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        skuInnerId: null,
        skuName: null,
        id: null,
        categoryIds: [],
        supplierId: null,
        brandId: null,
        seoStatus: 1,
        showStatus: null,
        status: null,
        salesCountMin: null,
        salesCountMax: null,
        salePriceMin: null,
        salePriceMax: null,
        tags: [],
        tagStatus: null
      },
      skuFormOpen: false,
      curSpuId: null,
      curSkuId: null,
      previewOpen: false,
      syncFormOpen: false,
      multipleSelection: [],
      // 导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/product/sku/seo/import'
      },
      // 标签导入参数
      upload2: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/product/sku/tag/import'
      }
    };
  },
  watch: {
    "queryParams": {
      deep: true,
      handler(newVal, oldVal) {
        const ck = 'seo-sku-list-params'
        sessionStorage.setItem(ck, JSON.stringify(newVal))
      }
    }
  },
  created() {
    this.initParams()
    this.handleQuery()
  },
  methods: {
    initParams() {
      const ck = 'seo-sku-list-params'
      const val = sessionStorage.getItem(ck)
      if(val) {
        Object.assign(this.queryParams, JSON.parse(val))
        this.queryParams.categoryIds = []
        this.queryParams.fullCategoryId = null
      }
    },
    getParams() {
      let params = {...this.queryParams};
      if(!params.skuInnerId) {
        delete params.skuInnerId
      }
      params.salePriceMin = this.queryParams.salePriceMin === null ? null : params.salePriceMin;
      params.salePriceMax = this.queryParams.salePriceMax === null ? null : params.salePriceMax;
      if(params.categoryIds && params.categoryIds.length) {
        params.fullCategoryId = params.categoryIds.join('-')
      }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, "createTime");
      return params
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      const params = this.getParams();
      // 执行查询
      getSeoSkuPage(params).then(async (response) => {
        let list = ((response.data && response.data.list) || []).map(item => {
          if (item.showStatus === undefined) {
            item.showStatus = 1
          }
          return item
        });
        await this.getSkuIndexStatus(list) 
        this.list = list
        this.total = parseInt(response.data.total)
      }).finally(() => {
        this.loading = false
      })
    },
    async getSkuIndexStatus(list) {
      if(!list || !list.length) {
        return
      }
      let params = {
        skuIds: list.map(item => item.id)
      }
      let res = await querySkuIndexStatus(params)
      if(res.data && res.data.length) {
        list.forEach((item) => {
          item.indexStatusDetail = res.data.find(indexItem => indexItem.skuId === item.id)
          item.statusError = item.indexStatusDetail && !item.indexStatusDetail.needIndex && (item.status && item.platformStatus && item.showStatus)
        })
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push({ name: 'ProductSpuForm'})
    },
    /** 修改状态按钮操作 */
    async handleUpdateStatus(row) {
      let params = {
        id: row.id,
        status: row.status === 0 ? 1 : 0
      }  
      let res = await updateSkuStatus(params)
      if(res.code === 0) {
        row.status = params.status
        this.getList()
        this.$modal.msgSuccess("操作成功")
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      if(row.supplierType === 1) {
        this.$modal.msgWarning("京东商品不支持修改");
        return
      }
      this.$router.push({ name: 'ProductSpuForm', query: { spuId: row.spuId }})
    },
    handleView(row) {
      this.$router.push({ name: 'SkuDetail', query: { spuId: row.spuId } })
    },
    handlePreview(row) {
      this.curSpuId = row.spuId
      this.curSkuId = row.id
      this.previewOpen = true
    },
    async handleShowStatus(row) {
      let params = [{
        id: row.id,
        showStatus: row.showStatus
      }]
      let res = await updateShowStatus(params)
      if(res.code === 0) {
        this.getList()
        this.$modal.msgSuccess("操作成功")
      }
    },
    async handlePlatformStatus(row) {
      let params = [{
        id: row.id,
        status: row.platformStatus
      }]
      let res = await updatePlatformStatus(params)
      if(res.code === 0) {
        this.getList()
        this.$modal.msgSuccess("操作成功")
      }
    },
    async multipleChangeStatus() {
      if (!this.multipleSelection.length) {
        this.$message.warning("请至少选择一个商品")
        return;
      }
      let params = this.multipleSelection.map(item => {
        return {
          id: item.id,
          showStatus: item.showStatus === 1 ? 0 : 1
        }
      })
      let res = await updateShowStatus(params)
      if(res.code === 0) {
        this.getList()
        this.$modal.msgSuccess("操作成功")
      }
    },
    updateSeoInfo(row) {
      let arr = []
      if(row) {
        arr.push(row)
      } else if (this.multipleSelection.length) {
        arr = this.multipleSelection
      } else {
        this.$message.warning("请至少选择一个商品")
        return;
      }
      this.$refs.skuSeoForm.init(arr)
    },
    editTagBatch() {
      let arr = []
      if (this.multipleSelection.length) {
        arr = this.multipleSelection
      } else {
        this.$message.warning("请至少选择一个商品")
        return;
      }
      let tagIds = []
      arr.forEach(row => {
        if(row.tagList && row.tagList.length) {
          let ids = row.tagList.map(t => t.id)
          tagIds = tagIds.concat(ids)
        }
      })
      this.$refs.tagDialog.show(tagIds)
    },
    clearTagBatch() {
      let arr = []
      if (this.multipleSelection.length) {
        arr = this.multipleSelection
      } else {
        this.$message.warning("请至少选择一个商品")
        return;
      }
      let skuIds = arr.map(item => item.id)
      clearSkuTags({skuIdList: skuIds}).then(res => {
        this.$modal.msgSuccess("操作成功");
        this.getList();
      })
    },
    rewriteOrderTags() {
      let arr = []
      if (this.multipleSelection.length) {
        arr = this.multipleSelection
      } else {
        this.$message.warning("请至少选择一个商品")
        return;
      }
      let skuIds = arr.map(item => item.id)
      this.$modal.confirm('此操作会将商品标签写入所有历史订单，确认执行此操作吗?').then(function() {
        return refreshOrderTags({ids: skuIds})
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("处理成功")
      }).catch(() => {
      })
    },
    saveTags(tags) {
      let arr = this.multipleSelection
      let skuIdList = arr.map(item => item.id)
      let tagIdList = tags.map(item => item.id)
      let params = {
        tagIdList: tagIdList,
        skuIdList: skuIdList
      }
      addSkuTags(params).then(res => {
        if(res.code === 0) {
          this.$modal.msgSuccess("操作成功");
          this.getList();
        } 
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleSortChange({ prop, order }) {
      this.queryParams.sortType = 10
      if(prop === 'createTime' && order === 'descending') {
        this.queryParams.sortType = 10
      } else if(prop === 'createTime' && order === 'ascending') {
        this.queryParams.sortType = 11
      } else if(prop === 'salesCount' && order === 'descending') {
        this.queryParams.sortType = 20
      } else if(prop === 'salesCount' && order === 'ascending') {
        this.queryParams.sortType = 21
      }  else if(prop === 'initSalesCount' && order === 'descending') {
        this.queryParams.sortType = 30
      } else if(prop === 'initSalesCount' && order === 'ascending') {
        this.queryParams.sortType = 31
      } else if(prop === 'salePrice' && order === 'descending') {
        this.queryParams.sortType = 40
      } else if(prop === 'salePrice' && order === 'ascending') {
        this.queryParams.sortType = 41
      } else if(prop === 'totalSalesCount' && order === 'descending') {
        this.queryParams.sortType = 50
      } else if(prop === 'totalSalesCount' && order === 'ascending') {
        this.queryParams.sortType = 51
      } 
      console.log(prop, order)
      this.handleQuery()
    },
    addProductSku() {
      this.$refs.productSkuSelect.init({seoStatus: 0})
    },
    handleAddCallback(rows) {
      console.log('rows----', rows)
      if(!rows || !rows.length) {
        return;
      }
      let params = {
        ids: rows.map(item => item.id)
      }
      addSkuSeoStatus(params).then(res => {
        this.$message.success("操作成功")
        this.getList()
      })
    },
    cancelSeo(row) {
      let params = {
        ids: []
      }
      if(row) {
        params.ids.push(row.id)
      } else if (this.multipleSelection.length) {
        params.ids = this.multipleSelection.map(item => item.id)
      } else {
        this.$message.warning("请至少选择一个商品")
        return;
      }
      resetSkuSeoStatus(params).then(res => {
        this.$message.success("操作成功")
        this.getList()
      })
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "运营商品导入"
      this.importLoading = false
      this.upload.open = true
      this.$nextTick(() => {
        this.$refs.upload.clearFiles()
      })
    },
    /** 下载模板操作 */
    importTemplate() {
      getImportTemplate().then(response => {
        this.$download.excel(response, '运营商品导入模板.xls');
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.importLoading = false
      if (response.code !== 0) {
        this.upload.isUploading = false
        this.$refs.upload.clearFiles()
        this.$modal.msgError(response.msg)
        return;
      }
      // this.upload.open = false;
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      this.$refs.importAlert.init2(response.data)
    },
    // 提交上传文件
    submitFileForm() {
      let files = this.$refs.upload.uploadFiles
      if(!files.length) {
        this.$modal.msg("请上传导入文件")
        return
      }

      this.importLoading = true
      this.$refs.upload.submit()
    },

    /** 导入按钮操作 */
    handleImport2() {
      this.upload2.title = "商品标签导入"
      this.importLoading2 = false
      this.upload2.open = true
      this.$nextTick(() => {
        this.$refs.upload2.clearFiles()
      })
    },
    /** 下载模板操作 */
    importTemplate2() {
      getTagImportTemplate().then(response => {
        this.$download.excel(response, '商品标签导入模板.xls');
      });
    },
    // 文件上传中处理
    handleFileUploadProgress2(event, file, fileList) {
      this.upload2.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess2(response, file, fileList) {
      this.importLoading2 = false
      if (response.code !== 0) {
        this.upload2.isUploading = false
        this.$refs.upload2.clearFiles()
        this.$modal.msgError(response.msg)
        return;
      }
      // this.upload.open = false;
      this.upload2.isUploading = false
      this.$refs.upload2.clearFiles()
      this.$refs.importAlert2.init2(response.data)
    },
    // 提交上传文件
    submitFileForm2() {
      let files = this.$refs.upload2.uploadFiles
      if(!files.length) {
        this.$modal.msg("请上传导入文件")
        return
      }

      this.importLoading2 = true
      this.$refs.upload2.submit()
    },
    // SEO商品导出
    exportSku() {
      const params = this.getParams()
      this.$refs.exportAlert.init(exportSeoSkuList, params, 'SEO商品导出')
    },
  }
};
</script>
<style lang="scss">
.app-container {
  .product-info {
    display: flex;

    .img-height {
      height: 50px;
      width: 50px;
    }

    .message {
      margin-left: 10px;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      word-break: break-all;
      -webkit-box-orient: vertical;
      white-space: normal;
      overflow: hidden;
      height: 50px;
      line-height: 25px;
    }
  }
  .form-item-range {
    width: 510px;
  }
  .row-tag-container {
    .el-tag {
      margin: 5px 0 0 5px;
    }
  }
}
</style>
