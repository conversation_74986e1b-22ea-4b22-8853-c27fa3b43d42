import request from '@/utils/request'

// 创建商品分类
export function createProductCategory(data) {
  return request({
    url: '/product/category/create',
    method: 'post',
    data: data
  })
}

// 更新商品分类
export function updateProductCategory(data) {
  return request({
    url: '/product/category/update',
    method: 'put',
    data: data
  })
}

// 更新商品分类
export function updateProductCategoryStatus(data) {
  return request({
    url: '/product/category/update-status',
    method: 'put',
    data: data
  })
}

// 删除商品分类
export function deleteProductCategory(id) {
  return request({
    url: '/product/category/delete?categoryId=' + id,
    method: 'delete'
  })
}

// 获得商品分类
export function getProductCategory(id) {
  return request({
    url: '/product/category/get?id=' + id,
    method: 'get'
  })
}

// 获得商品分类列表
export function getProductCategoryList(query) {
  return request({
    url: '/product/category/list',
    method: 'get',
    params: query
  })
}

// 获得商品一级分类列表
export function getCategoryRootList(query) {
  return request({
    url: '/product/category/list-root',
    method: 'get',
    params: query
  })
}

// 查询分类总数量
export function getCategoryTotalCount(query) {
  return request({
    url: '/product/category/get-total-count',
    method: 'get',
    params: query
  })
}

// 获得商品子级分类树列表
export function getCategoryChildTreeList(query) {
  return request({
    url: '/product/category/list-children-tree',
    method: 'get',
    params: query
  })
}

// 克隆配置平台商品分类
export function cloneProductCategory(data) {
  return request({
    url: '/product/category/clone',
    method: 'post',
    data: data
  })
}

// 根据上级分类ID获得分类列表
export function getCategoryListByParent(query) {
  return request({
    url: '/product/category/list-by-parent',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function getImportTemplate(query) {
  return request({
    url: '/product/category/get-import-template',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}