<template>
  <div>
    <div style="margin: 15px 0;font-size:1.2em;">
      规则列表
      <el-button style="margin-left: 10px" size="small" type="primary" @click="addConfigRule">添加规则</el-button>
    </div>
    <el-table :data="ruleList" style="width: 85%">
      <el-table-column prop="name" label="规则名称"></el-table-column>
      <el-table-column prop="type" label="规则类型">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.MALL_ASSETS_RULE_TYPE" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column prop="content" label="规则内容" show-overflow-tooltip>
        <template v-slot="scope">
          <span v-if="scope.row.type === 1">{{ scope.row.content }}</span>
          <span v-if="scope.row.type === 2">
            <el-tag :type="scope.row.categoryCount > 0 ? 'primary' : 'warning'">分类数量 {{ scope.row.categoryCount }}</el-tag>
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="editRuleCategory(scope.row)" v-if="scope.row.type === 2">配置分类</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="editConfigRule(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="deleteConfigRule(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :title="ruleTitle" :visible.sync="ruleOpen" width="650px" v-dialogDrag append-to-body>
      <el-form ref="ruleForm" :model="ruleForm" :rules="ruleRules" label-width="120px">
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="ruleForm.name" maxlength="50" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="规则类型" prop="type">
          <el-radio-group v-model="ruleForm.type">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.MALL_ASSETS_RULE_TYPE)"
                      :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="表达式内容" prop="content" :rules="[{required: true, message: '请输入表达式', trigger: 'blur'}]" v-if="ruleForm.type === 1">
          <span>支持变量: categoryCode skuId skuName skuPrice count skuTotalPrice</span>
          <el-input v-model="ruleForm.content" type="textarea" :autosize="{ minRows: 4, maxRows: 6}" maxlength="300" show-word-limit placeholder="请输入表达式" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="ruleForm.status">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                      :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="ruleFormSubmit" :loading="submitLoading2">确 定</el-button>
        <el-button @click="ruleOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <AssetsRuleCategory ref="assetsRuleCategory" @update="handleRuleCategoryUpdate"></AssetsRuleCategory>
  </div>
</template>

<script>
import * as api from "@/api/mall/config/assetsConfig";
import AssetsRuleCategory from "./assets-rule-category";
export default {
  name: "MallAssetsRule",
  props: {
    configId: {
      type: Number,
      required: true
    }
  },
  components: { AssetsRuleCategory },
  props: {
    configId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      submitLoading2: false,
      ruleOpen: false,
      ruleTitle: '',
      ruleList: [],
      ruleForm: {},
      ruleRules: {
        name: [
          { required: true, trigger: 'change', message: '请输入规则名称' }
        ],
        type: [
          { required: true, trigger: 'blur', message: '请选择规则类型' }
        ],
        status: [
          { required: true, trigger: 'blur', message: '请选择状态' }
        ],
      }
    }
  },
  mounted() {
    this.loadConfigRuleList()
  },
  methods: {
    loadConfigRuleList() {
      let params = {
        pageSize: 50,
        configId: this.configId
      }
      api.getAssetsRuleList(params).then(res => {
        this.ruleList = res.data || []
        this.loadRuleCategoryInfo(this.ruleList)
        this.$forceUpdate()
      })
    },
    ruleReset() {
      this.ruleForm = {
        id: undefined,
        configId: undefined,
        name: '',
        type: undefined,
        content: '',
        status: 0
      }
      this.resetForm("configForm");
    },
    addConfigRule() {
      this.ruleReset()
      this.ruleForm.configId = this.configId
      this.ruleTitle = '增加固定资规则'
      this.ruleOpen = true
    },
    editConfigRule(row) {
      this.ruleReset()
      this.ruleTitle = '编辑固资规则'
      Object.assign(this.ruleForm, row)
      this.ruleOpen = true
    },
    editRuleCategory(row) {
      this.$refs.assetsRuleCategory.show(row)
    },
    deleteConfigRule(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除"' + row.name + '"的规则项?').then(function() {
        return api.deleteAssetsRule(id);
      }).then(() => {
        this.loadConfigRuleList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    async doRuleSubmit() {
      this.submitLoading2 = true
      try {
        if (this.ruleForm.id !== undefined) {
          await api.updateAssetsRule(this.ruleForm)
          this.$modal.msgSuccess("修改成功");
        } else {
          await api.createAssetsRule(this.ruleForm)
          this.$modal.msgSuccess("保存成功");
        }
        this.submitLoading2 = false
        this.ruleOpen = false
        this.loadConfigRuleList()
      } catch(e) {
        this.submitLoading2 = false
      }
    },
    ruleFormSubmit() {
      this.$refs["ruleForm"].validate(valid => {
        if (!valid) {
          return;
        }
        this.doRuleSubmit()
      });
    },
    loadRuleCategoryInfo(ruleList) {
      if(!ruleList) {
        return
      }
      let list = ruleList.filter(item => item.type === 2)
      list.forEach(item => {
        this.ruleCategoryUpdate(item)
      })
    },
    async handleRuleCategoryUpdate(ruleInfo) {
      ruleInfo = this.ruleList.find(rule => rule.id ===  ruleInfo.id)
      this.ruleCategoryUpdate(ruleInfo)
    },
    async ruleCategoryUpdate(ruleInfo) {
      let param = {
        pageSize: 1,
        ruleId: ruleInfo.id
      }
      let res = await api.getAssetsRuleCategoryList(param)
      ruleInfo.categoryCount = res.data.total || 0
      // 解决表格中不刷新问题
      ruleInfo.type = 0
      ruleInfo.type = 2
      this.$forceUpdate();
    },
  }
}
</script>
