<template>
  <div class="app-container" v-show="open">
    <el-dialog :title="title" :visible.sync="open" @close="close" width="900px">
      <div>
        <div class="stitle">已选</div>
        <div class="flex-start">
          <div v-for="tag in selectedList" :key="tag.name" class="tag-item">
            <el-tag closable  type="primary" @close="handleClose(tag)">
              {{ tag.name }}
            </el-tag>  
          </div>
        </div>
      </div>
      <el-divider></el-divider>
      <div>
        <el-card class="box-card" header="可选列表">
          <div class="flex-start" v-if="availableList && availableList.length">
            <div v-for="item in availableList" :key="item.id" class="tag-item"> 
              <el-tag type="success" effect="plain" @click="selectOpt(item)">
                {{ item.name }}
              </el-tag>
            </div>
          </div>
          <el-empty v-else description="可选内容为空"></el-empty>
        </el-card>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
        <el-button @click="close">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSeoCardPage } from '@/api/mall/product/seoCard'
export default {
  name: 'ProductSeoCardDialog',
  data() {
    return {
      title: '选择商品运营区域',
      loading: false,
      selectedIds: [],
      submitLoading: false,
      open: false,
      seoCardList: []
    }
  },
  computed: {
    selectedList() {
      if(this.seoCardList) {
        let arr = []
        this.selectedIds.forEach(id => {
          let obj = this.seoCardList.find(item => item.id === id)
          if(obj) arr.push(obj)
        })
        return arr
      }
      return []
    },
    availableList() {
      let list = JSON.parse(JSON.stringify(this.seoCardList || []))
      return list.filter(opt => !this.selectedIds.includes(opt.id))
    }
  },
  methods: {
    show(ids= []) {
      this.open = true
      this.selectedIds = ids
      this.loadSeoCardList()
    },
    selectOpt(opt) {
      if(this.selectedIds.includes(opt.id)) {
        return
      }
      this.selectedIds.push(opt.id)
    },
    handleClose(opt) {
      if(!this.selectedIds.includes(opt.id)) {
        return
      }

      let index = this.selectedIds.findIndex(id => id === opt.id)
      this.selectedIds.splice(index, 1)
    },
    loadSeoCardList() {
      let params = {
        pageSize: 100
      }
      getSeoCardPage(params).then(res => {
        this.seoCardList = res.data.list
      })
    },
    submitForm() {
      this.open = false
      if(!this.selectedList.length) {
        return
      }
      this.open = false
      this.$emit('on-update', this.selectedList)
    },
    close() {
      this.open = false
    }
  }

}
</script>

<style lang="scss" scoped>
.app-container {
  .stitle {
    margin-bottom: 15px;
  }
  .el-divider {
    margin: 10px 0;
  }
  .flex-start {
    flex-wrap: wrap;
  }
  .tag-item {
    padding: 5px;
  }
  .group-name {
    margin: 10px 0;
  }
}

</style>
