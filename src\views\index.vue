<template>
  <div class="dashboard-editor-container">
    <StatisticsOverview v-if="checkPermi(['statistics:overview'])"></StatisticsOverview>
    <div v-else class="welcome-container">
      <h5 class="welcome-title">欢迎来到{{ comTitle }}</h5>
      <p class="welcome-subtitle">为您带来高效、便捷的采购体验</p>
    </div>
  </div>
</template>

<script>
import StatisticsOverview from '@/views/mall/statistics/overview'
import { getTenantInfo } from "@/utils/auth";
export default {
  name: 'Index',
  components: {
    StatisticsOverview
  },
  data() {
    return {
    }
  },
  computed: {
    comTitle() {
      let title = null
      let tenantInfo = getTenantInfo()
      if(process.env.VUE_APP_LOCALIZATION) {
        title = process.env.VUE_APP_TITLE
      } else if(tenantInfo) {
        title = tenantInfo.title
      }
      return title || this.defaultTitle
    }
  },
}
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  padding: 32px;
  position: relative;
}

.welcome-container {
  text-align: center;
  // margin-top: 20px;
}

.welcome-title {
  font-size: 2.5em;
  font-weight: bold;
  color: #333;
}

.welcome-subtitle {
  font-size: 1.5em;
  color: #555;
  margin-top: 10px;
}

@media (max-width:1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
</style>
