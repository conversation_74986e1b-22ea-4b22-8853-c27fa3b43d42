import request from '@/utils/request'

// 创建快递100快递公司编码
export function createDeliveryCompany(data) {
  return request({
    url: '/trade/delivery-company/create',
    method: 'post',
    data: data
  })
}

// 更新快递100快递公司编码
export function updateDeliveryCompany(data) {
  return request({
    url: '/trade/delivery-company/update',
    method: 'put',
    data: data
  })
}

// 删除快递100快递公司编码
export function deleteDeliveryCompany(id) {
  return request({
    url: '/trade/delivery-company/delete?id=' + id,
    method: 'delete'
  })
}

// 获得快递100快递公司编码
export function getDeliveryCompany(id) {
  return request({
    url: '/trade/delivery-company/get?id=' + id,
    method: 'get'
  })
}

// 获得快递公司编码分页
export function getDeliveryCompanyPage(query) {
  return request({
    url: '/trade/delivery-company/page',
    method: 'get',
    params: query
  })
}

// 导出快递100快递公司编码 Excel
export function exportDeliveryCompanyExcel(query) {
  return request({
    url: '/trade/delivery-company/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获得快递100快递公司编码分页
export function getConfigCompanyPage(query) {
  return request({
    url: '/trade/config/delivery-company/page',
    method: 'get',
    params: query
  })
}

// 批量添加
export function batchAdd(ids) {
  return request({
    url: `/trade/delivery-company/batch-add?ids=${ids}`,
    method: 'get'
  })
}