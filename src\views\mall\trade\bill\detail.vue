<template>
  <div class="app-container" v-loading="loading">
    <template v-if="bill">
      <!-- 账单信息 -->
      <el-descriptions title="账单信息" :column="3" border>
        <el-descriptions-item label="账单编号">{{ bill.id }}</el-descriptions-item>
        <el-descriptions-item label="账单名称">{{ bill.billName }}</el-descriptions-item>
        <el-descriptions-item label="供应商">{{ bill.supplierName }}</el-descriptions-item>
        <el-descriptions-item label="账单状态"><el-tag :type="bill.billStatus | billStatusStyle">{{ bill.billStatus |
          billStatusInfo }}</el-tag></el-descriptions-item>
        <el-descriptions-item label="结算方式"><dict-tag :type="DICT_TYPE.BILL_SETTLEMENT_WAY"
            :value="bill.settlementWay" /></el-descriptions-item>
        <el-descriptions-item label="结算时间">{{ parseTime(bill.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="订单数量">{{ bill.orderCount }}</el-descriptions-item>
        <el-descriptions-item label="已结算数量">{{ bill.settlementCount }}</el-descriptions-item>
        <el-descriptions-item label="总金额">{{ formatMoney(bill.totalAmount) }}</el-descriptions-item>

        <el-descriptions-item label="操作人">{{ bill.userName }}</el-descriptions-item>
      </el-descriptions>

      <!-- 订单信息 -->
      <!--  :span-method="objectSpanMethod" -->
      <el-descriptions style="margin-top: 24px;" :colon="false" direction="vertical" :column="1">
        <template slot="title">
          <div class="description-title-1">
            <div>订单明细</div>
            <el-form :model="detailQueryParams" ref="detailQueryParams" class="order-form" size="small" :inline="true"
              label-width="68px">
              <el-form-item label="订单号">
                <el-input v-model="detailQueryParams.orderNo" clearable></el-input>
              </el-form-item>
              <el-form-item label="预约单号">
                <el-input v-model="detailQueryParams.voucherNo" clearable></el-input>
              </el-form-item>
              <el-form-item label="结算方式" prop="settlementWay">
                <el-select v-model="detailQueryParams.settlementWay" clearable placeholder="请选择结算方式" style="width:100%">
                  <el-option v-for="dict in this.getDictDatas(DICT_TYPE.BILL_SETTLEMENT_WAY)" :key="dict.value"
                    :label="dict.label" :value="parseInt(dict.value)" />
                </el-select>
              </el-form-item>
              <el-button type="primary" icon="el-icon-search" size="small"
                @click="initSearchBillRefOrder">搜索</el-button>
              <el-button icon="el-icon-refresh" size="small" @click="resetQuery2">重置</el-button>
            </el-form>
          </div>
        </template>
        <template slot="extra">
          <el-button style="margin-left: 16px;" v-if="[0, 1, 2, 3].includes(bill.billStatus)"
            v-hasPermi="['trade:bill:order']" type="primary" icon="el-icon-files" size="small"
            @click="openOrderRelationDrawer">添加订单</el-button>
        </template>
        <el-descriptions-item label="">
          <el-table ref="table" class="order-table" v-loading="loading" :data="orderList" row-key="no"
            default-expand-all :row-class-name="cellClass"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
            <el-table-column label="商品" width="320">
              <template slot-scope="{ row }">
                <span v-if="row.children != undefined" class="orderCodeNum">订单编号: </span>
                <span v-if="row.children != undefined">{{ row.no || '--' }}</span>
                <div v-else class="goodsCard">
                  <div class="goodsCard-item" v-for="item in row.productList" :key="item.skuId">
                    <div class="goodsCard-item-img">
                      <img :src="item.picUrl" alt="" height="30" />
                    </div>
                    <div class="goodsCard-item-info">
                      <div class="goodsCard-item-info-name text-ellipsis" :title="item.skuName">{{ item.skuName || '--'
                      }}</div>
                      <div class="goodsCard-item-info-code">{{ item.skuId || '--' }}</div>
                    </div>
                    <div v-if="item.isAsset">
                      <el-tag :type="item.assetStatus | orderAssetStatusStyle" size="small">固资</el-tag>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="单价" width="120">
              <template slot-scope="{ row }">
                <div v-if="row.children != undefined">创建时间:</div>
                <div v-else class="unit-price" v-for="item in row.productList" :key="item.skuId">
                  <span class="unit-price-number">{{ formatMoney(item.skuPrice) }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="数量" width="160">
              <template slot-scope="{ row }">
                <div v-if="row.children != undefined">
                  {{ parseTime(row.createTime) }}
                </div>
                <div v-else class="unit-price" v-for="item in row.productList" :key="item.skuId">
                  x{{ item.count }}
                  <div style="display:inline-block;margin-left:5px;" v-if="item.afterSaleStatus">
                    <dict-tag :type="DICT_TYPE.TRADE_ORDER_ITEM_AFTER_SALE_STATUS" :value="item.afterSaleStatus" />
                  </div>
                  <OrderAcceptInfo :orderItem="item" :orderStatus="row.status"></OrderAcceptInfo>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="下单人" align="center" width="100">
              <template slot-scope="{ row }">
                <span v-if="row.children != undefined && isProject">预约单号: </span>
                <div v-if="row.children == undefined">
                  <div>{{ row.userName }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="订单金额" align="center" width="160">
              <template slot-scope="{ row }">
                <span v-if="row.children != undefined">{{ row.voucherNo || '--' }}</span>
                <div v-if="row.children == undefined">
                  <div>净{{ formatMoney(row.orderPrice - row.refundPrice) }}</div>
                  <div v-if="row.refundPrice" style="color:#ff00008c;">退{{ formatMoney(row.refundPrice) }}</div>
                  <dict-tag :type="DICT_TYPE.MALL_PAYMENT_METHOD" :value="row.paymentMethod || 1" />
                </div>
              </template>
            </el-table-column>
            <el-table-column label="订单状态" align="center">
              <template slot-scope="{ row }">
                <div v-if="row.children == undefined">
                  <div class="status-box">
                    <div class="status-ball" :style="{ backgroundColor: getStatusColor(row.status) }"></div>
                    <el-tag :type="row.status | orderStatusStyle">{{ row.status | orderStatusInfo }}</el-tag>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="invoiceStatus" align="center" label="开票状态">
              <template v-slot="scope">
                <div v-if="scope.row.children == undefined">
                  <div class="status-box">
                    <el-tag :type="scope.row.invoiceStatus | invoiceStatusStyle">{{ scope.row.invoiceStatus |
                      invoiceStatusInfo }}</el-tag>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="assetStatus" align="center" label="固资状态" v-if="isAssetSwitch">
              <template v-slot="scope">
                <div v-if="scope.row.children == undefined">
                  <div class="status-box">
                    <el-tag :type="scope.row.assetStatus | orderAssetStatusStyle">{{ scope.row.assetStatus |
                      orderAssetStatusInfo }}</el-tag>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="billStatus" align="center" label="结算状态" v-if="isProject">
              <template v-slot="scope">
                <div v-if="scope.row.children == undefined">
                  <div class="status-box">
                    <el-tag :type="scope.row.ycrhStatus | orderYcrhStatusStyle">{{ scope.row.ycrhStatus |
                      orderYcrhStatusInfo }}</el-tag>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="offlineSettlement" align="center" label="结算方式" v-if="isProject">
              <template v-slot="scope">
                <div v-if="scope.row.children == undefined">
                  <div class="status-box">
                    <el-tag :type="scope.row.offlineSettlement | offlineSettlementWayStyle">{{
                      scope.row.offlineSettlement | offlineSettlementInfo }}</el-tag>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column fixed="right" label="操作" align="center">
              <template v-slot="scope">
                <template v-if="scope.row.children == undefined">
                  <el-button size="medium" type="text" @click="goOrderToDetail(scope.row)">详情</el-button>
                  <el-button v-if="[0, 1, 2, 3].includes(bill.billStatus)" v-hasPermi="['trade:bill:order']"
                    size="medium" type="text" @click="deleteOrderRelation(scope.row)">移除</el-button>
                  <el-button
                    v-if="[0, 2].includes(bill.billStatus) && [-2, 0, 1, 2, 3, 4, 5].includes(parseInt(scope.row.ycrhStatus)) && [0].includes(bill.settlementWay)"
                    v-hasPermi="['trade:bill:update']" size="medium" type="text"
                    @click="forceSyncOrder4YcrhHandle(scope.row)">同步</el-button>
                </template>
              </template>
            </el-table-column>

          </el-table>

          <pagination v-show="total > 0" :page-sizes="[20, 50, 100, 200, 300]" hide-on-single-page :total="total"
            :page.sync="pageNo" :limit.sync="pageSize" @pagination="getBillRefOrder" />

        </el-descriptions-item>
      </el-descriptions>
    </template>

    <el-drawer title="添加订单" :visible.sync="drawerVisible" direction="rtl" size="75%" class="bill-drawer"
      :before-close="handleClose">
      <div class="drawer-body-wrap">
        <el-form class="form" :model="drawerFormData" size="small" :inline="true" label-width="76px"
          style="padding-top: 10px;">
          <el-form-item label="订单编号">
            <el-input placeholder="请输入订单编号" v-model="drawerFormData.no" clearable style="width: 210px;"></el-input>
          </el-form-item>
          <el-form-item label="发票状态">
            <el-select v-model="drawerFormData.invoiceStatus" clearable="">
              <el-option v-for="item in invoiceStatusList" :key="item.label" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="固资状态" v-if="isAssetSwitch">
            <el-select v-model="drawerFormData.assetStatusList" multiple clearable="">
              <el-option v-for="item in assetStatusList" :key="item.label" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="支付方式" prop="paymentMethod" label-width="70px" v-if="payMethodOpts.length">
            <el-select v-model="drawerFormData.paymentMethod" clearable>
              <el-option v-for="dict in payMethodOpts" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="完成时间" prop="finishTime">
            <el-date-picker v-model="drawerFormData.finishTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss"
              type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']" />
          </el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="getBillOrder">搜索</el-button>
          <el-button type="primary" icon="el-icon-add" size="small" @click="batchAddBillOrder">批量添加</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>

          <el-table ref="multipleTable" class="order-table" v-loading="drawerLoading" :data="billOrderList" row-key="no"
            max-height="600" default-expand-all :row-class-name="cellClass" @selection-change="handleSelectionChange"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
            <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>

            <el-table-column label="商品" width="400">
              <template slot-scope="{ row }">
                <span v-if="row.children != undefined" class="orderCodeNum">
                  订单编号: <el-button @click="goOrderToDetail(row)" type="text">{{ row.no || '--' }}</el-button>
                </span>
                <div v-else class="goodsCard">
                  <div class="goodsCard-item" v-for="item in row.productList" :key="item.skuId">
                    <div class="goodsCard-item-img">
                      <img :src="item.picUrl" alt="" height="25" />
                    </div>
                    <div class="goodsCard-item-info">
                      <div class="goodsCard-item-info-name text-ellipsis" :title="item.skuName">{{ item.skuName || '--'
                      }}</div>
                      <div class="goodsCard-item-info-code">{{ item.skuId || '--' }}</div>
                    </div>
                    <div v-if="item.isAsset">
                      <el-tag :type="item.assetStatus | orderAssetStatusStyle" size="small">固资</el-tag>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="单价" width="130px">
              <template slot-scope="{ row }">
                <div v-if="row.children != undefined">完成时间:</div>
                <div v-else class="unit-price" v-for="item in row.productList" :key="item.skuId">
                  <span class="unit-price-number">{{ formatMoney(item.skuPrice) }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="数量" width="180px">
              <template slot-scope="{ row }">
                <div v-if="row.children != undefined">
                  {{ parseTime(row.finishTime) }}
                </div>
                <div v-else class="unit-price" v-for="item in row.productList" :key="item.skuId">
                  x{{ item.count }}
                  <div style="display:inline-block;margin-left:5px;" v-if="item.afterSaleStatus">
                    <dict-tag :type="DICT_TYPE.TRADE_ORDER_ITEM_AFTER_SALE_STATUS" :value="item.afterSaleStatus" />
                  </div>
                  <OrderAcceptInfo :orderItem="item" :orderStatus="row.status"></OrderAcceptInfo>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="下单人" align="center">
              <template slot-scope="{ row }">
                <div v-if="row.children == undefined">
                  <div>{{ row.userName }}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="下单金额" align="center" width="130">
              <template slot-scope="{ row }">
                <div v-if="row.children == undefined">
                  <div>总{{ formatMoney(row.orderPrice) }}</div>
                  <div v-if="row.refundPrice" style="color:#ff00008c;">退{{ formatMoney(row.refundPrice) }}</div>
                  <dict-tag :type="DICT_TYPE.MALL_PAYMENT_METHOD" :value="row.paymentMethod || 1" />
                </div>
              </template>
            </el-table-column>

            <el-table-column label="订单状态" align="center">
              <template slot-scope="{ row }">
                <div v-if="row.children == undefined">
                  <div class="status-box">
                    <div class="status-ball" :style="{ backgroundColor: getStatusColor(row.status) }"></div>
                    <el-tag :type="row.status | orderStatusStyle">{{ row.status | orderStatusInfo }}</el-tag>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="开票状态" align="center">
              <template v-slot="scope">
                <div v-if="scope.row.children == undefined">
                  <div class="status-box">
                    <el-tag :type="scope.row.invoiceStatus | invoiceStatusStyle">{{ scope.row.invoiceStatus |
                      invoiceStatusInfo }}</el-tag>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="固资状态" align="center" v-if="isAssetSwitch">
              <template v-slot="scope">
                <div v-if="scope.row.children == undefined">
                  <div class="status-box">
                    <el-tag :type="scope.row.assetStatus | orderAssetStatusStyle">{{ scope.row.assetStatus |
                      orderAssetStatusInfo }}</el-tag>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="结算方式" align="center">
              <template v-slot="scope">
                <div v-if="scope.row.children == undefined">
                  <div class="status-box">
                    <el-tag :type="scope.row.offlineSettlement | offlineSettlementWayStyle">{{
                      scope.row.offlineSettlement | offlineSettlementInfo }}</el-tag>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="invoiceStatus" label="发票验真" v-if="false">
              <template v-slot="scope">
                <div v-if="scope.row.children == undefined">
                  <div class="status-box">
                    <el-tag :type="scope.row.invoiceCheckStatus ? 'success' : 'info'">{{ scope.row.invoiceCheckStatus ?
                      '已验' : '未验' }}</el-tag>
                  </div>
                </div>
              </template>
            </el-table-column>

          </el-table>
        </el-form>

        <pagination v-show="drawerPage.total > 0" :page-sizes="[50, 100, 200, 300]" hide-on-single-page
          :total="drawerPage.total" :page.sync="drawerPage.pageNo" :limit.sync="drawerPage.pageSize"
          style="margin-top:5px;padding-top:10px;" @pagination="getBillOrder" />

        <div class="drawer__footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="submitSelection">确 认</el-button>
        </div>
      </div>
    </el-drawer>

  </div>
</template>

<script>
import * as api from "@/api/mall/trade/bill.js"
import { INVOICE_STATUS_LIST, ORDER_ASSET_STATUS, SETTLEMENT_WAY_LIST } from '@/utils/mallUtil'
import { configMixins } from '@/views/mall/config/components/configMixin.js'
import OrderAcceptInfo from '@/views/mall/trade/order/components/acceptInfo'
export default {
  name: "TradeBillDetail",
  components: { OrderAcceptInfo },
  mixins: [configMixins],
  data() {
    return {
      pushLoading: false,
      loading: false,
      bill: null,
      orderList: [],
      total: 0,
      pageNo: 1,
      pageSize: 20,
      changed: false,
      submitLoading: false,
      drawerLoading: false,
      selectedList: [],
      billOrderList: [],
      drawerTotal: 0,
      drawerVisible: false,
      drawerPage: {
        total: 0,
        pageNo: 1,
        pageSize: 50
      },
      drawerFormData: {
        no: '',
        invoiceStatus: 2,
        paymentMethod: null,
        assetStatusList: [],
        finishTime: []
      },
      invoiceStatusList: INVOICE_STATUS_LIST.filter(item => item.value < 4),
      settlementWayList: SETTLEMENT_WAY_LIST,
      assetStatusList: ORDER_ASSET_STATUS,
      detailQueryParams: {}
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      api.getBill({
        id: this.$route.query.id || ''
      }).then(res => {
        this.bill = res.data
        this.initSearchBillRefOrder()
      })
    },
    initSearchBillRefOrder() {
      this.pageNo = 1
      this.getBillRefOrder()
    },
    // 重置
    resetQuery2() {
      this.pageNo = 1
      this.detailQueryParams.orderNo = null
      this.detailQueryParams.voucherNo = null
      this.detailQueryParams.settlementWay = null
      this.getBillRefOrder()
    },
    async getBillRefOrder() {
      this.loading = true
      const params = {
        billId: this.$route.query.id || '',
        orderNo: this.detailQueryParams.orderNo || null,
        voucherNo: this.detailQueryParams.voucherNo || null,
        pageNo: this.pageNo,
        pageSize: this.pageSize
      }
      if ([0, 1].includes(this.detailQueryParams.settlementWay)) {
        params.offlineSettlement = this.detailQueryParams.settlementWay == 1
      }
      const res = await api.getBillRefOrder(params)
      if (res.code === 0 && res.data) {
        const list = res.data.list || [];
        list.forEach((item, index) => {
          item.children = [
            {
              id: item.id,
              no: item.no + index,
              trueNo: item.no,
              productList: item.items || [],
              productCount: item.productCount,
              userName: item.userName,
              receiverName: item.receiverName,
              productPrice: item.productPrice,
              orderPrice: item.orderPrice,
              refundPrice: item.refundPrice,
              paymentMethod: item.paymentMethod,
              status: item.status,
              billStatus: item.billStatus,
              afterSaleStatus: item.afterSaleStatus,
              ycrhStatus: item.ycrhStatus,
              assetStatus: item.assetStatus,
              finishTime: item.finishTime,
              createTime: item.createTime,
              updateTime: item.updateTime,
              invoiceStatus: item.invoiceStatus,
              invoiceCheckStatus: item.invoiceCheckStatus,
              receiverMobile: item.receiverMobile,
              offlineSettlement: item.offlineSettlement
            }
          ]
        })
        this.orderList = list
        this.total = Number(res.data.total)
      } else {
        this.orderList = []
        this.total = 0
      }
      this.loading = false
    },
    // 查询待结算订单
    async getBillOrder() {
      this.drawerLoading = true
      let params = {
        no: this.drawerFormData.no || '',
        finishTime: this.drawerFormData.finishTime,
        invoiceStatus: this.drawerFormData.invoiceStatus,
        assetStatusList: this.drawerFormData.assetStatusList,
        paymentMethod: this.drawerFormData.paymentMethod,
        // offlineSettlement: this.bill.settlementWay == 1,
        pageNo: this.drawerPage.pageNo,
        pageSize: this.drawerPage.pageSize,
        supplierId: this.bill.supplierId,
        notBillId: this.bill.id,
        parentType: 0
      }
      if (params.finishTime && params.finishTime.length > 0) {
        params.finishTime = params.finishTime.join(',')
      }

      const res = await api.getBillOrder(params)
      if (res.code === 0 && res.data) {
        const list = res.data.list || [];
        list.forEach((item, index) => {
          item.children = [
            {
              id: item.id,
              no: item.no + index,
              trueNo: item.no,
              productList: item.items || [],
              productCount: item.productCount,
              userName: item.userName,
              receiverName: item.receiverName,
              productPrice: item.productPrice,
              orderPrice: item.orderPrice,
              refundPrice: item.refundPrice,
              paymentMethod: item.paymentMethod,
              offlineSettlement: item.offlineSettlement,
              status: item.status,
              assetStatus: item.assetStatus,
              afterSaleStatus: item.afterSaleStatus,
              createTime: item.createTime,
              updateTime: item.updateTime,
              invoiceStatus: item.invoiceStatus,
              invoiceCheckStatus: item.invoiceCheckStatus,
              receiverMobile: item.receiverMobile
            }
          ]
        })

        this.billOrderList = list
        this.selectedList.forEach(item => {
          this.$refs.multipleTable.toggleRowSelection(item, true)
        })
        this.drawerPage.total = Number(res.data.total)
      } else {
        this.billOrderList = []
        this.selectedList = []
        this.drawerPage.total = 0
      }
      this.drawerLoading = false
    },
    // 一键批量添加订单到账单
    async batchAddBillOrder() {
      if (this.billOrderList.length === 0) {
        this.$message.info('请先查询订单')
        return;
      }
      this.drawerLoading = true
      let params = {
        finishTime: this.drawerFormData.finishTime,
        invoiceStatus: this.drawerFormData.invoiceStatus,
        assetStatusList: this.drawerFormData.assetStatusList,
        supplierId: this.bill.supplierId,
        notBillId: this.bill.id,
        parentType: 0,
        notZero: true,
        status: 8
      }
      if (this.drawerFormData.no != null && this.drawerFormData.no != '') {
        params.no = this.drawerFormData.no
      }
      if (params.finishTime && params.finishTime.length > 0) {
        params.finishTime = params.finishTime.join(',')
      }

      const res = await api.batchAddOrderRelation(params)
      if (res.code === 0) {
        this.changed = true
        this.$message.success('批量添加订单成功！')
        this.getBillOrder()
      }
      this.drawerLoading = false
    },
    // 勾选订单
    handleSelectionChange(val) {
      this.selectedList = val;
    },
    // 点击关联订单按钮
    openOrderRelationDrawer() {
      this.drawerVisible = true
      this.drawerFormData.no = ''
      this.selectedList = []
      this.getBillOrder()
      this.$nextTick(() => {
        this.$refs.multipleTable.clearSelection()
      })
    },
    // 关闭抽屉
    handleClose() {
      this.drawerVisible = false
      this.billOrderList = []
      this.drawerFormData.no = ''
      this.drawerPage.pageNo = 1
      if (this.changed) {
        this.changed = false
        this.init()
      }
    },
    // 重置
    resetQuery() {
      this.drawerFormData.no = ''
      this.drawerFormData.finishTime = []
      this.drawerFormData.invoiceStatus = 2
      this.drawerPage.pageNo = 1
      this.getBillOrder()
    },
    // 提交
    async submitSelection() {
      if (!this.selectedList.length) {
        this.$message.info('请选择订单')
        return;
      }
      let orderNos = this.selectedList.map(item => item.no)
      let params = {
        billId: this.bill.id,
        orderNos: orderNos
      }
      let res = await api.addBillOrderRelation(params)
      if (res.code === 0) {
        this.changed = true
        this.$message.success('添加订单成功！')
        this.getBillOrder()
      }
    },
    // 删除订单关联
    async deleteOrderRelation(row) {
      let params = {
        billId: this.bill.id,
        orderNos: [row.trueNo]
      }
      let res = await api.deleteBillOrderRelation(params)
      if (res.code === 0) {
        this.$message.success('删除订单关联成功！')
        this.getBillRefOrder()
      }
    },
    forceSyncOrder4YcrhHandle(row) {
      this.$modal.confirm('是否同步订单到业财').then(() => {
        let params = {
          orderNo: row.trueNo
        }
        this.forceSyncOrder4Ycrh(params)
      }).catch(() => {
      });
    },
    // 强制同步订单至业财融合
    async forceSyncOrder4Ycrh(params) {
      await api.forceSyncOrder4Ycrh(params)
      this.$message.success('同步订单到业财成功')
    },
    getStatusColor(status) {
      if ([1, 2, 3, 6].includes(status)) {
        return '#FF9500'
      } else if (status === 9) {
        return '#E5E6EB'
      }
      return '#00B42A'
    },
    cellClass({ row }) {
      if (!row.hasOwnProperty('children')) {
        return 'disableheadselection'
      }
      return 'orderCode'
    },
    goOrderToDetail(row) {
      this.$router.push({ name: 'TradeOrderDetail', query: { id: row.id } })
    },
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-descriptions) {
  &:not(:nth-child(1)) {
    margin-top: 20px;
  }

  .el-descriptions__title {
    display: flex;
    align-items: center;

    &::before {
      content: '';
      display: inline-block;
      margin-right: 10px;
      width: 3px;
      height: 20px;
      background-color: #409EFF;
    }
  }

  .el-descriptions-item__container {
    margin: 0 10px;

    .no-colon {
      margin: 0;

      &::after {
        content: ''
      }
    }
  }
}

.clickInvoice {
  cursor: pointer;

  &:hover {
    color: #f13d33;
  }
}

.product-info {
  display: flex;
  align-items: center;

  .skuName {
    margin-left: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    text-overflow: -o-ellipsis-lastline;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    word-break: break-all;
    -webkit-box-orient: vertical;
  }
}

.order-table {
  :deep(.el-table__expand-icon) {
    display: none;
  }

  :deep(.disableheadselection) {
    color: #8c8c8c;

    >td:first-child {
      >.cell {
        >.el-checkbox {
          display: none;
        }
      }
    }
  }

  :deep(.orderCode) {
    color: #333333;
    background-color: #f2f4f7;
  }

  .orderCodeNum {
    font-size: 12px;
    font-weight: bold;
    color: #333333;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .goodsCard {
    display: block;

    .goodsCard-item {
      display: flex;
      width: 100%;

      +.goodsCard-item {
        margin-top: 20px;
      }

      .goodsCard-item-img {
        position: relative;
      }

      .goodsCard-item-info {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-left: 8px;
        font-size: 12px;
        line-height: 20px;

        .goodsCard-item-info-name {
          max-width: 300px;
          color: #1d2129;
        }

        .goodsCard-item-info-code {
          color: #86909c;
        }
      }
    }
  }

  .unit-price {
    display: flex;
    align-items: center;
    height: 38px;

    +.unit-price {
      margin-top: 20px;
    }

    .unit-price-number {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .status-box {
    display: flex;
    align-items: center;
    justify-content: center;

    .status-ball {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      margin-right: 10px;
    }
  }
}

.bill-drawer {
  :deep(.el-drawer__header) {
    font-size: 20px;
    font-weight: 600;
    border-bottom: 1px solid #eee;
    padding-bottom: 0px;
  }

  .drawer-body-wrap {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;

    .form {
      padding: 10px;
    }

    .drawer__footer {
      text-align: right;
      border-top: 1px solid #eee;
      padding: 10px;
    }
  }
}

.description-title-1 {
  display: flex;
  align-items: center;
  justify-content: center;

  .el-form {
    margin-top: 18px;
  }
}
</style>
