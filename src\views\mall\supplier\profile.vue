<template>
  <div class="app-container supplier-profile" v-loading="loading">
    <el-card v-if="initSuccess">
      <el-tabs v-model="activeTab" @tab-click="saveTabCache">
        <el-tab-pane label="基本资料" name="name1">
          <div class="simple-tab-content">
            <BaseInfo :supplierInfo="supplierInfo" @on-update="onUpdated"></BaseInfo>
          </div>
        </el-tab-pane>
        <el-tab-pane label="起售金额" name="name2">
          <div class="simple-tab-content">
            <Threshold :supplierInfo="supplierInfo" @on-update="onUpdated"></Threshold>
          </div>
        </el-tab-pane>
        <el-tab-pane label="客服信息" name="name3">
          <div class="simple-tab-content">
            <CustomerService :supplierInfo="supplierInfo" @on-update="onUpdated"></CustomerService>
          </div>  
        </el-tab-pane>
        <el-tab-pane label="收款账号" name="name4" v-if="checkPermi(['mall:supplier-profile:account'])">
          <div class="simple-tab-content">
            <SupplierAccount :supplierInfo="supplierInfo" @on-update="onUpdated"></SupplierAccount>
          </div>
        </el-tab-pane>
        <el-tab-pane label="开放API" name="name5" v-if="checkPermi(['mall:supplier-profile:openapp']) && supplierInfo.type === 10">
          <div class="simple-tab-content">
            <SupplierOpenApp :supplierInfo="supplierInfo" @on-update="onUpdated"></SupplierOpenApp>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    <div v-else>
      <el-empty description="配置异常，请联系运营"></el-empty>
    </div>
  </div>
</template>

<script>
import * as api from '@/api/mall/supplier/supplier-profile'
import BaseInfo from '@/views/mall/supplier/components/base-info'
import Threshold from '@/views/mall/supplier/components/threshold'
import CustomerService from '@/views/mall/supplier/components/customer-service'
import SupplierAccount from '@/views/mall/supplier/components/account'
import SupplierOpenApp from '@/views/mall/supplier/components/open-app'
export default {
  name: 'SupplierProfile',
  components: { BaseInfo, Threshold, CustomerService, SupplierAccount, SupplierOpenApp },
  data() {
    return {
      loading: false,
      activeTab: 'name1',
      supplierInfo: {},
      initSuccess: false
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      this.initTab()
      await this.loadSupplierInfo()
      this.initSuccess = true
    },
    onUpdated() {
      this.loadSupplierInfo()
    },
    async loadSupplierInfo() {
      try {
        this.loading = true
        let res = await api.getSupplierProfile()
        this.supplierInfo = res.data
        this.loading = false
      } catch(e) {
        this.loading = false
      }
    },
    getCacheTabKey1() {
      return `mall-supplier-profile-tab-${this.$store.state.user.id}`
    },
    initTab() {
      let value = sessionStorage.getItem(this.getCacheTabKey1())
      if(value) {
        this.activeTab = value
      } else {
        this.activeTab = 'name1'
      }
    },
    saveTabCache() {
      sessionStorage.setItem(this.getCacheTabKey1(), this.activeTab)
    }
  }
}
</script>

<style lang="scss">
.supplier-profile {
  .simple-tab-content {

  }
  .el-alert__description {
    font-size: 13px;
  }
  .title {
    font-size: 14px;
    margin-right: 10px;
  }
  .list-group-item {
    font-size: 13px;
  }
  .list-group-item:first-child {
    border-top: none;
  }
}
</style>