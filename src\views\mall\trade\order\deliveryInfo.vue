<template>
  <div>
    <!-- 物流公司 -->
    <el-descriptions title="" :column="4" direction="vertical" border>
      <el-descriptions-item>
        <template slot="label">
          快递公司
        </template>
        {{ deliveryInfo.name || '--' }} - {{ deliveryInfo.com  }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          快递单号
        </template>
        {{ deliveryInfo.num || '--' }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          来源
        </template>
        {{ deliveryInfo.source || '--' }}
      </el-descriptions-item>
    </el-descriptions>

    <div> 
      <el-table
        :data="deliveryInfo.skuList"
        style="width: 100%;">
        <el-table-column prop="picUrl" label="商品">
          <template v-slot="scope">
            <img v-if="scope.row.picUrl" :src="scope.row.picUrl" alt="图片" style="height: 35px;" />
            <span> {{ scope.row.skuName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="skuId" label="商品SKU"></el-table-column>
      </el-table>
    </div>

    <el-descriptions :column="1">
      <el-descriptions-item labelClassName="no-colon">
        <!-- 包裹详情 -->
        <el-timeline>
          <el-timeline-item v-for="(data, index) in deliveryInfo.trackList" :key="index"
            :timestamp="parseTime(data.deliveryTime)">
            {{ data.content }}（{{ data.status }}）
          </el-timeline-item>
        </el-timeline>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>
  
<script>

export default {
  name: "DeliveryInfo",
  data() {
    return {

    }
  },
  props: {
    deliveryInfo: {
      type: Object,
      default: () => {}
    }
  },
  methods: {}
}
</script>
  
<style lang="scss" scoped>
:deep(.el-descriptions) {
  &:not(:nth-child(1)) {
    margin-top: 20px;
  }

  .el-descriptions__title {
    display: flex;
    align-items: center;

    &::before {
      content: '';
      display: inline-block;
      margin-right: 10px;
      width: 3px;
      height: 20px;
      background-color: #409EFF;
    }
  }

  .el-descriptions-item__container {
    margin: 0 10px;

    .no-colon {
      margin: 0;

      &::after {
        content: ''
      }
    }
  }
}
</style>
  