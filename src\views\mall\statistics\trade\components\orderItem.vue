<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" class="order-form" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="搜索方式">
        <el-input v-model="queryParams.searchValue" clearable style="width: 300px">
          <el-select v-model="queryParams.searchType" slot="prepend" style="width: 100px">
            <el-option v-for="dict in searchTypes" :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-input>
      </el-form-item>
      <el-form-item label="供应商" prop="supplierId">
        <supplier-select v-model="queryParams.supplierId" placeholder="请选择供应商" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="商品分类" prop="categoryIds">
        <category-select v-model="queryParams.categoryIds" :extProps="{checkStrictly: true}" placeholder="请选择商品分类" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="下单时间" prop="submitTime">
        <el-date-picker v-model="queryParams.submitTime" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item label="订单状态">
        <el-select v-model="queryParams.orderStatus" clearable="">
          <el-option :value="1" label="未确认"/>
          <el-option :value="2" label="已确认"/>
          <el-option :value="3" label="已发货"/>
          <el-option :value="4" label="已送达"/>
          <el-option :value="5" label="已签收"/>
          <el-option :value="8" label="已完成"/>
          <el-option :value="9" label="已取消"/>
        </el-select>
      </el-form-item>
      <el-form-item label="标签状态" prop="tagStatus">
        <el-select v-model="queryParams.tagStatus" clearable placeholder="请选择" @keyup.enter.native="handleQuery">
          <el-option :value="1" label="有标签"></el-option>
          <el-option :value="0" label="无标签"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="商品标签" prop="skuTags">
        <tag-select size="small" v-model="queryParams.skuTags" placeholder="请选择标签" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <div>
          <el-button v-hasPermi="['stats:order-item:export']" icon="el-icon-files" size="mini" type="primary" plain :loading="exportLoading" @click="exportData">导出</el-button>
        </div>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="商品名称" align="center" prop="skuName" width="240" show-overflow-tooltip="">
        <template slot-scope="{ row }">
          <div>{{ row.skuName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="商品SKU" align="center" prop="skuId" width="190">
        <template slot-scope="{ row }">
          <div>{{ row.skuId }}</div>
          <template v-if="row.thirdSkuId">
            <div>{{ row.thirdSkuId }}</div>  
          </template>
        </template>
      </el-table-column>
      <el-table-column label="商品标签" align="center" prop="skuTags" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <div> {{ row.skuTags && row.skuTags.length ? row.skuTags.join('/') : '' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="商品单价" align="center" prop="price">
        <template slot-scope="{ row }">
          <div>{{ formatMoney(row.price) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="数量" align="center" prop="quantity">
        <template slot-scope="{ row }">
          <div> x{{ row.quantity }}</div>
        </template>
      </el-table-column>
      <el-table-column label="总金额" align="center" prop="totalPrice">
        <template slot-scope="{ row }">
          <div>{{ formatMoney(row.totalPrice) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="订单号" align="center" prop="orderNo" width="190">
        <template slot-scope="{ row }">
          <el-link :underline="false" type="primary" @click="goOrderDetail(row)">{{ row.orderNo }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" align="center" prop="orderStatus" width="170">
        <template slot-scope="{ row }">
          <el-tag :type="row.orderStatus | orderStatusStyle">
            {{ row.orderStatus | orderStatusInfo }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="供应商" align="center" prop="supplierName" show-overflow-tooltip=""/>
      <el-table-column label="用户名" align="center" prop="userName" show-overflow-tooltip="">
        <template slot-scope="{ row }">
          <div>{{ row.userName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="下单时间" align="center" prop="submitTime" width="180">
        <template slot-scope="{ row }">
          <div>{{ parseTime(row.submitTime) }}</div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize" @pagination="getList"/>
    
    <ExportAlert ref="exportAlert"></ExportAlert>
  </div>
</template>

<script>
import * as api from '@/api/mall/statistics/stats-v2'
import CategorySelect from "@/views/mall/product/spu/components/category-select"
import SupplierSelect from "@/views/mall/product/spu/components/supplier-select"
import TagSelect from "@/views/mall/product/seo/components/tag-select"
import ExportAlert from '@/components/AsyncTaskAlert/export'
export default {
  name: 'StatsTradeOrderItemCom',
  components: {
    CategorySelect, SupplierSelect, TagSelect, ExportAlert
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      allTotal: 0,
      total: 0,
      // 列表
      list: [],
      queryParams: {
        pageNo: 1,
        pageSize: 20,
        searchType: 'skuId',
        searchValue: '',
        supplierId: null,
        tagStatus: null,
        orderStatus: null,
        submitTime: [],
        categoryIds: [],
        skuTags: []
      },
      searchTypes: [
        { label: '平台SKU', value: 'skuId' },
        { label: '三方SKU', value: 'thirdSkuId' },
        { label: '用户名', value: 'userName' },
        { label: '用户ID', value: 'userId' },
        { label: '订单号', value: 'orderNo' },
        { label: '部门名称', value: 'deptName' },
        { label: '部门编码', value: 'deptCode' }
      ]
    }
  },
  created() {
    this.handleQuery();
  },
  methods: {
    /** 查询列表 */
    async getList() {
      this.loading = true;
      const params = this.getParams();
      if (params == null) {
        this.loading = false;
        return
      }
      const res = await api.pageQueryStatsOrderItem(params);
      if (res.code === 0 && res.data) {
        const list = res.data.list;
        this.list = list
        if (this.queryParams.status === 'all') {
          this.allTotal = Number(res.data.total)
        }
        this.total = Number(res.data.total)
      } else {
        this.list = []
        this.total = 0
      }
      this.loading = false;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 20,
        searchType: 'skuId',
        searchValue: '',
        supplierId: null,
        tagStatus: null,
        orderStatus: null,
        submitTime: [],
        categoryIds: [],
        skuTags: []
      }
      this.handleQuery();
    },
    getParams() {
      const params = {
        pageNo: this.queryParams.pageNo,
        pageSize: this.queryParams.pageSize,
        tagStatus: this.queryParams.tagStatus,
        orderStatus: this.queryParams.orderStatus,
        sortType: 1
      }
      if(this.queryParams.supplierId) {
        params.supplierId = this.queryParams.supplierId
      }
      if (this.queryParams.submitTime && this.queryParams.submitTime.length > 0) {
        params.submitTime = this.queryParams.submitTime.join(',')
      }
      if (this.queryParams.searchValue) {
        this.$set(params, this.queryParams.searchType, this.queryParams.searchValue)
      }
      if(this.queryParams.skuTags && this.queryParams.skuTags.length) {
        params.skuTags = this.queryParams.skuTags
      }
      if(this.queryParams.categoryIds && this.queryParams.categoryIds.length) {
        if(this.queryParams.categoryIds.length > 0) {
          params.categoryId1 = this.queryParams.categoryIds[0]
        }
        if(this.queryParams.categoryIds.length > 1) {
          params.categoryId2 = this.queryParams.categoryIds[1]
        }
        if(this.queryParams.categoryIds.length > 2) {
          params.categoryId3 = this.queryParams.categoryIds[2]
        }
      }

      if(this.queryParams.searchType == 'userId'){
        if (Number.isNaN(Number(this.queryParams.searchValue))){
          this.$message.error('用户ID必须为数字')
          return null
        }
      }
      else if(this.queryParams.searchType == 'skuId'){
        if (Number.isNaN(Number(this.queryParams.searchValue))){
          this.$message.error('平台SKU须为数字')
          return null
        }
      }

      return params
    },
    goOrderDetail(row) {
      this.$router.push({ name: 'TradeOrderDetail', query: { id: row.orderId } })
    },
    exportData() {
      const params = this.getParams();
      this.$refs.exportAlert.init(api.exportStatsOrderItem, params, '销售明细导出')
    }
  }

}
</script>

<style>

</style>