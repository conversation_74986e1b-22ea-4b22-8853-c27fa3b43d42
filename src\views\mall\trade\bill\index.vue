<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" class="order-form" size="small" :inline="true" v-show="showSearch"
      label-width="68px">
      <el-row>
        <el-form-item label="账单编号">
          <el-input v-model="queryParams.billId" clearable></el-input>
        </el-form-item>
        <el-form-item label="账单名称">
          <el-input v-model="queryParams.searchValue" clearable></el-input>
        </el-form-item>
        <el-form-item label="订单编号">
          <el-input v-model="queryParams.orderNo" clearable></el-input>
        </el-form-item>
        <el-form-item label="预约单号">
          <el-input v-model="queryParams.voucherNo" clearable></el-input>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item v-if="notSupplierUser()" v-hasPermi="['mall:supplier']" label="供应商" prop="supplierId">
          <supplier-select v-model="queryParams.supplierId" placeholder="请选择供应商" @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="结算方式" prop="settlementWay">
          <el-select v-model="queryParams.settlementWay" clearable placeholder="请选择结算方式" style="width:100%">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.BILL_SETTLEMENT_WAY)" :key="dict.value"
              :label="dict.label" :value="parseInt(dict.value)" />
          </el-select>
        </el-form-item>
        <el-form-item label="账单状态">
          <el-select v-model="queryParams.status" clearable>
            <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker v-model="queryParams.createTime" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
            range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" clearable
            :default-time="['00:00:00', '23:59:59']" />
        </el-form-item>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
      </el-row>

    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button icon="el-icon-document-add" size="mini" v-hasPermi="['trade:bill:create']" type="primary" plain
          @click="handleAdd">新建账单</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="el-icon-files" size="mini" v-hasPermi="['trade:bill:export']" type="primary" plain
          :loading="exportLoading" @click="handleExport">导出账单</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="list" @selection-change="handleSelectionChange" border>
      <el-table-column type="selection" align="center" width="55"></el-table-column>
      <el-table-column label="账单编号" align="center" prop="id" width="185"></el-table-column>
      <el-table-column label="账单名称" align="center" prop="billName"></el-table-column>
      <el-table-column label="供应商" align="center" prop="supplierName" width="150"></el-table-column>
      <el-table-column label="订单数量" align="center" prop="orderCount" width="120"></el-table-column>
      <el-table-column label="总金额" align="center" prop="totalAmount" width="140">
        <template v-slot="scope">
          <span>{{ formatMoney(scope.row.totalAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="settlementWay" align="center" label="结算方式" width="120">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.BILL_SETTLEMENT_WAY" :value="scope.row.settlementWay" />
        </template>
      </el-table-column>
      <el-table-column label="账单状态" align="center" prop="billStatus" width="120">
        <template v-slot="scope">
          <el-tag :type="scope.row.billStatus | billStatusStyle">{{ scope.row.billStatus | billStatusInfo }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作人" align="center" prop="userName" width="150" />
      <el-table-column fixed="right" label="操作" align="center" width="220">
        <template v-slot="scope">
          <el-button size="medium" type="text" @click="goToDetail(scope.row)"
            v-hasPermi="['trade:bill:query']">详情</el-button>
          <el-button v-if="[0, 2].includes(scope.row.billStatus)" size="medium" type="text"
            @click="handleUpdate(scope.row)" v-hasPermi="['trade:bill:update']">修改</el-button>
          <el-button v-if="[0, 2].includes(scope.row.billStatus) && scope.row.orderCount > 0" size="medium" type="text"
            @click="changeSettlementWay(scope.row)" v-hasPermi="['trade:bill:update']">{{ scope.row.settlementWay == 0 ?
              '批量冻结' : '批量解冻' }}</el-button>
          <el-button v-if="[0].includes(scope.row.billStatus) && scope.row.orderCount > 500"
            v-hasPermi="['trade:bill:push']" size="medium" type="text" @click="splitBillHandler([scope.row.id])"
            :loading="billLoading">拆分账单</el-button>
          <el-button
            v-if="[0, 1, 2].includes(scope.row.billStatus) && [0].includes(scope.row.settlementWay) && scope.row.orderCount > 0"
            v-hasPermi="['trade:bill:push']" size="medium" type="text" @click="pushBillHandler(scope.row)"
            :loading="billLoading">发起结算</el-button>
          <el-button v-if="[1].includes(scope.row.billStatus) && scope.row.orderCount > 0"
            v-hasPermi="['trade:bill:cancel']" size="medium" type="text" @click="cancelBillHandler(scope.row.id)"
            :loading="billLoading">取消结算</el-button>
          <el-button v-if="[0, 1, 2].includes(scope.row.billStatus) && scope.row.orderCount > 0"
            v-hasPermi="['trade:bill:export-settle']" size="medium" type="text"
            @click="exportSettleHandle(scope.row.id)" :loading="billLoading">导出结算单</el-button>
          <el-button v-if="[0, 1, 2].includes(scope.row.billStatus) && scope.row.orderCount > 0"
            v-hasPermi="['trade:bill:export-archive']" size="medium" type="text"
            @click="exportSettleArchiveHandle(scope.row.id)" :loading="billLoading">导出发票结算单</el-button>
          <el-button
            v-if="[0, 1, 2].includes(scope.row.billStatus) && [0].includes(scope.row.settlementWay) && scope.row.orderCount > 0"
            v-hasPermi="['trade:bill:update']" size="medium" type="text"
            @click="syncOrder4YcrhInBillHandle(scope.row.id)" :loading="billLoading">同步订单</el-button>
          <el-button v-if="[0, 1, 2, 3].includes(scope.row.billStatus) && scope.row.orderCount > 0"
            v-hasPermi="['trade:bill:update']" size="medium" type="text"
            @click="syncOrderBillStatusHandle(scope.row.id)" :loading="billLoading">同步账单</el-button>
          <el-button
            v-if="[0, 1, 2].includes(scope.row.billStatus) && [0].includes(scope.row.settlementWay) && scope.row.orderCount > 0"
            v-hasPermi="['trade:settle:invoice']" size="medium" type="text"
            @click="syncInvoice4YcrhInBillHandle(scope.row.id)" :loading="billLoading">同步发票</el-button>
          <el-button v-if="[0, 2].includes(scope.row.billStatus)" size="medium" type="text"
            v-hasPermi="['trade:bill:delete']" @click="deleteBill(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination v-show="total > 0" hide-on-single-page :total="total" :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize" @pagination="getList" />


    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="账单名称" prop="billName">
          <el-input v-model="form.billName" :maxlength="50" placeholder="请输入品牌名称" />
        </el-form-item>
        <el-form-item v-if="notSupplierUser()" v-hasPermi="['mall:supplier']" label="供应商" prop="supplierId">
          <supplier-select v-model="form.supplierId" placeholder="请选择供应商" />
        </el-form-item>
        <el-form-item label="结算方式" prop="settlementWay">
          <el-select v-model="form.settlementWay" clearable placeholder="请选择结算方式" style="width:100%">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.BILL_SETTLEMENT_WAY)" :key="dict.value"
              :label="dict.label" :value="parseInt(dict.value)" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <ExportAlert ref="exportAlert"></ExportAlert>
  </div>
</template>

<script>
import * as api from "@/api/mall/trade/bill.js"
import { BILL_STATUS_LIST } from '@/utils/mallUtil'
import SupplierSelect from "@/views/mall/product/spu/components/supplier-select";
import ExportAlert from '@/components/AsyncTaskAlert/export'
import { download } from "../../../../api/mall/download/exportTask";
import { SETTLEMENT_WAY_LIST } from "../../../../utils/mallUtil";
export default {
  name: "TradeBill",
  components: { SupplierSelect, ExportAlert },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      list: [],
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        searchValue: '',
        billId: '',
        orderNo: '',
        voucherNo: '',
        status: '',
        createTime: []
      },
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        billName: [{ required: true, message: "请输入账单名称", trigger: "blur" }],
        supplierId: [{ required: true, message: "请选择供应商", trigger: "change" }],
        settlementWay: [{ required: true, message: "请选择结算方式", trigger: "change" }],
      },
      billLoading: false,
      multipleSelection: [],
      exportLoading: false
    }
  },
  computed: {
    statusList() {
      return BILL_STATUS_LIST
    }
  },
  created() {
    this.getList();
  },
  methods: {
    handleSelectionChange(val) {
      console.log('handleSelectionChange', val)
      this.multipleSelection = val;
    },
    deleteBill(id) {
      this.$modal.confirm('是否确认删除账单吗?').then(function () {
        return api.deleteBill({ id: id });
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 查询列表 */
    async getList() {
      this.loading = true;
      const params = this.getParams();
      const res = await api.getBillPage(params);
      if (res.code === 0 && res.data) {
        this.list = res.data.list;
        this.total = Number(res.data.total);
      } else {
        this.list = []
        this.total = 0
      }
      this.loading = false;
    },
    getParams() {
      const params = {
        pageNo: this.queryParams.pageNo,
        pageSize: this.queryParams.pageSize
      }
      if (this.queryParams.createTime && this.queryParams.createTime.length > 0) {
        params.createTime = this.queryParams.createTime
      }
      if (this.queryParams.searchValue) {
        params.billName = this.queryParams.searchValue
      }
      if (this.queryParams.billId) {
        params.billId = this.queryParams.billId
      }
      if (this.queryParams.orderNo) {
        params.orderNo = this.queryParams.orderNo
      }
      if (this.queryParams.voucherNo) {
        params.voucherNo = this.queryParams.voucherNo
      }
      if (this.queryParams.supplierId) {
        params.supplierId = this.queryParams.supplierId
      }
      if (this.queryParams.settlementWay == 0 || this.queryParams.settlementWay == 1) {
        params.settlementWay = this.queryParams.settlementWay
      }
      if (this.queryParams.status !== '') {
        params.billStatus = this.queryParams.status
      }
      return params
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        searchValue: '',
        billId: '',
        orderNo: '',
        voucherNo: '',
        status: '',
        createTime: []
      }
      this.getList();
    },
    pushBillHandler(bill) {
      this.$modal.confirm('是否确认发起结算').then(() => {
        this.pushBill(bill.id, bill.billName, bill.supplierId)
      }).catch(() => {
      });
    },
    cancelBillHandler(billId) {
      this.$modal.confirm('是否确认取消结算').then(() => {
        this.cancelBill(billId);
      }).catch(() => {
      });
    },
    exportSettleHandle(billId) {
      this.$modal.confirm('是否确认导出结算单').then(() => {
        return api.exportSettle({
          billId: billId,
          exportPdf: false
        });
      }).then(response => {
        this.$download.excel(response, '账单' + billId + '结算单.xlsx');
      }).catch(() => { });
    },
    exportSettleArchiveHandle(billId) {
      this.$modal.confirm('是否确认导出账单结算单和发票').then(() => {
        return api.exportSettleArchive({
          billId: billId
        });
      }).then(response => {
        this.$modal.msgSuccess("导出任务" + billId + "已开始，请前往下载中心查看导出进度");
      }).catch(() => { });
    },
    syncOrder4YcrhInBillHandle(billId) {
      this.$modal.confirm('是否确认同步账单内订单到业财').then(() => {
        this.forceSyncOrder4YcrhInBill(billId);
      }).catch(() => {
      });
    },
    syncInvoice4YcrhInBillHandle(billId) {
      this.$modal.confirm('是否确认同步订单发票到业财').then(() => {
        this.syncInvoice4YcrhInBill(billId);
      }).catch(() => {
      });
    },
    syncOrderBillStatusHandle(billId) {
      this.$modal.confirm('是否确认同步业财账单状态').then(() => {
        this.syncOrderBillStatus(billId);
      }).catch(() => {
      });
    },
    splitBillHandler(arr) {
      this.$modal.confirm('超过500个订单的账单分拆成多个账单，是否确认拆分结算单').then(() => {
        this.splitBill(arr);
      }).catch(() => {
      });
    },
    async pushBill(billId, billName, supplierId) {
      this.billLoading = true
      try {
        const res = await api.getBillOrderStatus({ billId: billId })
        if (res.code === 0) {
          console.log(res)
          // 业财存在的订单
          const foundOrderList = res.data["foundOrderList"]
          // 业财不存在的订单
          const notFoundOrderList = res.data["notFoundOrderList"]
          // 已经结算的订单
          let settlementOrders = []
          if (foundOrderList && foundOrderList.length > 0) {
            const res = await api.pushBill(billId)
            if (res.code === 0) {
              this.$message.success('推送账单成功！')
              this.getList();
            } else {
              this.$message.error('推送账单失败！')
            }
          }
          else {
            this.$message.success('账单中订单都不存在，重推账单中所有订单')
            if (await api.forceSyncOrder4YcrhInBill(billId)) {
              const res = await api.pushBill(billId)
              if (res.code === 0) {
                this.$message.success('推送账单成功！')
                this.getList();
              } else {
                this.$message.error('推送账单失败！')
              }
            }
          }
        } else {
          this.$message.error('获取账单中订单状态失败！')
        }
        this.billLoading = false
      } catch (e) {
        this.billLoading = false
      }
    },
    async pushBill2(billId, billName, supplierId) {
      this.billLoading = true
      try {
        const res = await api.getBillOrderStatus({ billId: billId })
        if (res.code === 0) {
          console.log(res)
          // 业财存在的订单
          const foundOrderList = res.data["foundOrderList"]
          // 业财不存在的订单
          const notFoundOrderList = res.data["notFoundOrderList"]
          // 已经结算的订单
          let settlementOrders = []
          if (foundOrderList && foundOrderList.length > 0) {
            const res = await api.pushBill(billId)
            if (res.code === 0) {
              this.$message.success('推送账单成功！')
              this.getList();
            } else {
              this.$message.error('推送账单失败！')
            }
          }
          else {
            this.$message.success('账单中订单都不存在，重推账单中所有订单')
            if (await api.forceSyncOrder4YcrhInBill(billId)) {
              const res = await api.pushBill(billId)
              if (res.code === 0) {
                this.$message.success('推送账单成功！')
                this.getList();
              } else {
                this.$message.error('推送账单失败！')
              }
            }
          }
        } else {
          this.$message.error('获取账单中订单状态失败！')
        }
        this.billLoading = false
      } catch (e) {
        this.billLoading = false
      }
    },
    async cancelBill(billId) {
      this.billLoading = true
      try {
        const res = await api.cancelBill(billId)
        if (res.code === 0) {
          this.$message.success('取消账单成功！')
          this.getList();
        } else {
          this.$message.error('取消账单失败！')
        }
        this.billLoading = false
      } catch (e) {
        this.billLoading = false
      }
    },
    async forceSyncOrder4YcrhInBill(billId) {
      this.billLoading = true
      try {
        const res = await api.forceSyncOrder4YcrhInBill(billId)
        if (res.code === 0) {
          this.$message.success('同步账单内不存在的订单到业财成功！')
          this.getList();
        } else {
          this.$message.error('同步账单内不存在的订单到业财失败！')
        }
        this.billLoading = false
      } catch (e) {
        this.billLoading = false
      }
    },
    async syncInvoice4YcrhInBill(billId) {
      this.billLoading = true
      try {
        const res = await api.syncInvoice4YcrhInBill()
        if (res.code === 0) {
          this.$message.success('开始同步订单发票到业财！')
          this.getList();
        } else {
          this.$message.error('开始同步订单发票到业财失败！')
        }
        this.billLoading = false
      } catch (e) {
        this.billLoading = false
      }
    },
    async syncOrderBillStatus(billId) {
      this.billLoading = true
      try {
        const res = await api.syncOrderBillStatus(billId)
        if (res.code === 0) {
          this.$message.success('同步账单内订单状态成功！')
          this.getList();
        } else {
          this.$message.error('同步账单内订单状态失败！')
        }
        this.billLoading = false
      } catch (e) {
        this.billLoading = false
      }
    },
    async splitBill(arr) {
      this.billLoading = true
      try {
        const res = await api.splitBill(arr)
        if (res.code === 0) {
          this.$message.success('拆分账单成功！')
          this.getList();
        } else {
          this.$message.error('拆分账单失败！')
        }
        this.billLoading = false
      } catch (e) {
        this.billLoading = false
      }
    },
    async exportSettleArchive(billId) {
      const res = await api.exportSettleArchive(billId);
      if (res.code === 0) {
        this.$modal.msgSuccess("导出发票结算单任务已开始，请前往下载中心查看导出进度");
      } else {
        this.$modal.msgError("导出任务失败");
      }
    },
    goToDetail(row) {
      this.$router.push({ name: 'TradeBillDetail', query: { id: row.id } })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        billName: undefined,
        supplierId: undefined
      };
      this.resetForm("form");
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加账单";
      this.settlement_way = 0;
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning("请至少选择一项账单")
        return;
      }

      const billIds = this.multipleSelection.map((item) => item.id);
      this.$refs.exportAlert.init(api.exportBill, billIds, '账单导出')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      api.getBill({ id: id }).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改账单";
      });
    },
    /** 批量冻结/解冻 */
    changeSettlementWay(row) {
      this.$modal.confirm('是否确认' + (row.settlementWay == 0 ? '批量冻结' : '批量解冻') + '账单?').then(() => {
        api.updateOrderSettlementWay({
          id: row.id,
          supplierId: row.supplierId,
          settlementWay: row.settlementWay
        }).then(res => {
          if (res.code === 0) {
            this.$message.success(row.settlementWay == 0 ? '批量冻结成功！' : '批量解冻成功！')
          } else {
            this.$message.error(row.settlementWay == 0 ? '批量冻结失败！' : '批量解冻失败！')
          }
        });
      });
    },
    /** 提交按钮 */
    submitForm() {
      if (this.$store.getters.supplierId) {
        this.form.supplierId = this.$store.getters.supplierId
      }
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 新建的提交
        if (!this.form.id) {
          api.createBill(this.form).then(response => {
            this.$modal.msgSuccess("新建成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 修改的提交
        api.updateBill(this.form).then(response => {
          this.$modal.msgSuccess("修改成功");
          this.open = false;
          this.getList();
        });
      });
    },
  }
}
</script>

<style lang="scss" scoped></style>