<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" class="order-form" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="订单号">
        <el-input v-model="queryParams.orderNo" clearable></el-input>
      </el-form-item>
      <el-form-item label="用户名" prop="userName">
        <el-input v-model="queryParams.userName" clearable></el-input>
      </el-form-item>
      <el-form-item label="员工工号" prop="userNo">
        <el-input v-model="queryParams.userNo" clearable></el-input>
      </el-form-item>
      <el-form-item label="商品名称" prop="skuName">
        <el-input v-model="queryParams.skuName" clearable></el-input>
      </el-form-item>
      <el-form-item label="建档状态">
        <el-select v-model="queryParams.assetStatus" clearable>
          <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="notSupplierUser()" v-hasPermi="['mall:supplier']" label="供应商" prop="supplierId">
        <supplier-select v-model="queryParams.supplierId" placeholder="请选择供应商" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="建档处理时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" clearable
          :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
      <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <div>
          <el-button icon="el-icon-files" v-hasPermi="['trade:order-item-assets:export']" size="mini" type="primary" plain @click="exportList">导出</el-button>
        </div>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="list" border>
      <el-table-column label="订单号" align="center" prop="orderNo" width="180">
        <template v-slot="scope">
          <el-button type="text" @click="showDetail(scope.row)">{{scope.row.orderNo}}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="商品名称" align="center" prop="skuName" show-overflow-tooltip></el-table-column>
      <el-table-column label="单价" align="center" prop="price" width="120">
        <template v-slot="scope">
          <span>{{ formatMoney(scope.row.price) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数量" align="center" prop="quantity" width="80"></el-table-column>
      <el-table-column label="用户名" align="center" prop="userName" width="120" show-overflow-tooltip></el-table-column>
      <!-- <el-table-column label="员工工号" align="center" prop="userNo" width="120" show-overflow-tooltip></el-table-column> -->
      <el-table-column label="部门名称" align="center" prop="deptName" width="120" show-overflow-tooltip></el-table-column>
      <el-table-column label="供应商" align="center" prop="supplier" width="150" show-overflow-tooltip></el-table-column>
      <!-- <el-table-column label="购买时间" align="center" prop="buyDate" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.buyDate) }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="建档开始时间" align="center" prop="createTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="建档状态" align="center" prop="assetStatus"  width="130">
        <template v-slot="scope">
          <el-tag :type="scope.row.assetStatus | orderAssetStatusStyle">{{ scope.row.assetStatus | orderAssetStatusInfo }}</el-tag>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination v-show="total > 0" hide-on-single-page :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
      @pagination="getList" />


    <el-drawer
      title="查看详情"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="70%">
      <TradeOrderAssetsDetail ref="tradeOrderAssetsDetail" @on-update="getList" @on-hide="drawerVisible = false"/>
    </el-drawer>

    <ExportAlert ref="exportAlert"></ExportAlert>
  </div>
</template>
  
<script>
import * as api from "@/api/mall/trade/assets.js"
import { ORDER_ASSET_STATUS } from '@/utils/mallUtil'
import ExportAlert from '@/components/AsyncTaskAlert/export'
import TradeOrderAssetsDetail from '@/views/mall/trade/assets/detail'
import SupplierSelect from "@/views/mall/product/spu/components/supplier-select"
export default {
  name: "TradeOrderAssets",
  components: { ExportAlert, TradeOrderAssetsDetail, SupplierSelect },
  data () {
    return {
      // 遮罩层
      loading: false,
      // 详情抽屉
      drawerVisible: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      list: [],
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        userNo: null,
        userName: null,
        supplierId: null,
        orderNo: null,
        skuName: null,
        assetAcceptNo: null,
        assetNo: null,
        assetStatus: null,
        createTime: []
      }
    }
  },
  computed: {
    statusList() {
      return ORDER_ASSET_STATUS
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      let orderNo = this.$route.query.orderNo
      if(orderNo) {
        this.queryParams.orderNo = orderNo
      }
      this.handleQuery();
    },
    /** 查询列表 */
    async getList() {
      this.loading = true;
      const params = this.queryParams;
      const res = await api.getOrderAssetsPage(params);
      if (res.code === 0 && res.data) {
        this.list = res.data.list;
        this.total = Number(res.data.total);
      } else {
        this.list = []
        this.total = 0
      }
      this.loading = false;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        userName: null,
        userNo: null,
        orderNo: null,
        supplierId: null,
        skuName: null,
        assetAcceptNo: null,
        assetNo: null,
        assetStatus: null,
        createTime: []
      }
      this.getList();
    },
    showDetail(row) {
      this.drawerVisible = true
      this.$nextTick(() => {
        this.$refs.tradeOrderAssetsDetail.init(row.id)
      })
    },
    // 导出
    exportList() {
      const params = this.queryParams
      this.$refs.exportAlert.init(api.exportOrderAssetsList, params, '固资建档列表导出')
    },
  }
}
</script>
  
<style lang="scss" scoped>
</style>
  