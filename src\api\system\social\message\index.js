import request from '@/utils/request'

// 创建三方消息
export function createSocialMessage(data) {
  return request({
    url: '/system/social-message/create',
    method: 'post',
    data: data
  })
}

// 更新三方消息
export function updateSocialMessage(data) {
  return request({
    url: '/system/social-message/update',
    method: 'put',
    data: data
  })
}

// 删除三方消息
export function deleteSocialMessage(id) {
  return request({
    url: '/system/social-message/delete?id=' + id,
    method: 'delete'
  })
}

// 获得三方消息
export function getSocialMessage(id) {
  return request({
    url: '/system/social-message/get?id=' + id,
    method: 'get'
  })
}

// 获得三方消息分页
export function getSocialMessagePage(query) {
  return request({
    url: '/system/social-message/page',
    method: 'get',
    params: query
  })
}

// 导出三方消息 Excel
export function exportSocialMessageExcel(query) {
  return request({
    url: '/system/social-message/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

//三方消息重发
export function resendSocialMessage(id) {
  return request({
    url: '/system/social-message-send/resend',
    method: 'put',
    params: {  
      messageId: id  
    }
  })
}

