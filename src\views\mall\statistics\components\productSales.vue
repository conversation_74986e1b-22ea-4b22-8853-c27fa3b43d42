<template>
  <div class="app-container">
    <el-alert title="展示近30天的商品销量和销售额" type="info" style="margin-bottom: 24px;"></el-alert>
    <div id="product-echart" class="product-echart"></div>
    <div id="sales-echart" style="margin-top: 16px;" class="product-echart"></div>
  </div>
</template>

<script>
import * as api from "@/api/mall/statistics/commodity";
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  data() {
    return {
      chart: null,
      chart1: null
    };
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      api.getSalesAndRevenueInLastDays({
        days: 30
      }).then(response => {
        if (response.code === 0) {
          if (response.data && response.data.length > 0) {
            this.initChart(response.data)
          } else {
            if (this.chart) {
              this.chart.dispose()
              this.chart = null
              this.$message.info('当前范围内没有数据')
            }
            if (this.chart1) {
              this.chart1.dispose()
              this.chart1 = null
              this.$message.info('当前范围内没有数据')
            }
          }
        }
      })
    },
    initChart(data) {
      const chartDom = document.getElementById('product-echart');
      this.chart = echarts.init(chartDom, 'macarons')
      this.chart.setOption({
        legend: {
          data: ['销量']
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        grid: {
          left: '1%',
          right: '1%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.map(x => x.skuName || x.skuId)
        },
        yAxis: {
          type: 'value'
        },
        series: [
          { 
            name: '销量',
            type: 'bar',
            data: data.map(x => x.salesVolume)
          }
        ]
      })
      const chartDom1 = document.getElementById('sales-echart');
      this.chart1 = echarts.init(chartDom1, 'macarons')
      this.chart1.setOption({
        legend: {
          data: ['销售额']
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        grid: {
          left: '1%',
          right: '1%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.map(x => x.skuName || x.skuId)
        },
        yAxis: {
          type: 'value'
        },
        series: [
          { 
            name: '销售额',
            type: 'bar',
            data: data.map(x => x.salesRevenue),
            itemStyle: {  
              color: '#ffa500'
            }
          }
        ]
      })
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    if (this.chart1) {
      this.chart1.dispose()
      this.chart1 = null
    }
  }
};
</script>

<style lang="scss" scoped>
.product-echart {
  width: 100%;
  height: 400px;
}
</style>