<template>
  <div class="app-container">
    <el-tabs class="form" v-model="tabType" style="float:none;margin-bottom: 24px;" @tab-click="handleClick">
      <el-tab-pane label="待结算订单" name="0">
      </el-tab-pane>
      <el-tab-pane label="已结算订单" name="1">
      </el-tab-pane>
      <el-tab-pane label="线下结算订单" name="2">
      </el-tab-pane>
    </el-tabs>

    <el-form :model="queryParams" ref="queryForm" class="order-form" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="搜索方式">
        <el-input v-model="queryParams.searchValue" clearable style="width:300px">
          <el-select v-model="queryParams.searchType" slot="prepend" style="width: 100px">
            <el-option v-for="dict in searchTypes" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-input>
      </el-form-item>
      <el-form-item label="发票状态">
        <el-select v-model="queryParams.invoiceStatus" clearable>
          <el-option v-for="item in invoiceStatusList" :key="item.label" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="tabType != '2'" label="固资状态">
        <el-select v-model="queryParams.assetStatus" clearable>
          <el-option v-for="item in assetStatusList" :key="item.label" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="支付方式" prop="paymentMethod" label-width="70px" v-if="payMethodOpts.length">
        <el-select v-model="queryParams.paymentMethod" clearable>
          <el-option v-for="dict in payMethodOpts"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item v-if="notSupplierUser()" v-hasPermi="['mall:supplier']" label="供应商">
        <supplier-select v-model="queryParams.supplierId" placeholder="请选择供应商" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime" v-if="false">
        <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <br>
      <el-form-item label="完成时间" prop="finishTime">
        <el-date-picker v-model="queryParams.finishTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
      <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <div>
          <el-button icon="el-icon-files" v-hasPermi="['trade:settle:export']" size="small" type="primary" :loading="exportLoading" @click="exportOrder">订单导出</el-button>
          <el-button icon="el-icon-document-checked" v-hasPermi="['trade:settle:update']" size="small" @click="settleOrderBatch" v-if="tabType == 0">批量结算</el-button>
          <el-button icon="el-icon-files" size="small" v-hasPermi="['trade:settle:invoice']" :loading="invoiceLoading1" @click="invoiceApplyBatch(0)">订单开票</el-button>
          <el-button icon="el-icon-download" size="small" v-hasPermi="['trade:settle:invoice']" :loading="invoiceLoading2" @click="invoiceApplyBatch(1)">发票导出</el-button>
          <el-button icon="el-icon-upload" v-if="false" v-hasPermi="['trade:settle:invoice']" size="small" :loading="uploadLoding" @click="invoiceUploadBatch">发票上传</el-button>
          <el-button icon="el-icon-edit" size="small" v-hasPermi="['trade:settle:invoice']" :loading="checkLoding" @click="invoiceCheckBatch" v-if="false">发票验真</el-button>
          <el-button icon="el-icon-edit" size="small" v-if="notSupplierUser() && isAssetSwitch && tabType != '2'" v-hasPermi="['trade:order-item-assets:update']" :loading="assetsLoading" @click="assetCheckBatch">固资建档</el-button>
        </div>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      ref="table"
      class="settlement-table"
      v-loading="loading"
      :data="list"
      row-key="no"
      default-expand-all
      :row-class-name="cellClass"
      @selection-change="handleSelectionChange"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column label="商品" width="550">
        <template slot-scope="{ row }">
          <span v-if="row.children != undefined" class="orderCodeNum">
            订单编号: {{ row.orderNo || '--' }}
          </span>
          <div v-else class="goodsCard">
            <div class="goodsCard-item" v-for="item in row.productList" :key="item.skuId">
              <div class="goodsCard-item-img">
                <img :src="item.picUrl" alt="" height="35" />
              </div>
              <div class="goodsCard-item-info">
                <div class="goodsCard-item-info-name text-ellipsis" :title="item.skuName">{{ item.skuName || '--' }}</div>
                <div class="goodsCard-item-info-code text-ellipsis">{{ item.skuId || '--' }} <span v-if="item.skuInnerId && row.supplierType !== 20"> / {{ item.skuInnerId }}</span></div>
              </div>
              <div v-if="item.isAsset"> 
                <el-tag :type="item.assetStatus | orderAssetStatusStyle" size="small">固资 <span> > {{item.assetStatus | orderAssetStatusInfo}}</span></el-tag>
              </div>
              <OrderAcceptInfo :orderItem="item" :orderStatus="row.status"></OrderAcceptInfo>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="单价">
        <template slot-scope="{ row }">
          <div
            class="unit-price"
            v-for="item in row.productList"
            :key="item.skuId"
          >
            <span class="unit-price-number">{{ formatMoney(item.skuPrice) }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="数量" width="120px">
        <template slot-scope="{ row }">
          <div v-for="item in row.productList" :key="item.skuId">
            <div class="unit-price" >x{{ item.count }}
              <div style="display:inline-block;margin-left:5px;" v-if="item.afterSaleStatus">
                <dict-tag :type="DICT_TYPE.TRADE_ORDER_ITEM_AFTER_SALE_STATUS" :value="item.afterSaleStatus" />
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="下单人" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.children == undefined">
            <div>{{ row.userName || '--' }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="商品金额" align="center" v-if="false">
        <template slot-scope="{ row }">
          <div v-if="row.children == undefined">
            <div>{{ formatMoney(row.productPrice) }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="运费金额" align="center" v-if="false">
        <template slot-scope="{ row }">
          <div v-if="row.children == undefined">
            <div>{{ formatMoney(row.deliveryPrice) }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="订单总金额" align="center" width="120">
        <template slot-scope="{ row }">
          <div v-if="row.children == undefined">
            <div>总{{ formatMoney(row.orderTotalPrice) }}</div>
            <div v-if="row.refundPrice" style="color:#ff00008c;">退{{ formatMoney(row.refundPrice) }}</div>
            <dict-tag :type="DICT_TYPE.MALL_PAYMENT_METHOD" :value="row.paymentMethod || 1" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="下单时间" align="center" width="170" v-if="false">
        <template slot-scope="{ row }">
          <div v-if="row.children == undefined">
            <div>{{ parseTime(row.createTime) }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="完成时间" align="center" width="170">
        <template slot-scope="{ row }">
          <div v-if="row.children == undefined">
            <div>{{ parseTime(row.finishTime) }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="供应商" align="center" width="120">
        <template slot-scope="{ row }">
          <div>{{ row.supplierName || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="开票状态" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.children == undefined && row.tradeOrderInvoice">
            <el-tag :type="row.tradeOrderInvoice.invoiceStatus | invoiceStatusStyle">{{ row.tradeOrderInvoice.invoiceStatusName }}</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="发票上传" align="center" v-if="false">
        <template slot-scope="{ row }">
          <div v-if="row.children == undefined && row.tradeOrderInvoice">
            <el-tag :type="row.tradeOrderInvoice.voucherUploadStatus ? 'success' : 'info'">{{ row.tradeOrderInvoice.voucherUploadStatus ? '已上传' : '未上传' }}</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="发票验真" align="center" v-if="false">
        <template slot-scope="{ row }">
          <div v-if="row.children == undefined && row.tradeOrderInvoice">
            <el-tag :type="row.tradeOrderInvoice.invoiceCheckStatus ? 'success' : 'info'">{{ row.tradeOrderInvoice.invoiceCheckStatus ? '已验真' : '未验真' }}</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="固资建档" align="center" v-if="isAssetSwitch && tabType != '2'">
        <template slot-scope="{ row }">
          <div v-if="!row.children">
            <el-tag :type="row.assetStatus | orderAssetStatusStyle"> {{ row.assetStatus | orderAssetStatusInfo }}
              <el-popover
                v-if="row.assetStatus === 0 && row.assetMemo"
                title="接口失败原因"
                width="200"
                trigger="click"
                :content="row.assetMemo">
                <i slot="reference" class="el-icon-warning"></i>
              </el-popover>
            </el-tag> 
            <el-button v-if="notSupplierUser() && row.assetStatus !== 0" size="medium" type="text" v-hasPermi="['trade:order-item-assets:update']" @click="resetOrderAssets(row)">回滚</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <template v-if="scope.row.children == undefined">
            <el-button v-if="scope.row.settleStatus === 0" size="medium" type="text" v-hasPermi="['trade:settle:update']" @click="settleOrder([scope.row.id])">结算</el-button>
            <el-button size="medium" type="text" @click="toOrderDetail(scope.row)" v-hasPermi="['trade:order:query']">查看订单</el-button>
            <el-button v-if="!scope.row.tradeOrderInvoice.fileUrl && scope.row.tradeOrderInvoice.invoiceStatus !== 1" v-hasPermi="['trade:settle:invoice']" size="medium" type="text" @click="invoiceApply([scope.row.id])">申请开票</el-button>
            <el-button size="medium" type="text" @click="uploadInvoice(scope.row)" v-hasPermi="['trade:settle:invoice']">上传发票</el-button>
            <el-button v-if="scope.row.tradeOrderInvoice.invoiceStatus === 2" size="medium" v-hasPermi="['trade:settle:query']" type="text" @click="downloadInvoice(scope.row.tradeOrderInvoice.fileUrl)">查看发票</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination v-show="total > 0" :page-sizes="[20, 50, 100, 200, 300]" hide-on-single-page :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <ExportAlert ref="exportAlert"></ExportAlert>  

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="订单编号" prop="orderNo">
          <div>{{ form.orderNo }}</div>
        </el-form-item>
        <el-form-item label="发票金额" prop="invoicePrice">
          <div>￥{{ form.invoicePrice }}</div>
        </el-form-item>
        <el-form-item label="发票日期" prop="invoiceDate">
          <el-date-picker
            v-model="form.invoiceDate"
            type="date"
            placeholder="选择日期时间"
            default-time="">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="发票号码" prop="invoiceId">
          <el-input v-model="form.invoiceId" placeholder="请输入发票号"/>
        </el-form-item>
        <el-form-item label="发票链接" prop="invoiceUrl">
          <fileUpload v-model="form.fileUrl" :limit="1" :fileSize="2" :fileType="['pdf']" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import * as api from "@/api/mall/trade/order.js"
import { INVOICE_STATUS_LIST, ORDER_ASSET_STATUS } from '@/utils/mallUtil'
import SupplierSelect from "@/views/mall/product/spu/components/supplier-select";
import ExportAlert from '@/components/AsyncTaskAlert/export'
import { configMixins } from '@/views/mall/config/components/configMixin.js'
import OrderAcceptInfo from '@/views/mall/trade/order/components/acceptInfo'
import FileUpload from '@/components/FileUpload'
import Decimal from 'decimal.js';
import { ref } from "vue";
export default {
  name: "TradeOrderSettlement",
  mixins: [ configMixins ],
  components: {
    SupplierSelect, ExportAlert, OrderAcceptInfo, FileUpload
  },
  data () {
    return {
      exportLoading: false,
      tabType: '0',
      // 遮罩层
      loading: true,
      // 开票加载状态
      invoiceLoading1: false,
      // 发票导出加载状态
      invoiceLoading2: false,
      // 上传加载状态
      uploadLoding: false,
      // 验真加载状态
      checkLoding: false,
      // 固资建档提交状态
      assetsLoading: false,
      // 导出遮罩层
      exportLoading: false,
      // 发票详情对话框
      open: false,
      // 显示搜索条件
      showSearch: true,
      statusList: [{
        value: 0,
        label: '待结算'
      }, {
        value: 1,
        label: '已结算'
      }],
      // 总条数
      total: 0,
      // 交易售后列表
      list: [],
      queryParams: {
        pageNo: 1,
        pageSize: 20,
        searchType: 'orderNo',
        searchValue: '',
        invoiceStatus: null,
        paymentMethod: null,
        assetStatus: null,
        supplierId: null,
        createTime: [],
        finishTime: []
      },
      searchTypes: [
        { label: '订单号', value: 'orderNo' },
        // { label: '用户ID', value: 'userId' },
        // { label: '用户昵称', value: 'userNickname' },
        { label: '下单人姓名', value: 'userName' },
        { label: '收货人姓名', value: 'receiverName' },
        { label: '收货人手机号', value: 'receiverMobile' },
      ],
      multipleSelection: [],
      invoiceStatusList: INVOICE_STATUS_LIST.filter(item => item.value < 4),
      assetStatusList: ORDER_ASSET_STATUS,
      title: '上传发票',
      form: {
        orderNo: null,
        id: null,
        supplierId: null,
        invoiceId: null,
        invoicePrice: null,
        invoiceDate: null,
        fileUrl: null,
      },
      // 表单校验
      rules: {
        invoiceId: [{ required: true, message: "发票号码不能为空", trigger: "blur" }],
        invoiceDate: [{ required: true, message: "发票日期不能为空", trigger: "blur" }],
        fileUrl: [{ required: true, message: "发票不能为空", trigger: "blur" }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleClick() {
      this.queryParams.pageNo = 1
      this.getList();
    },
    /** 查询列表 */
    async getList() {
      this.loading = true;
      const params = this.getParams();
      const res = await api.queryOrderSettlePage(params);
      if (res.code === 0 && res.data) {
        const list = res.data.list;
        res.data.list.forEach((item, index) => {
          item.children = [
            {
              id: item.orderId,
              status: item.status,
              acceptStatus: item.acceptStatus,
              no: item.orderNo + index,
              supplierId: item.supplierId,
              trueNo: item.orderNo,
              productList: item.items || [],
              receiverName: item.receiverName,
              productPrice: item.productPrice,
              orderPrice: item.orderPrice,
              refundPrice: item.refundPrice,
              deliveryPrice: item.deliveryPrice,
              orderTotalPrice: item.orderTotalPrice,
              refundPrice: item.refundPrice,
              paymentMethod: item.paymentMethod,
              settleStatusDesc: item.settleStatusDesc,
              userName: item.userName,
              createTime: item.createTime,
              updateTime: item.updateTime,
              finishTime: item.finishTime,
              receiverMobile: item.receiverMobile,
              assetStatus: item.assetStatus,
              assetMemo: item.assetMemo,
              invoiceStatus: item.invoiceStatus,
              tradeOrderInvoice: item.tradeOrderInvoice
            }
          ]
        })
        this.list = list
        this.total = Number(res.data.total);
        this.$nextTick(() => {
          this.$refs.table.doLayout()
        })
      } else {
        this.list = []
        this.total = 0
      }
      this.loading = false;
    },
    cellClass({ row }) {
      if (!row.hasOwnProperty('children')) {
        return 'disableheadselection'
      }
      return 'orderCode'
    },
    getParams() {
      const params = {
        offlineSettlement: this.tabType ==  '2',
        pageNo: this.queryParams.pageNo,
        pageSize: this.queryParams.pageSize,
        createTime: this.queryParams.createTime,
        finishTime: this.queryParams.finishTime,
        supplierId: this.queryParams.supplierId,
        assetStatus: this.queryParams.assetStatus,
        invoiceStatus: this.queryParams.invoiceStatus,
        paymentMethod: this.queryParams.paymentMethod
      }
      if(this.tabType == '2'){
        params.offlineSettlement = true
      }
      else {
        params.offlineSettlement = false
        params.settleStatus = this.tabType
      }
      if (this.queryParams.searchValue) {
        this.$set(params, this.queryParams.searchType, this.queryParams.searchValue)
      }
      if (params.createTime && params.createTime.length > 0) {
        params.createTime = params.createTime.join(',')
      }
      if (params.finishTime && params.finishTime.length > 0) {
        params.finishTime = params.finishTime.join(',')
      }
      return params
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 20,
        searchType: 'orderNo',
        searchValue: '',
        invoiceStatus: null,
        assetStatus: null,
        supplierId: null,
        createTime: [],
        finishTime: []
      }
      this.handleQuery();
    },
    // 结算
    async settleOrder(arr) {
      const res = await api.settleByOrderIds(arr)
      if (res.code === 0) {
        this.$message.success('订单结算成功！')
        this.queryParams.pageNo = 1
        this.getList()
      } else {
        this.$message.error('订单结算失败！')
      }
    },
    // 批量结算
    settleOrderBatch() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning("请至少选择一项订单")
        return;
      }
      this.settleOrder(this.multipleSelection.map(x => {
        return x.orderId
      }));
    },
    handleSelectionChange(val) {
      console.log('handleSelectionChange', val)
        this.multipleSelection = val;
    },
    // 批量开发票
    invoiceApplyBatch(type) {
      if (this.multipleSelection.length === 0) {
        this.$message.warning("请至少选择一项订单")
        return;
      }
      if (type === 0) {
        this.invoiceApply(this.multipleSelection.map(x => {
          return x.orderId
        }));
      } else {
        this.invoiceExport(this.multipleSelection.map(x => {
          return x.orderId
        }));
      }
    },
    // 批量固资建档
    assetCheckBatch() {
      if (!this.multipleSelection.length) {
        this.$message.warning("请至少选择一项订单")
        return;
      }
      let objs = this.multipleSelection.filter(item => item?.assetStatus === 0)
      if(!objs.length) {
        this.$message.warning("请选择建档待处理的订单")
        return;
      }
      let invoicePendingLength = objs.filter(obj => obj.invoiceStatus !== 2).length
      if(invoicePendingLength) {
        this.$message.warning("请选择开票已完成的订单")
        return;
      }

      let orderIds = objs.map(item => item.orderId) || []
      this.assetsLoading = true
      orderIds.forEach(orderId => {
        api.createOrderAssets({orderId: orderId}).then(res => {
          if(res.code === 0) {
            if(!res.data) {
              this.$message.error(`订单:${orderId}固资建档提交失败`)
            } else {
              this.$message.success(`订单:${orderId}固资建档提交成功`)
            }
            this.getList();
          }
        })
      })
      setTimeout(() => {
        this.assetsLoading = false
      }, 3000)
    },
    // 订单固资状态回滚
    async resetOrderAssets(row) {
      this.$modal.confirm('回滚固资状态将清空固资信息，确认操作吗?').then(async () => {
        let params = {
        orderId: row.id
        }
        const res = await api.resetOrderAssets(params)
        if (res.code === 0) {
          this.$message.success('操作成功！')
          this.getList()
        } else {
          this.$message.error('操作失败！')
        }
      }).catch(() => {
      })
    },
    // 批量上传，可以先不用
    invoiceUploadBatch() {
      if (!this.multipleSelection.length) {
        this.$message.warning("请至少选择一项订单")
        return;
      }
      let objs = this.multipleSelection.filter(item => item?.tradeOrderInvoice?.invoiceStatus === 2)
      if(!objs.length) {
        this.$message.warning("请选择开票完成的订单")
        return;
      }
      let orderIds = objs.map(item => item.orderId)
      this.uploadLoding = true
      his.$message.info("此操作比较耗时，请耐心等待")
      api.uploadVoucher(orderIds).then(res => {
        if(res.code === 0) {
          if(res.data) {
            this.$message.success("发票上传成功")
          } else {
            this.$message.warning("发票上传失败")
          }
          this.getList();
        }
      }).finally(() => {
        this.uploadLoding = false
      })
    },
    // 批量验真
    invoiceCheckBatch() {
      if (!this.multipleSelection.length) {
        this.$message.warning("请至少选择一项订单")
        return;
      }
      let objs = this.multipleSelection.filter(item => [2,6].includes(item?.tradeOrderInvoice?.invoiceStatus))
      if(!objs.length) {
        this.$message.warning("请选择开票完成或验真失败的订单")
        return;
      }
      let orderIds = objs.map(item => item.orderId)
      this.checkLoding = true
      this.$message.info("此操作比较耗时，请耐心等待")
      api.checkInvoice(orderIds).then(res => {
        if(res.code === 0) {
          if(res.data) {
            this.$message.success("发票验真成功")
          } else {
            this.$message.warning("发票验真失败")
          }
          this.getList();
        }
      }).finally(() => {
        this.checkLoding = false
      })
    },
    invoiceApply(arr) {
      this.invoiceLoading1 = true
      api.invoiceApply({
        orderIdList: arr,
        invoiceDate: this.parseTime(new Date().getTime())
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('开票申请成功！')
          this.handleQuery();
        } else {
          this.$message.error('开票申请失败！')
        }
      }).finally(() => {
        this.invoiceLoading1 = false
      })
    },
    // 发票导出
    invoiceExport(arr) {
      this.invoiceLoading2 = true
      api.downloadInvoice({
        orderIdList: arr
      }).then(res => {
        this.$download.download0(res, '发票', res.type);
      }).finally(() => {
        this.invoiceLoading2 = false
      })
    },
    // 订单导出
    exportOrder() {
      const params = this.getParams()
      this.$refs.exportAlert.init(api.exportSettleOrder, params, '结算订单导出')
    },
    toOrderDetail(row) {
      this.$router.push({ name: 'TradeOrderDetail', query: { id: row.id } })
    },
    downloadInvoice(url) {
      window.open(url, '_blank')
    },
    /** 重置 */
    reset() {
      this.open = false;
      this.form = {
        orderId: null,
        orderNo: null,
        supplierId: null,
        invoiceId: null,
        totalBatchInvoiceAmount: null,
        invoicePrice: null,
        invoiceDate: null,
        fileUrl: null,
      }
    },
    /** 修改按钮操作 */
    uploadInvoice(row) {
      this.reset();
      var realPrice = new Decimal(row.orderPrice || 0).sub(new Decimal(row.refundPrice || 0));
      this.form = {
        orderId: row.id,
        orderNo: row.no,
        supplierId: row.supplierId,
        invoiceId: row.tradeOrderInvoice.invoiceId,
        invoiceDate: row.tradeOrderInvoice.invoiceDate,
        totalBatchInvoiceAmount: realPrice.toFixed(2),
        invoicePrice: realPrice.toFixed(2),
        fileUrl: row.tradeOrderInvoice.fileUrl
      }
      this.open = true;
      this.title = "上传发票";
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });
    },
    /** 提交按钮操作 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          const data = {
            orderId: this.form.orderId,
            orderNo: this.form.orderNo,
            supplierId: this.form.supplierId,
            invoiceId: this.form.invoiceId,
            invoiceDate: this.parseTime(this.form.invoiceDate),
            totalBatchInvoiceAmount: this.form.totalBatchInvoiceAmount,
            invoicePrice: this.form.invoicePrice,
            fileUrl: this.form.fileUrl,
            canInvoice: true,
          }
          api.invoiceUpdate(data).then(res => {
            if (res.code === 0) {
              this.$message.success('发票上传成功！')
              this.handleQuery();
            } else {
              this.$message.error('发票上传失败！')
            }
            this.getList();
          });
          this.open = false;
          this.reset();
        }
      })
    },
    /** 取消按钮操作 */
    cancel() {
      this.open = false;
      this.reset();
    },
  }
}
</script>

<style lang="scss" scoped>
.settlement-table {
  :deep(.el-table__expand-icon) {
    display: none;
  }
  :deep(.disableheadselection) {
    color: #8c8c8c;
    > td:first-child {
      > .cell {
        > .el-checkbox {
          display: none;
        }
      }
    }
  }
  :deep(.orderCode) {
    color: #333333;
    background-color: #f2f4f7;
  }
  .orderCodeNum {
    font-size: 12px;
    font-weight: bold;
    color: #333333;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .goodsCard {
    display: block;
    .goodsCard-item {
      display: flex;
      width: 100%;
      + .goodsCard-item {
        margin-top: 20px;
      }
      .goodsCard-item-img {
        position: relative;
      }
      .goodsCard-item-info {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-left: 8px;
        font-size: 12px;
        line-height: 20px;
        max-width: 300px;
        .goodsCard-item-info-name {
          max-width: 300px;
          color: #1d2129;
        }
        .goodsCard-item-info-code {
          color: #86909c;
        }
      }
    }
  }
  .unit-price {
    display: flex;
    align-items: center;
    height: 38px;
    + .unit-price {
      margin-top: 20px;
    }
    .unit-price-number {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
