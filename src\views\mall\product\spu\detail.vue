<template>
  <div class="app-container" v-loading="loading">
    <el-tabs v-model="activeName" class="tabs">
      <el-tab-pane label="基础信息" name="tab1">
        <el-descriptions class="des-wrap" :column="1" border>
          <el-descriptions-item label="商品名称">{{ detailData.spuName }}</el-descriptions-item>
          <el-descriptions-item label="供应商">{{ detailData.supplierName }}</el-descriptions-item>
          <el-descriptions-item label="商品品牌">{{ detailData.brandName }}</el-descriptions-item>
          <el-descriptions-item label="商品分类">{{ detailData.fullCategoryName }}</el-descriptions-item>
          <el-descriptions-item v-if="notSupplierUser()" label="促销语">{{ detailData.sellPoint || '--' }}</el-descriptions-item>
          <el-descriptions-item v-if="notSupplierUser()" label="商品排序">{{ detailData.sort }}</el-descriptions-item>
          <el-descriptions-item label="单位">
            <el-tag type="primary">{{ detailData.unit || '--' }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="上架状态">
            <el-tag :type="detailData.status === 0 ? 'default' : 'success'">{{detailData.status === 0 ? '下架' : '上架'}}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="商品主图">
            <el-image style="width: 150px;" :src="detailData.picUrl" fit="contain"></el-image>
          </el-descriptions-item>
          <el-descriptions-item label="商品图书">
            <el-image v-for="(imgUrl, index) in detailData.sliderPicUrls" :key="index" style="width: 150px;" :src="imgUrl" fit="contain"></el-image>
          </el-descriptions-item>
        </el-descriptions>

      </el-tab-pane>
      <el-tab-pane v-if="notSupplierUser()" label="商品属性" name="tab2">
        <el-descriptions class="des-wrap" title="" :column="2" border>
          <el-descriptions-item v-for="(spec, index) in detailData.spuSpecValueList" :key="index" :label="spec.specName">{{ spec.specValue }}</el-descriptions-item>
        </el-descriptions>
        <el-empty v-if="!detailData.spuSpecValueList || !detailData.spuSpecValueList.length"></el-empty>
      </el-tab-pane>
      <el-tab-pane label="商品SKU" name="tab3">
        <el-table :data="skus" :border="true" style="width: 100%">
          <template>
            <el-table-column :key="index" v-for="(item, index) in dynamicSpec" :label="item" width="120">
              <template v-slot="scope">
                <span>{{ getSpecValue(item, scope.row.specValueList) }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column label="平台SKU" key="92" width="220">
            <template v-slot="scope">
              <span>{{ scope.row.id }}</span>
            </template>
          </el-table-column>
          <el-table-column label="三方SKU" key="921" v-if="detailData.supplierType !== 20">
            <template v-slot="scope">
              <el-input :value="scope.row.skuInnerId" disabled/>
            </template>
          </el-table-column>
          <el-table-column label="市场价(元)" key="93">
            <template v-slot="scope">
              <el-input :value="scope.row.marketPrice" disabled style="width:100px;"/>
            </template>
          </el-table-column>
          <el-table-column label="销售价(元)"  key="94">
            <template v-slot="scope">
              <el-input :value="scope.row.salePrice" disabled style="width:100px;" />
            </template>
          </el-table-column>
          <el-table-column label="最低起售数量"  key="941">
            <template v-slot="scope">
              <el-input :value="scope.row.lowestBuy" disabled style="width:100px;" />
            </template>
          </el-table-column>
          <el-table-column label="库存" key="9">
            <template v-slot="scope">
              <el-input :value="scope.row.stock" disabled style="width:100px;" />
            </template>
          </el-table-column>
          <el-table-column label="预警库存" key="97">
            <template v-slot="scope">
              <el-input :value="scope.row.warnStock" disabled style="width:100px;" />
            </template>
          </el-table-column>
          <el-table-column label="状态" key="99" width="120">
            <template v-slot="scope">
              <el-tag :type="scope.row.status === 0 ? 'info' : 'success'">{{ scope.row.status == 0 ? '已下架' : '已上架' }}</el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="商品PC端详情" name="tab4">
        <div class="vhtml-wrapper" v-html="detailData.description"></div>
        <el-empty v-if="!detailData.description"></el-empty>
      </el-tab-pane>
      <el-tab-pane label="商品H5端详情" name="tab42">
        <div class="vhtml-wrapper" v-html="detailData.descriptionH5"></div>
        <el-empty v-if="!detailData.descriptionH5"></el-empty>
      </el-tab-pane>
      <el-tab-pane v-if="notSupplierUser()" v-hasPermi="['product:operate-log:query']" label="操作日志" name="tab5">
        <div v-if="operateLogList && operateLogList.length">
          <el-timeline>
            <el-timeline-item :timestamp="parseTime(logItem.createTime)" placement="top" v-for="(logItem, index) in operateLogList" :key="index">
              <el-card>
                <p>{{ logItem.userName || logItem.userId }} {{ logItem.content }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
        <el-empty v-else></el-empty>
      </el-tab-pane>
    </el-tabs>

  </div>
</template>

<script>
import { getSpuDetail, getOperateLogPage } from "@/api/mall/product/spu.js"
export default {
  name: "ProductSpuDetail",
  data() {
    return {
      activeName: 'tab1',
      detailData: {},
      loading: false,
      dynamicSpec: [],
      skus: [],
      operateLogList: []
    };
  },
  created() {
    let id = this.$route.params.spuId || this.$route.query.spuId
    if(id) {
      this.loadSpuDetail(id)
    }
  },
  methods: {
    async loadOperateLogPage() {
      const res = await getOperateLogPage({
        pageNo: 1,
        pageSize: 100,
        spuId: this.$route.query.spuId
      })
      this.operateLogList = []
      if (res.code === 0) {
        this.operateLogList = res.data.list || []
      }
    },
    async loadSpuDetail(id) {
      this.loading = true
      let res = await getSpuDetail(id)
      if (res.code === 0) {
        this.detailData = res.data
        this.skus = res.data.skus || []

        let specDic = []
        this.skus.forEach((sku, index) => {
          sku.specValueList.map(specValue => {
            if(!specDic.includes(specValue.specName)) {
              specDic.push(specValue.specName)
            }
          })
        })
        this.dynamicSpec = specDic
        if(this.$store.getters.permissions.includes('product:operate-log:query')) {
          this.loadOperateLogPage()
        }
      }
      this.loading = false
    },
    getSpecValue(specName, specValueList) {
      let sv = specValueList.find(item => item.specName === specName)
      if(sv) return sv.specValue
      return ''
    },
    typeFormatter(row, column) {
      const typeList = ['创建', '修改', '删除', '已确认', '已发货', '已送达', '已签收', '已完成', '已取消']
      return typeList[row.operateType]
    }
  },
};
</script>

<style lang="scss">
.app-container{
  padding: 20px;
  min-height: 800px;
  width: 100%;
  .des-wrap {
    margin-bottom: 24px;
  }
  .vhtml-wrapper {
    padding: 2px;
    box-sizing: border-box;
    img {
      max-width: 100%;
      object-fit: fill!important;
    }
  }
}
</style>
