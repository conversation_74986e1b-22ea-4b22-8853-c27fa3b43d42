<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="规则名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入规则名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="生效时间" prop="effectTime" v-if="false">
        <el-date-picker v-model="queryParams.effectTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item label="项目范围" prop="projectScopeType" v-if="false">
        <el-select v-model="queryParams.projectScopeType" placeholder="请选择项目范围" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MALL_PROJECT_SCOPE_TYPE)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="项目范围" prop="projectScopeContent">
        <el-input v-model="queryParams.projectScopeContent" placeholder="请输入项目范围内容" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="用户范围" prop="userScopeContent">
        <el-input v-model="queryParams.userScopeContent" placeholder="请输入项目范围内容" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="商品范围" prop="productScopeContent">
        <el-input v-model="queryParams.productScopeContent" placeholder="请输入项目范围内容" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="规则状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime" v-if="false">
        <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-alert
      style="margin:0 0 10px;padding:10px;"
      :closable="false"
      title="规则使用说明 ：规则使用优先级 禁止使用规则 > 条件使用规则 > 自由使用规则; 如果规则为空，则无限制；"
      description="匹配过程：先通过禁止使用规则将匹配的项目置为不可用，其次通过条件使用规则找到可用的项目并置为可用，前面都未匹配到的项目经过自由使用规则进行匹配，如有匹配则状态置为可用，否则为不可用。"
      type="info">
    </el-alert>
    <!-- 操作工具栏 -->
    <div class="flex-between" style="z-index:10;">
      <div style="width:80%;">
        <el-tabs v-model="activeRuleType" type="card">
          <el-tab-pane label="自由使用规则-低" name="1"></el-tab-pane>
          <el-tab-pane label="禁止使用规则-高" name="2"></el-tab-pane>
          <el-tab-pane label="条件使用规则-中" name="3"></el-tab-pane>
        </el-tabs>
      </div>
      <div style="width:260px;">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['mall:project-rule:create']" style="margin:0 10px;">新增</el-button>
        <el-button type="primary" plain icon="el-icon-orange" size="mini" @click="handleTest" v-hasPermi="['mall:project-rule:create']" style="margin:0 10px;">测试</el-button>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </div>
    </div>

    <!-- 列表 -->
    <el-table v-if="!loading" :data="list">
      <el-table-column label="规则名称" align="center" prop="name" width="160" show-overflow-tooltip/>
      <el-table-column label="生效时段" align="center" prop="effectTime" width="180" show-overflow-tooltip>
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.effectTime, '{y}-{m}-{d}') }} 至 {{ parseTime(scope.row.expireTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="指定项目" align="center" prop="projectScopeContent">
        <template v-slot="scope">
          <span>{{ ['','项目编号：','表达式：'][scope.row.projectScopeType]}} {{ scope.row.projectScopeType !== 0 ? scope.row.projectScopeContent : '全部' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户范围" align="center" prop="userScopeContent" v-if="['3'].includes(activeRuleType)">
        <template v-slot="scope">
          <span>{{ ['','工号：','部门：'][scope.row.userScopeType]}} {{ scope.row.userScopeType !== 0 ? scope.row.userScopeContent : '全部' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="指定商品" align="center" prop="productScopeContent" v-if="['3'].includes(activeRuleType)">
        <template v-slot="scope">
          <span>{{ ['全部','商品分类','SKU：'][scope.row.productScopeType]}} {{ scope.row.productScopeType == 2 ? scope.row.productScopeContent : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="120">
        <template v-slot="scope">
          <el-switch v-model="scope.row.status" active-color="#13ce66" :active-value="0" :inactive-value="1" @change="handleChangeStatus(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="170">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="170">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['mall:project-rule:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="manage(scope.row)"
                     v-hasPermi="['mall:project-rule:update']">管理</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['mall:project-rule:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="900px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入规则名称" :maxlength="50"/>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="生效时间" prop="effectTime">
              <el-date-picker clearable v-model="form.effectTime" type="date" value-format="timestamp" placeholder="选择生效时间" style="width:100%"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="过期时间" prop="expireTime">
              <el-date-picker clearable v-model="form.expireTime" type="date" value-format="timestamp" placeholder="选择过期时间" style="width:100%"/>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目范围" prop="projectScopeType">
              <el-select v-model="form.projectScopeType" placeholder="请选择项目范围" style="width:100%">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MALL_PROJECT_SCOPE_TYPE)"
                        :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规则类型" prop="ruleType">
              <el-select v-model="form.ruleType" placeholder="请选择规则类型" style="width:100%">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MALL_PROJECT_RULE_TYPE)"
                        :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="指定项目范围" prop="projectScopeContent" v-if="[1,2].includes(form.projectScopeType)" :rules="[{required: true, message: '请填写项目范围内容', trigger: 'blur'}]">
          <span v-if="form.projectScopeType === 2" class="cust-form-item-tip">
            支持变量: name no type deptNo, 逻辑与: && 逻辑或: ||  <br>
            常用函数: 开头-startsWith 结尾-endsWith 包含-contain 相等-equals 数组包含-contains <br>
            如 #no.startsWith('00121') && #type.startsWith('00121'),#no.equals('112') {'apple','banana'}.contains(#name)
          </span>
          <el-input v-model="form.projectScopeContent" type="textarea" :placeholder="form.projectScopeType === 1 ? '请输入项目编号，多个以逗号分隔' : '请输入表达式内容'" :rows="4" :maxlength="500"/>
        </el-form-item>

        <el-row v-if="[3].includes(form.ruleType)">
          <el-col :span="12">
            <el-form-item label="用户范围" prop="userScopeType" :rules="[{required: true, message: '请选择用户范围内容', trigger: 'blur'}]">
              <el-select v-model="form.userScopeType" placeholder="请选择用户范围" style="width:100%">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MALL_USER_SCOPE_TYPE)"
                        :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指定用户范围" prop="userScopeContent" v-if="[1,2].includes(form.userScopeType)" :rules="[{required: true, message: '请填写用户范围内容', trigger: 'blur'}]">
              <el-input v-model="form.userScopeContent" type="textarea" :placeholder="form.userScopeType === 1 ? '请输入员工工号，多个以逗号分隔' : '请输入部门编号，多个以逗号分隔'" :maxlength="500"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="[3].includes(form.ruleType)">
          <el-col :span="12">
            <el-form-item label="商品范围" prop="productScopeType" :rules="[{required: true, message: '请选择商品范围', trigger: 'blur'}]">
              <el-select v-model="form.productScopeType" placeholder="请选择商品范围" style="width:100%">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MALL_PRODUCT_SCOPE_TYPE)"
                        :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指定商品范围" v-if="[2].includes(form.productScopeType)" :rules="[{required: true, message: '请填写商品SKU，逗号分隔', trigger: 'blur'}]">
              <el-input v-model="form.productScopeContent" type="textarea" placeholder="请填写商品SKU，逗号分隔" :maxlength="600"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="[3].includes(form.ruleType)">
          <el-col :span="12">
            <el-form-item label="余额校验" prop="balanceValidate">
              <el-radio-group v-model="form.balanceValidate">
                <el-radio :label="false">关闭</el-radio>
                <el-radio :label="true">打开</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="授权余额值" prop="balanceThreshold" v-if="form.balanceValidate">
              <el-input-number :min="0" :max="999999" v-model="form.balanceThreshold" placeholder="请输入授权余额值" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="[3].includes(form.ruleType)">
          <el-col :span="12">
            <el-form-item label="额度校验" prop="balance2Validate">
              <el-radio-group v-model="form.balance2Validate">
                <el-radio :label="false">关闭</el-radio>
                <el-radio :label="true">打开</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="授权额度值" prop="balance2Threshold" v-if="form.balance2Validate">
              <el-input-number :min="0" :max="999999" v-model="form.balance2Threshold" placeholder="请输入授权额度值" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                          :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="备注说明" prop="memo">
          <el-input v-model="form.memo" type="textarea" placeholder="请输入备注说明" :maxlength="200" />
        </el-form-item>
        
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-drawer
      title="管理规则"
      :visible.sync="drawerVisible"
      direction="rtl"
      class="cust-drawer"
      size="75%">
      <ProjectRuleDetail ref="projectRuleDetail"></ProjectRuleDetail>
    </el-drawer>

    <el-drawer
      title="规则检测"
      :visible.sync="drawerVisible2"
      direction="rtl"
      class="cust-drawer"
      size="60%">
      <ProjectRuleTest ref="projectRuleTest"></ProjectRuleTest>
    </el-drawer>

  </div>
</template>

<script>
import * as api from "@/api/mall/project/projectRule";
import ProjectRuleDetail from './rule-detail'
import ProjectRuleTest from './rule-test'
export default {
  name: "MallProjectRule",
  components: {
    ProjectRuleDetail, ProjectRuleTest
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目规则列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 选中规则TAB
      activeRuleType: '3',
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        effectTime: [],
        expireTime: [],
        userScopeType: null,
        projectScopeType: null,
        productScopeType: null,
        status: null,
        createTime: [],
        projectScopeContent: null,
        userScopeContent: null,
        productScopeContent: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: "规则名称不能为空", trigger: "blur" }],
        effectTime: [{ required: true, message: "生效时间不能为空", trigger: "blur" }],
        expireTime: [{ required: true, message: "过期时间不能为空", trigger: "blur" }],
        ruleType: [{ required: true, message: "规则类型不能为空", trigger: "blur" }],
        projectScopeType: [{ required: true, message: "项目范围不能为空", trigger: "change" }],
        balanceValidate: [{ required: true, message: "余额校验不能为空", trigger: "blur" }],
        balanceThreshold: [{ required: true, message: "授权余额值不能为空", trigger: "blur" }],
        balance2Validate: [{ required: true, message: "额度校验不能为空", trigger: "blur" }],
        balance2Threshold: [{ required: true, message: "授权额度不能为空", trigger: "blur" }],
        action: [{ required: true, message: "执行动作不能为空", trigger: "blur" }],
        status: [{ required: true, message: "状态", trigger: "blur" }],
      },
      drawerVisible: false,
      drawerVisible2: false
    };
  },
  watch: {
    activeRuleType() {
      this.handleQuery()
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      this.queryParams.ruleType = this.activeRuleType
      // 执行查询
      api.getProjectRulePage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      let date1 = new Date().getTime()
      let date2 = new Date().getTime() + (1000 * 60 * 60 * 24 * 9000)
      this.form = {
        id: undefined,
        name: undefined,
        ruleType: undefined,
        effectTime: date1,
        expireTime: date2,
        userScopeType: 0,
        userScopeContent: undefined,
        projectScopeType: 0,
        projectScopeContent: undefined,
        productScopeType: 0,
        balanceValidate: false,
        balanceThreshold: 0,
        balance2Validate: false,
        balance2Threshold: 0,
        action: undefined,
        memo: undefined,
        status: 0,
        productScopeContent: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form.ruleType = parseInt(this.activeRuleType)
      this.open = true;
      this.title = "添加项目规则";
    },
    handleTest() {
      this.drawerVisible2 = true
      this.$nextTick(() => {
        this.$refs.projectRuleTest.init()
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      api.getProjectRule(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改项目规则";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        if(this.form.projectScopeType === 0) {
          this.form.projectScopeContent = ''
        }
        if(this.form.userScopeType === 0) {
          this.form.userScopeContent = ''
        }
        if([0,1].includes(this.form.productScopeType)) {
          this.form.productScopeContent = ''
        }
        if(this.form.ruleType !== 3) {
          this.form.balanceValidate = false
          this.form.balanceThreshold = 0
          this.form.balance2Validate = false
          this.form.balance2Threshold = 0
        }
        // 修改的提交
        if (this.form.id != null) {
          api.updateProjectRule(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        api.createProjectRule(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    async handleChangeStatus(row) {
      let params = {
        id: row.id,
        status: row.status
      }
      let res = await api.updateProjectRuleStatus(row)
      if(res.code === 0) {
        this.$modal.msgSuccess("操作成功");
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除项目规则编号为"' + id + '"的数据项?').then(function() {
        return api.deleteProjectRule(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    manage(row) {
      this.drawerVisible = true
      this.$nextTick(() => {
        this.$refs.projectRuleDetail.init(row.id)
      })
    }
  }
};
</script>
<style lang="scss">
.cust-drawer {
  .el-drawer__header {
    margin-bottom: 10px;
  }
}
.cust-form-item-tip {
  display: inline-block;
  line-height: 1.3;
  font-size: 0.9em;
}
</style>
