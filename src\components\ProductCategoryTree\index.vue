<template>
 <div>
  <el-dialog
    title="添加商品分类"
    :visible.sync="dialogVisible"
    width="800px"
    >
    <div class="product-category-container">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="title">可选的分类列表：
            <template v-if="type === 1">
              <el-radio-group v-model="categoryType" @input="categoryTypeChange">
                <el-radio :label="1">京东分类</el-radio>
                <el-radio :label="2">平台分类</el-radio>
              </el-radio-group>
            </template>
          </div>
          <el-card class="left-block">
            <el-tree
              v-if="treeShow"
              ref="eltree"
              :props="props"
              :load="loadNode"
              lazy
              show-checkbox
              :check-strictly="checkStrictly"
              :node-key="nodeKey"
              :default-expanded-keys="defaultExpandedKeys"
              :default-checked-keys="defaultCheckedKeys"
              @check-change="handleCheckChange">
            </el-tree>
          </el-card>
        </el-col>
        <el-col :span="12">
          <div class="title">当前选中的数量：{{selectedList.length}}</div>
          <div >
            <el-card class="right-block">
              <div class="right-item" v-for="(item,index) in selectedList" :key="item.categoryId">
                <div>
                  <el-tag>{{item.categoryName}}</el-tag>
                </div>
                <div>
                  <i class="el-icon-delete" @click="removeSelectedItem(index)"></i>
                  <i class="el-icon-arrow-down" v-if="moveMode && index < selectedList.length - 1" @click="moveSelectedList(index, 'down')"></i>
                  <i class="el-icon-arrow-up" v-if="moveMode && index > 0" @click="moveSelectedList(index, 'up')"></i>
                </div>
              </div>
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleClose">确 定</el-button>
    </span>
  </el-dialog>
 </div>
</template>

<script>
import * as api1 from "@/api/mall/product/configCategory.js"
import * as api2  from "@/api/mall/product/category.js"
// 隐藏侧边栏路由
const hideList = ['/index', '/user/profile'];

export default {
  name: 'ProductCategoryTree',
  props: {
    type: {
      type: Number,
      default() {
        // 业务类型，1-取平台配置，2-取租户配置
        return 1
      }
    },
    checkStrictly: {
      type: Boolean,
      default() {
        // 业务类型，1-取平台配置，2-取租户配置
        return false
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      defaultCheckedKeys: [],
      defaultExpandedKeys: [],
      checkedNodes: [],
      categoryType: 1,
      state: '',
      nodeKey: 'categoryId',
      props: {
        id: 'categoryId',
        label: 'categoryName',
        children: 'childCategoryList',
        isLeaf: 'leaf'
      },
      selectedList: [],
      treeShow: true,
      moveMode: false
    };
  },
  methods: {
    async loadNode(node, resolve) {
      console.log('level======', node.level)
      let params = {}
      let func = this.getAcutalFunc(node.level)
      if (node.level > 0) {
        params.parentCategoryId = node.data.categoryId
      }
      if(this.type === 1) {
        params.type = this.categoryType
      }

      // 执行查询
      const res = await func(params)
      let nodeList = []
      if (res.code !== 0) {
        this.$message.error(res.msg)
      } else {
        nodeList = res.data || []
      }
      if(node.level > 0) {
        this.checkLeaf(nodeList)
      }
      nodeList = nodeList.filter(item => item.type === 2 || ( item.categoryName.indexOf('京喜通') < 0) )
      resolve(nodeList)
    },
    getAcutalFunc(nodeLevel) {
      if(this.type === 1) {
        if(nodeLevel > 0) {
          return api1.getCategoryChildTreeList
        } else {
          return api1.getCategoryRootList
        }
      } else {
        if(nodeLevel > 0) {
          return api2.getCategoryChildTreeList
        } else {
          return api2.getCategoryRootList
        }
      }
    },
    checkLeaf(nodeList) {
      let func = (list = []) => {
        list.forEach(item => {
          item.leaf = !item.childCategoryList || item.childCategoryList.length === 0
          if(!item.leaf) {
            func(item.childCategoryList)
          }
        })
      }
      func(nodeList)
    },
    handleCheckChange(data, checked, indeterminate) {
      this.checkedNodes = this.$refs.eltree.getCheckedNodes(false, true) || []
      this.handleSelectedList()
    },
    handleSelectedList() {
      this.checkedNodes.forEach(item => {
        let hitIndex = this.selectedList.findIndex(item2 => item2.categoryId === item.categoryId)
        if(hitIndex < 0) {
          this.selectedList.push(item)
        }
      })
    },
    removeSelectedItem(index) {
      this.$refs.eltree.setChecked(this.selectedList[index].categoryId, false)
      this.selectedList.splice(index, 1)
    },
    moveSelectedList(index, direction) {
      let temp = this.selectedList[index]
      if(direction === 'up' && index > 0) {
        this.selectedList[index] = this.selectedList[index - 1]
        this.selectedList[index - 1] = temp
      } else if(direction === 'down' && index < this.selectedList.length - 1){
        this.selectedList[index] = this.selectedList[index + 1]
        this.selectedList[index + 1] = temp
      }
      this.selectedList = [...this.selectedList]
    },
    categoryTypeChange() {
      this.treeShow = false
      setTimeout(() => {
        this.treeShow = true
      }, 500)
    },
    show(state, selectedNodes = []) {
      this.state = state
      this.dialogVisible = true
      this.treeShow = true
      this.moveMode = false
      this.$nextTick(() => {
        let checkedKeys = []
        this.selectedList = []
        if(selectedNodes.length) {
          this.moveMode = true
          checkedKeys = selectedNodes.map(item => item.id)
          this.selectedList = selectedNodes.map( node => {
            return {
              categoryId: node.id,
              categoryName: node.name,
              categoryLevel: node.level - 1
            }
          })
        }
        this.$refs.eltree.setCheckedKeys(checkedKeys)
      })
    },
    handleClose() {
      let nodes = this.selectedList || []
      this.$emit('on-select', nodes, this.state)
      this.dialogVisible = false
      this.treeShow = true
    }
  }
};
</script>

<style lang="scss">
.topmenu-container.el-menu--horizontal > .el-menu-item {
  float: left;
  height: 50px !important;
  line-height: 50px !important;
  color: #999093 !important;
  padding: 0 5px !important;
  margin: 0 10px !important;
}

.topmenu-container.el-menu--horizontal > .el-menu-item.is-active, .el-menu--horizontal > .el-submenu.is-active .el-submenu__title {
  border-bottom: 2px solid #{'var(--theme)'} !important;
  color: #303133;
}

/* submenu item */
.topmenu-container.el-menu--horizontal > .el-submenu .el-submenu__title {
  float: left;
  height: 50px !important;
  line-height: 50px !important;
  color: #999093 !important;
  padding: 0 5px !important;
  margin: 0 10px !important;
}
.product-category-container {
  .title {
    margin: 5px 0;
  }
  .left-block {
    height: 420px;
    overflow-y: auto;
  }
  .right-block {
    height: 420px;
    overflow-y: auto;
    .el-card__body {
      padding: 5px 10px;
    }
  }
  .right-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px;
    margin: 5px 0 0 0;
    background: #0000000d;
    i {
      margin: 0 10px;
    }
  }
}
</style>
