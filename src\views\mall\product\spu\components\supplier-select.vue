<template>
  <div>
    <template v-if="notSupplierUser()">
      <el-select 
        :value="selectedVal" 
        @change="handleChange"
        :placeholder="placeholder"
        :autoWidth="autoWidth"
        :autofocus="autofocus"
        :borderless="borderless"
        :clearable="clearable"
        filterable
        :multiple="multiple"
        :size="size"
        remote
        reserve-keyword
        :remote-method="remoteMethod"
        :loading="loading"
        style="width: 100%;">
          <el-option
            v-for="item in options"
            :disabled="item.status !== 1"
            :key="item.id"
            :label="item | formatName"
            :value="item.id">
          </el-option>
      </el-select>
    </template>
    <template v-else-if="options.length"> 
      <el-tag :type="options[0].status === 1 ? '' : 'danger'">
        {{ options[0] | formatName }}
      </el-tag>
      <span v-if="options[0].status === 0"> 请您尽快完成供应商基础配置 </span>
    </template>
    
  </div>
</template>

<script>
import { getSimpleSuppliers } from '@/api/mall/product/spu'
export default {
  name: 'SupplierSelect',
  props: {
    value: {
      type: [String, Number],
      default: () => null
    },
    extParams: {
      type: Object,
      default: () => {}
    },
    placeholder: {
      type: String,
      default: () => '请选择供应商'
    },
    autoWidth: {
      type: Boolean,
      default: () => false
    },
    autofocus: {
      type: Boolean,
      default: () => false
    },
    borderless: {
      type: Boolean,
      default: () => false
    },
    clearable: {
      type: Boolean,
      default: () => true
    },
    filterable: {
      type: Boolean,
      default: () => false
    },
    multiple: {
      type: Boolean,
      default: () => false
    },
    readonly: {
      type: Boolean,
      default: () => false
    },
    size: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      loading: false,
      options: []
    }
  },
  filters: {
    formatName(item) {
      let statusDic = {
        0: ' : 未配置',
        1: '',
        2: ' : 已停用'
      }
      return item.name + statusDic[item.status]
    }
  },
  computed: {
    selectedVal() {
      if(this.value) {
        return parseInt(this.value)
      }
      return null
    }
  },
  created() {
    this.remoteMethod()
  },
  methods: {
    remoteMethod(query) {
      this.loading = true
      let params = {
        name: query
      }
      getSimpleSuppliers(params).then(res => {
        res.data.forEach(element => {
          element.id = element.id
        })
        this.options = res.data
        if(this.isSupplierUser()) {
          // 如果为供应商角色且状态为可用，则自动选中
          if(this.options && this.options.length) {
            let opt = this.options[0]
            if(opt.status === 1) {
              this.handleChange(opt.id)
            }
          }
        }
      }).finally(() => {
        this.loading = false
      })
    },
    handleChange(val) {
      let opt = this.options.find(item => item.id === val)
      this.$emit("change", val, opt)
      this.$emit("input", val);
    }
  }
}
</script>

<style>

</style>