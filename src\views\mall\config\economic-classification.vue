<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="经济分类编码" prop="code">
        <el-input v-model="queryParams.code" placeholder="请输入经济分类编码" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="经济分类名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入经济分类名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="经济分类状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option v-for="dict in statusDictDatas" :key="parseInt(dict.value)" :label="dict.label" :value="parseInt(dict.value)"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['mall:economic-classification:create']">新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="经济分类编码" align="center" prop="code" />
      <el-table-column label="经济分类名称" align="center" prop="name" />
      <el-table-column label="经济分类备注" align="center" prop="memo" width="450" />
      <el-table-column label="状态" key="status" align="center">
        <template v-slot="scope">
          <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1" @change="handleStatusChange(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['mall:economic-classification:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['mall:economic-classification:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="800px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="经济分类编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入经济分类编码" />
        </el-form-item>
        <el-form-item label="经济分类名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入经济分类名称" />
        </el-form-item>
        <el-form-item label="经济分类备注" prop="memo">
          <el-input type="textarea" :rows="3" maxlength="300" show-word-limit v-model="form.memo" placeholder="请输入经济分类备注" />
        </el-form-item>
        <el-form-item label="匹配规则" prop="matchRule">
          <span>支持变量: pname-项目名称 pno-项目编号 ptype-项目类型 pdeptNo-项目部门编号 preason-采购原因
             <br>常用函数: 开头-startsWith 结尾-endsWith 包含-contain 相等-equals 数组包含-contains
             <br>如 (#pno.startsWith('2')||#pno.startsWith('42'))||(#pdeptNo.startsWith('4')||#pno.startsWith('6'))
          </span>
          <el-input v-model="form.matchRule" type="textarea" :autosize="{ minRows: 4, maxRows: 6}" :maxlength="300" show-word-limit placeholder="请输入条件表达式" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in statusDictDatas"
                      :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as api from "@/api/mall/config/economicClassification";
import { getDictDatas, DICT_TYPE } from '@/utils/dict'
export default {
  name: "EconomicClassification",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商城经济分类列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        code: null,
        name: null,
        status: null,
        createTime: [],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        code: [{ required: true, message: "经济分类编码不能为空", trigger: "blur" }],
        name: [{ required: true, message: "经济分类名称不能为空", trigger: "blur" }],
      },
      // 数据字典
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS)
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // fix 61
      if (!this.queryParams.code) {
        this.queryParams.code = null
      }
      if (!this.queryParams.status) {
        this.queryParams.status = null
      }
      if (!this.queryParams.name) {
        this.queryParams.name = null
      }
      // 执行查询
      api.getEconomicClassificationPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        code: undefined,
        name: undefined,
        memo: undefined,
        matchRule: undefined,
        status: 0,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加商城经济分类";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      api.getEconomicClassification(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改商城经济分类";
      });
    },
    // 状态修改
    handleStatusChange(row) {
      let text = row.status === 0 ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '"此经济分类吗?').then(function() {
          return api.updateEconomicClassification(row);
        }).then(() => {
          this.$modal.msgSuccess(text + "成功");
        }).catch(function() {
          row.status = row.status === 1 ? 0: 1;
        });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.id != null) {
          api.updateEconomicClassification(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        api.createEconomicClassification(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除经济分类编码为"' + row.code + '"的数据项?').then(function() {
          return api.deleteEconomicClassification(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有经济分类数据项?').then(() => {
          this.exportLoading = true;
          return api.exportEconomicClassificationExcel(params);
        }).then(response => {
          this.$download.excel(response, '经济分类.xls');
          this.exportLoading = false;
        }).catch(() => {});
    }
  }
};
</script>
