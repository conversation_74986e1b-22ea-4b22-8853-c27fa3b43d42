<template>
  <div class="app-container" v-show="myOpen">
    <el-dialog title="CMS内容预览" :visible.sync="myOpen" @close="close" width="1200px">
      <div style="margin-top:10px;">
        <el-link :href="iframeUrl" target="_blank">{{ iframeUrl }}</el-link>
      </div>
      <div class="preview-iframe">
        <iframe :src="iframeUrl" v-if="iframeUrl"></iframe> 
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as configApi from "@/api/mall/config/basisConfig.js"
export default {
  name: 'CmsContentPreview',
  props: {
    contentId: {
      type: [Number, String],
      default: () => null
    },
    open: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      loading: false,
      myOpen: false,
      basisConfig: {},
      iframeUrl: null
    }
  },
  computed: {
    contentPreviewUrl() {
      if(this.basisConfig.domain && this.contentId) {
        let domain = this.basisConfig.domain
        if(domain.indexOf(",")) {
          domain = domain.split(",")[0]
        }
        let rn = Math.random()
        let contentId = this.contentId
        return `https://${domain}/#/information/content?cid=${contentId}?rn=${rn}`
      }
      return null
    }
  },
  watch: {
    open(newVal, oldVal) {
      this.myOpen = newVal
      this.updateIframe()
    }
  },
  created() {
    this.loadConfig()
  },
  methods: {
    close() {
      this.$emit('update:open', false)
    },
    async loadConfig() {
      let res = await configApi.getBasisConfig()
      if(res.code === 0) {
        this.basisConfig = res.data || {}
      }
    },
    updateIframe() {
      this.iframeUrl = null
      setTimeout(() => {
        this.iframeUrl = this.contentPreviewUrl
      }, 500)
      
    },
    cancel() {
      this.close()
    }
  }
}
</script>

<style lang="scss" scoped>
.preview-iframe {
  width: 100%;
  height: 600px;
  padding: 10px 0;
  iframe {
    width: 100%;
    height: 100%;
    overflow: scroll;
  }
}

</style>