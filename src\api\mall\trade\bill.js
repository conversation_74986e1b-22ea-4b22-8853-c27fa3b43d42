import request from '@/utils/request'

// 创建账单
export function createBill(data) {
  return request({
    url: '/trade/bill/create',
    method: 'post',
    data
  })
}

// 更新账单
export function updateBill(data) {
  return request({
    url: '/trade/bill/update',
    method: 'post',
    data
  })
}

// 更新订单结算方式
export function updateOrderSettlementWay(data) {
  return request({
    url: '/trade/bill/update-order-settlement-way',
    method: 'post',
    data
  })
}

// 查询待结算订单
export function getBillOrder(params) {
  return request({
    url: '/trade/bill/get-bill-order',
    method: 'get',
    params
  })
}

// 批量添加订单到账单
export function batchAddOrderRelation(params) {
  return request({
    url: '/trade/bill/batch-add-order-relation',
    method: 'get',
    params
  })
}

// 获得账单
export function getBill(params) {
  return request({
    url: '/trade/bill/get',
    method: 'get',
    params
  })
}

// 导出账单关联订单
export function exportBill(data) {
  return request({
    url: '/trade/bill/export',
    // responseType: 'blob',
    method: 'post',
    data
  })
}

// 删除账单
export function deleteBill(params) {
  return request({
    url: '/trade/bill/delete',
    method: 'delete',
    params
  })
}

// 获得账单分页
export function getBillPage(params) {
  return request({
    url: '/trade/bill/page',
    method: 'get',
    params
  })
}

// 查询账单关联订单业财状态
export function getBillOrderStatus(params) {
  return request({
    url: '/trade/bill/get-bill-order-status',
    method: 'get',
    params
  })
}

// 强制同步账单内不存在的订单到业财，如果订单存在则取消后补推，然后同步售后状态
export function forceSyncOrder4YcrhInBill(data) {
  return request({
    url: '/trade/bill/forceSyncOrder4YcrhInBill',
    method: 'post',
    data
  })
}

// 同步订单账单状态
export function syncOrderBillStatus(billId) {
  return request({
    url: '/trade/bill/sync-order-bill-status?billId=' + billId,
    method: 'get'
  })
}

// 同步发票到业财
export function syncInvoice4YcrhInBill() {
  return request({
    url: '/trade/orderSettle/fix-uploadVoucher',
    method: 'post',
  })
}

// 推送账单
export function pushBill(data) {
  return request({
    url: '/trade/bill/push-bill',
    method: 'post',
    data
  })
}

// 取消账单
export function cancelBill(data) {
  return request({
    url: '/trade/bill/cancel-bill',
    method: 'post',
    data
  })
}

// 查询账单关联订单
export function getBillRefOrder(params) {
  return request({
    url: '/trade/bill/get-bill-ref-order',
    method: 'get',
    params
  })
}

// 添加订单关联
export function addBillOrderRelation(data) {
  return request({
    url: '/trade/bill/add-order-relation',
    method: 'post',
    data
  })
}

// 删除订单关联
export function deleteBillOrderRelation(data) {
  return request({
    url: '/trade/bill/delete-order-relation',
    method: 'post',
    data
  })
}

// 账单状态修改为完成
export function completeBill(data) {
  return request({
    url: '/trade/bill/complete-bill',
    method: 'post',
    data
  })
}

// 账单拆分
export function splitBill(data) {
  return request({
    url: '/trade/bill/split-bill',
    method: 'post',
    data
  })
}

// 强制同步订单至业财融合
export function forceSyncOrder4Ycrh(data) {
  return request({
    url: '/trade/order/forceSyncOrder4Ycrh',
    method: 'post',
    data
  })
}

// 导出结算单
export function exportSettle(params) {
  return request({
    url: '/trade/bill/exportSettle',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出账单结算单发票
export function exportSettleArchive(params) {
  return request({
    url: '/trade/bill/exportSettleArchive',
    method: 'get',
    params
  })
}