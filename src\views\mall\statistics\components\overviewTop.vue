<template>
  <div>
    <el-row :gutter="40" class="panel-group">
      <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-people">
            <svg-icon icon-class="example" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              登录用户数
            </div>
            <count-to :start-val="0" :end-val="userCount" :duration="1500" class="card-panel-num" />
          </div>
        </div>
      </el-col>
      <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-message">
            <svg-icon icon-class="star" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              总商品数
            </div>
            <count-to :start-val="0" :end-val="listingTotal" :duration="1500" class="card-panel-num" />
          </div>
        </div>
      </el-col>
      <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-money">
            <svg-icon icon-class="number" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              总销量
            </div>
            <count-to :start-val="0" :end-val="orderCount" :duration="1000" class="card-panel-num" />
          </div>
        </div>
      </el-col>
      <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-money">
            <svg-icon icon-class="pay" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              总销售额
            </div>
            <count-to :start-val="0" :end-val="orderAmount" :duration="1000" :decimals="decimals" class="card-panel-num" />
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as api from "@/api/mall/statistics/overview";
import CountTo from 'vue-count-to'

export default {
  components: {
    CountTo
  },
  data() {
    return {
      userCount: 0,
      decimals: 0,
      listingTotal: 0,
      orderCount: 0,
      orderAmount: 0
    };
  },
  created() {
    this.getUserCount();
    this.getListSkuTotal();
    this.getTotalSale();
  },
  methods: {
    getUserCount() {
      // 用户数
      api.getUserCount({}).then(response => {
        if (response.code === 0) {
          this.userCount = response.data;
        }
      });
    },
    getListSkuTotal() {
      // 商品总数
      api.getSkuTotal({}).then(response => {
        if (response.code === 0) {
          this.listingTotal =  response.data.listingTotal || 0;
        }
      });
    },
    getTotalSale() {
      // 订单统计
      api.getTotalSaleV2({}).then(response => {
        if (response.code === 0) {
          this.orderAmount =  response.data.netTotalPrice;
          this.orderCount =  response.data.totalCount;
        }
      }); 
    }
  }
};
</script>

<style lang="scss" scoped>
.panel-group {
  margin-top: 18px;

  .card-panel-col {
    margin-bottom: 32px;
  }

  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-shopping {
        background: #34bfa3
      }
    }

    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-shopping {
      color: #34bfa3
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px 26px 26px 0;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}
.overview-order {
  display: flex;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(78, 89, 105, 0.1);
  .order-item {
    flex: 1;
    padding: 20px;
    display: flex;
    .order-image {
      width: 40px;
      height: 54px;
      padding: 7px 0;
      border-radius: 50%;
      margin-right: 16px;
    }
    .order-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      color: #5cb7c7;
      .label {
        font-size: 16px;
      }
      .num {
        font-size: 24px;
        font-weight: 600;
      }
    }
  }
}
</style>