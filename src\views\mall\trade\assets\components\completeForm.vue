<template>
  <div class="app-container" >
    <template>
      <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="80px" label-position="top">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="验收单号" prop="acceptNo">
                <el-input v-model="form.acceptNo" placeholder="请输入验收单号" :maxlength="100"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="资产编号，多个编码以逗号分隔" prop="assetNo">
                <el-input v-model="form.assetNo" placeholder="请输入资产编号" :maxlength="100"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="6大类编码" prop="category6">
                <el-input v-model="form.category6" placeholder="请输入6大类编码" :maxlength="50"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="16大类编码" prop="category16">
                <el-input v-model="form.category16" placeholder="请输入16大类编码" :maxlength="50"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="审批人" prop="approveBy">
                <el-input v-model="form.approveBy" placeholder="请输入审批人姓名" :maxlength="30"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="建档完成时间" prop="approveTime">
                <el-date-picker clearable size="small" v-model="form.approveTime" type="datetime"
                                  value-format="timestamp" placeholder="请选择建档完成时间" style="width:100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="业务类别" prop="businessClass">
                <el-input v-model="form.businessClass" placeholder="请输入业务类型编码" :maxlength="30"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="资产验收单文件" prop="acceptFile">
            <fileUpload v-model="form.acceptFile" :limit="1" :fileSize="10" :fileType="['pdf','png', 'jpg', 'jpeg', 'doc', 'docx', 'xls', 'xlsx']" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </template>
  </div>
</template>

<script>
import * as api from "@/api/mall/trade/assets.js"
import FileUpload from '@/components/FileUpload'
export default {
  name: "TradeOrderAssetsCompleteForm",
  components: {
    FileUpload
  },
  data() {
    return {
      detailData: null,
      // 弹出层标题
      title: "资产建档完成表单",
      // 是否显示弹出层
      open: false,
      submitLoading: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        acceptNo: [{required: true, message: "验收单编号不能为空", trigger: "blur"}],
        assetNo: [{required: true, message: "资产编号不能为空", trigger: "blur"}],
        category6: [{required: true, message: "6大类编码不能为空", trigger: "blur"}],
        category16: [{required: true, message: "16大类编码不能为空", trigger: "change"}],
      }
    }
  },
  methods: {
    show(row) {
      if(!row || !row.orderItemId) {
        return
      }
      this.detailData = row
      this.open = true
      this.reset()
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        orderItemId: this.detailData.orderItemId,
        acceptNo: undefined,
        assetNo: undefined,
        category6: undefined,
        category16: undefined,
        approveBy: undefined,
        approveTime: undefined,
        acceptFile: undefined,
        businessClass: undefined
      };
      this.resetForm("form")
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return
        }
        let data = {...this.form}
        data.assetNo = data.assetNo.split(',')
        this.submitLoading = true
        api.updateAssetComplete(data).then(response => {
          this.$modal.msgSuccess("保存成功")
          this.open = false
          this.$emit('on-update', )
        }).finally(() => {
          this.submitLoading = false
        })
      })
    },
  }
}
</script>
<style lang="scss" scoped>

</style>