import request from '@/utils/request'


// 查询配置
export function getBasisConfig() {
  return request({
    url: '/mall/basis-config/get',
    method: 'get'
  })
}

// 更新配置
export function updateBasisConfig(data) {
  return request({
    url: '/mall/basis-config/update',
    method: 'put',
    data: data
  })
}

// 更新配置
export function createBasisConfig(data) {
  return request({
    url: '/mall/basis-config/create',
    method: 'post',
    data: data
  })
}

// 清除配置
export function cleanBasisConfig(id) {
  return request({
    url: '/mall/basis-config/delete?id=' + id,
    method: 'delete'
  })
}
