<template>
  <div> 
      <el-card>
      <div class="flex-center"> 
        <ApplySteps ref="applySteps" :status="approveStatus" style="width:90%;margin:20px 0 30px"></ApplySteps>
      </div>
      <div class="flex-center">
      <el-form ref="form" :model="form" :rules="rules" label-width="140px" style="width:800px">
        <el-form-item label="统一社会信用代码" prop="enterpriseUnifiedId">
          <el-input v-model="form.enterpriseUnifiedId" :maxlength="30" placeholder="请输入统一社会信用代码"/>
        </el-form-item>
        <el-form-item label="企业全称" prop="enterpriseFullName">
          <el-input v-model="form.enterpriseFullName" :maxlength="60" placeholder="请输入企业全称"/>
        </el-form-item>
        <el-form-item label="企业简称" prop="enterpriseShortName">
          <el-input v-model="form.enterpriseShortName" :maxlength="20" placeholder="请输入企业简称"/>
        </el-form-item>
        <el-form-item label="联系人" prop="contact">
          <el-input v-model="form.contact" :maxlength="20" placeholder="请输入联系人"/>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="form.phone" :maxlength="20" placeholder="请输入联系电话"/>
        </el-form-item>
        <el-form-item label="法人身份证照片" prop="fileLegalRep">
          <imageUpload v-model="form.fileLegalRep" :limit="2" :resType="2"/>
        </el-form-item>
        <el-form-item label="营业执照照片" prop="fileLicense">
          <imageUpload v-model="form.fileLicense" :limit="2" :resType="2"/>
        </el-form-item>
        <el-form-item label="行业资质证明" prop="fileQualification">
          <imageUpload v-model="form.fileQualification" :limit="5" :resType="2"/>
        </el-form-item>
        <el-form-item label="其它补充材料" prop="fileOther">
          <imageUpload v-model="form.fileOther" :limit="5" :resType="2"/>
        </el-form-item>
        <el-form-item label="申请入驻说明" prop="remark" style="width:650px;">
          <el-input v-model="form.remark" type="textarea" :maxlength="300" :rows=4 placeholder="请输入内容"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="submitLoading" @click="submitForm(0)">保存数据</el-button>
          <el-button type="primary" :loading="submitLoading" @click="submitForm(1)">提交审核</el-button>
          <el-button @click="cancel">取 消</el-button>
        </el-form-item>
      </el-form>
      </div>
      </el-card>
  </div>
</template>

<script>
import * as api from '@/api/mall/supplier/supplier-form'
import ImageUpload from '@/components/ImageUpload'
import ApplySteps from '@/views/mall/supplier/components/apply-steps'
export default {
  name: 'SupplierSupplierFormAdd',
  components: { ImageUpload, ApplySteps },
  data() {
    return {
      submitLoading: false,
      approveStatus: null,
      form: {
        id: undefined,
        enterpriseUnifiedId: '',
        enterpriseFullName: '',
        enterpriseShortName: '',
        contact: '',
        phone: '',
        remark: '',
        fileLegalRep: [],
        fileLicense: [],
        fileQualification: [],
        fileOther: []
      },
      rules: {
        enterpriseUnifiedId: [
          { required: true, blur: 'trigger', message: '请输入统一社会信用代码'},
          {
            validator: (rule, value, callback) => {
              let params = {
                id: this.form.id,
                enterpriseUnifiedId: value
              }
              api.validateEnterpriseUnifiedId(params).then((res) => {
                this.tenantInfo2 = res.data
                if (res.data) {
                  callback()
                } else {
                  callback("您输入的统一社会信用代码已经存在，请调整后重试");
                }
              })
            },
            trigger: "blur",
          },
        ],
        enterpriseFullName: [
          { required: true, blur: 'trigger', message: '请输入企业全称'}
        ],
        enterpriseShortName: [
          { required: true, blur: 'trigger', message: '请输入企业简称'}
        ],
        contact: [
          { required: true, blur: 'trigger', message: '请输入联系人'}
        ],
        phone: [
          { required: true, blur: 'trigger', message: '请输入联系电话'},
          { pattern: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/,  trigger: "blur", message: "请输入正确的手机号码" }
        ],
        fileLegalRep: [
          { type: 'array', required: true, blur: 'trigger', message: '请上传法人照片正反面'}
        ],
        fileLicense: [
          { type: 'array', required: true, blur: 'trigger', message: '请上传营业执照照片'}
        ]
      }
    }
  },
  methods: {
    async edit(id) {
      if(!id) {
        return
      }
      this.resetForm()
      let res = await api.getUserApplyFormDetail({id: id})
      if(res.data) {
        this.approveStatus = res.data.approveStatus
        Object.keys(this.form).forEach(key => {
          this.form[key] = res.data[key]
        })
      }
    },
    resetForm() {
      this.approveStatus = null
      this.form = {
        id: undefined,
        enterpriseUnifiedId: '',
        enterpriseFullName: '',
        enterpriseShortName: '',
        contact: '',
        phone: '',
        remark: '',
        fileLegalRep: [],
        fileLicense: [],
        fileQualification: [],
        fileOther: []
      }
    },
    cancel() {
      this.$emit('on-cancel', 1)
    },
    /** 提交按钮 */
    submitForm(type) {
      this.$refs["form"].validate((valid, errorFields) => {
        if (!valid) {
          const firstError = this.getFirstErrorMessage(errorFields)
          this.$modal.msgWarning(firstError)
          return
        }

        if(type === 1) {
          this.$confirm('您确认提交审核吗?', '确认提示').then(() => {
            this.doSubmit(type)
          })
        } else {
          this.doSubmit(type)
        }
      })
    },
    doSubmit(type) {
      this.submitLoading = true
      // 添加的提交
      api.saveApplyForm(this.form).then(async (res) => {
        if(type == 1) {
          await api.submitApplyForm(res.data)
          this.$modal.msgSuccess("提交成功")
        } else {
          this.$modal.msgSuccess("保存成功")
        }
        this.$emit('on-update', res.data)
      }).finally(() => {
        this.submitLoading = false
      })
    },
    // 从验证错误对象中提取第一条错误信息
    getFirstErrorMessage(errorFields) {
      if (!errorFields) return '表单验证失败'
      
      // 获取第一个无效字段的key
      const firstInvalidField = Object.keys(errorFields)[0]
      
      // 获取该字段的错误信息数组
      const errorArray = errorFields[firstInvalidField]
      
      // 返回第一条错误信息
      return errorArray[0].message
    }
  }

}
</script>

<style>

</style>