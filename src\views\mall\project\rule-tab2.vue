<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="商品名称" prop="skuName">
        <el-input v-model="queryParams.skuName" placeholder="请输入商品名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="平台SKU" prop="skuId">
        <el-input v-model="queryParams.skuId" oninput="value=value.replace(/^(0+)|[^\d]+/g,'')" placeholder="请输入商品SKU" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="三方SKU" prop="skuInnerId">
        <el-input v-model="queryParams.skuInnerId" placeholder="请输入三方SKU" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="供应商" prop="supplierId">
        <supplier-select size="small" v-model="queryParams.supplierId" placeholder="请选择供应商" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-alert
      style="margin:0 0 10px;padding:10px;"
      :closable="false"
      title="商品白名单介于禁止规则和有条件规则之间，即对可用的经费卡开放的商品"
      type="info">
    </el-alert>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button v-hasPermi="['mall:project-rule:create']" type="primary" plain icon="el-icon-download" size="mini" @click="handleImport">导入商品</el-button>
        <el-button v-hasPermi="['mall:project-rule:create']" type="primary" plain icon="el-icon-plus" size="mini" @click="addProductSku">添加商品</el-button>
        <el-button v-hasPermi="['mall:project-rule:create']" type="primary" plain icon="el-icon-delete" size="mini" @click="deleteWhiteSku()">批量删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"/>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list" 
    @selection-change="handleSelectionChange" 
    :default-sort = "{prop: 'createTime', order: 'descending'}">
      <el-table-column type="selection" align="center" width="55"></el-table-column>
      <el-table-column label="商品信息" align="center" width="300">
        <template v-slot="scope">
          <div class="product-info">
            <img v-if="scope.row.picUrl" :src="scope.row.picUrl" alt="图片" class="img-height" />
            <div :title="scope.row.skuName" class="message">{{ scope.row.skuName }}</div>
          </div>
        </template>
        <!-- TODO 前端优化：可以有个 + 号，点击后，展示每个 sku -->
      </el-table-column>
      <el-table-column label="平台/三方SKU" align="center" prop="skuId" width="300">
        <template v-slot="scope">
          <el-link @click="handleView(scope.row)" type="primary"><span>{{ scope.row.skuId }}</span></el-link> / 
          <span v-if="scope.row.skuInnerId">{{ scope.row.skuInnerId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="供应商" align="center" prop="supplierName"/>
      <el-table-column label="商品分类" align="center" prop="fullCategoryName"/>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-delete" @click="deleteWhiteSku(scope.row)"
                      v-hasPermi="['mall:project-rule:create']">删除</el-button>           
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <ProductSkuSelect ref="productSkuSelect" @on-confirmed="handleAddCallback"></ProductSkuSelect>

    <el-dialog title="商品导入" :visible.sync="addBatchOpen" width="900px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="{}" label-width="120px">
        <el-form-item label="平台SKU" prop="inputSkuIds" :rules="[{required: true, message: '请输入平台SKU，多个以逗号分隔'}]">
          <el-input v-model="form.inputSkuIds" type="textarea" placeholder="请输入平台SKU，多个以逗号分隔" :rows="6" :maxlength="1000" />
        </el-form-item>
        
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleAddBatch" :loading="addBatchLoading">确 定</el-button>
        <el-button @click="addBatchOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as api from "@/api/mall/project/projectWhiteSku";
import SupplierSelect from "@/views/mall/product/spu/components/supplier-select";
import ProductSkuSelect from "@/views/mall/product/spu/components/sku-select";
export default {
  name: "ProductSeoSkuList",
  components: { SupplierSelect, ProductSkuSelect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商品spu列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        skuInnerId: null,
        skuName: null,
        id: null,
        supplierId: null
      },
      multipleSelection: [],
      form: {
        inputSkuIds: ''
      },
      addBatchOpen: false,
      addBatchLoading: false
    };
  },
  watch: {
    "queryParams": {
      deep: true,
      handler(newVal, oldVal) {
        const ck = 'rule-white-sku-list-params'
        sessionStorage.setItem(ck, JSON.stringify(newVal))
      }
    }
  },
  created() {
    this.initParams();
    this.handleQuery();
  },
  methods: {
    initParams() {
      const ck = 'rule-white-sku-list-params'
      const val = sessionStorage.getItem(ck)
      if(val) {
        Object.assign(this.queryParams, JSON.parse(val))
        this.queryParams.categoryIds = []
        this.queryParams.fullCategoryId = null
      }
    },
    getParams() {
      let params = {...this.queryParams};
      if(!params.skuInnerId) {
        delete params.skuInnerId
      }
      if(!params.skuName) {
        delete params.skuName
      }
      return params
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      const params = this.getParams();
      // 执行查询
      api.getProjectWhiteSkuPage(params).then((response) => {
        this.list = ((response.data && response.data.list) || []).map(item => {
          return item
        });
        this.total = parseInt(response.data.total);
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    async multipleChangeStatus() {
      if (!this.multipleSelection.length) {
        this.$message.warning("请至少选择一个商品")
        return;
      }
      let params = this.multipleSelection.map(item => {
        return {
          id: item.id,
          showStatus: item.showStatus === 1 ? 0 : 1
        }
      })
      let res = await updateShowStatus(params)
      if(res.code === 0) {
        this.getList()
        this.$modal.msgSuccess("操作成功")
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    addProductSku() {
      this.$refs.productSkuSelect.init({seoStatus: 0})
    },
    handleImport() {
      this.inputSkuIds = ''
      this.addBatchOpen = true
    },
    handleAddCallback(rows) {
      console.log('rows----', rows)
      if(!rows || !rows.length) {
        return;
      }
      let params = rows.map(item => {
        return {
          skuId: item.id,
          skuName: item.skuName,
          skuInnerId: item.skuInnerId,
          fullCategoryId: item.fullCategoryId,
          fullCategoryName: item.fullCategoryName,
          supplierId: item.supplierId,
          supplierName: item.supplierName,
          supplierType: item.supplierType
        }
      })
      api.createProjectWhiteSkuBatch(params).then(res => {
        this.addBatchOpen = false
        this.$message.success("操作成功")
        this.getList()
      })
    },
    handleAddBatch() {
      if(!this.form.inputSkuIds) {
        this.$message.warning("请输入商品SKU")
        return;
      }
      let params = {
        skuList: this.form.inputSkuIds.split(',')
      }

      this.addBatchLoading = true
      api.createProjectWhiteSkuBatchV2(params).then(res => {
        this.addBatchOpen = false
        this.$message.success("操作成功")
        this.getList()
      }).finally(() => {
        this.addBatchLoading = false
      })
    },
    deleteWhiteSku(row) {
      let params = {}
      if(row) {
        params = [row.id]
      } else if (this.multipleSelection.length) {
        params = this.multipleSelection.map(item => item.id)
      } else {
        this.$message.warning("请至少选择一个商品")
        return;
      }

      this.$modal.confirm('是否确认删除?').then(function() {
        return api.deleteProjectWhiteSkuBatch(params)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    }
  }
};
</script>
<style lang="scss">

</style>
