import request from '@/utils/request'

export function getSkuTotal(params) {
	return request({
		url: '/product/statistics/sku/getSkuTotal' ,
		method: 'get',
		params
	})
}

export function getProductCountByCategory(params) {
	return request({
		url: '/product/statistics/sku/getProductCountByCategory' ,
		method: 'get',
		params
	})
}

export function getSellProductCategorySummary(params) {
	return request({
		url: '/product/statistics/sku/getSellProductCategorySummary' ,
		method: 'get',
		params
	})
}

export function getSupplierSellProductCountSummary(params) {
	return request({
		url: '/product/statistics/sku/getSupplierSellProductCountSummary' ,
		method: 'get',
		params
	})
}

export function getSellProductAmountSummary(params) {
	return request({
		url: '/product/statistics/sku/getSellProductAmountSummary' ,
		method: 'get',
		params
	})
}

export function getSellProductTotalSummary(params) {
	return request({
		url: '/product/statistics/sku/getSellProductTotalSummary' ,
		method: 'get',
		params
	})
}