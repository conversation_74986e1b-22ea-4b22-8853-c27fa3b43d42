<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="关键字" prop="keyword">
        <el-input v-model="queryParams.keyword" placeholder="请输入关键字" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="消息Id" prop="messageId">
        <el-input v-model="queryParams.messageId" placeholder="请输入VOP消息Id" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="消息类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择消息类型" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MALL_VOP_MESSAGE_TYPE)" :key="parseInt(dict.value)" :label= "dict.value + '-' + dict.label"
                     :value="parseInt(dict.value)"/>
        </el-select>
      </el-form-item>
      <el-form-item label="处理状态" prop="handleStatus">
        <el-select v-model="queryParams.handleStatus" placeholder="请选择消息处理状态" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MALL_VOP_MESSAGE_HANDLE_STATUS)" :key="parseInt(dict.value)" :label="dict.label"
                     :value="parseInt(dict.value)"/>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime" label-width="80px">
        <el-date-picker v-model="queryParams.createTime" value-format="yyyy-MM-dd HH:mm:ss" type="daterange" :picker-options="pickerOptions"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']" style="width: 240px;"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain size="mini" @click="handleBatchConsume"
                   v-hasPermi="['mall:vop-message:consume']">批量消费消息</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table ref="multipleTable" v-loading="loading" :data="list">
      <el-table-column type="selection" width="55">
    </el-table-column>
      <el-table-column label="消息Id" align="center" prop="messageId" width="180"/>
      <el-table-column label="关键字" align="center" prop="keyword" width="180"/>
      <el-table-column label="消息类型" align="center" prop="typeStr" width="180"/>
      <!-- <el-table-column label="消息说明" align="center" prop="type">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.MALL_VOP_MESSAGE_TYPE" :value="scope.row.type"/>
        </template>
      </el-table-column> -->
      <el-table-column
        label="消息内容"
        align="center"
        prop="content"
      >
        <template v-slot="scope">
          <el-tooltip class="item" effect="dark" :content="scope.row.content" placement="top">
            <span style="display:inline-block; max-width:500px; overflow:hidden; text-overflow:ellipsis; white-space:nowrap; vertical-align:middle;">
              {{ scope.row.content }}
            </span>
          </el-tooltip>
          <el-button
            type="text"
            size="mini"
            style="margin-left:4px;"
            @click="copyContent(scope.row.content)"
          >复制</el-button>
        </template>
      </el-table-column>
      <el-table-column label="处理状态" align="center" prop="handleStatus" width="180">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.MALL_VOP_MESSAGE_HANDLE_STATUS" :value="scope.row.handleStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" @click="handleConsume(scope.row)"
                     v-hasPermi="['mall:vop-message:consume']">消费</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(批量消费消息) -->
    <el-dialog :title="title" :visible.sync="open" width="500px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="VOP消息">
          <el-input type="textarea" v-model="form.content"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getVopMessage, getVopMessagePage, consume, batchConsume } from "@/api/mall/trade/vopMessage";
import { formatDate } from "@/utils/dateUtils";
import { getDictDatas, getDictDataLabel, DICT_TYPE } from '@/utils/dict'
export default {
  name: "VopMessage",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // VOP消息列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        clientId: null,
        yn: null,
        messageId: null,
        type: null,
        content: null,
        handleStatus: null,
        createTime: [],
        keyword: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        content: [{ required: true, message: "VOP消息id不能为空", trigger: "blur" }]
      },
      pickerOptions: {
        shortcuts: [{
          text: '本周',
          onClick(picker) {
            // This week (Monday to Sunday)
            const now = new Date()
            const day = now.getDay() || 7 // 周日返回0，转换为7
            const monday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - day + 1)
            const sunday = new Date(now.getFullYear(), now.getMonth(), now.getDate() + (7 - day))
            picker.$emit('pick', [monday, sunday])
          }
        }, {
          text: '本月',
          onClick(picker) {
            // Current month
            const start = new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            const end = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '本年',
          onClick(picker) {
            // Current year
            const start = new Date(new Date().getFullYear(), 0, 1)
            const end = new Date(new Date().getFullYear(), 11, 31)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '近7天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近30天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近90天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近180天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
            picker.$emit('pick', [start, end]);
          }
        }]
      }
    };
  },
  created() {
    let preDate = new Date()
    preDate.setTime(new Date().getTime() - (1000 * 60 * 60 * 24 * 7))
    this.queryParams.createTime.push(formatDate(preDate, 'yyyy-MM-dd 00:00:00'))
    this.queryParams.createTime.push(formatDate(new Date(), 'yyyy-MM-dd 23:59:59'))

    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      if(this.queryParams.keyword == ''){
        this.queryParams.keyword = null
      }
      if(this.queryParams.messageId == ''){
        this.queryParams.messageId = null
      }
      const types = this.getDictDatas(DICT_TYPE.MALL_VOP_MESSAGE_HANDLE_STATUS)
      // 执行查询
      getVopMessagePage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.list.forEach(item => {
          const label = this.getDictDataLabel(DICT_TYPE.MALL_VOP_MESSAGE_TYPE, item.type)
          item.typeStr = label ? label + ' - ' + String(item.type) : String(item.type);
        });
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        clientId: undefined,
        yn: undefined,
        messageId: undefined,
        type: undefined,
        content: undefined,
        handleStatus: undefined,
        keyword: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** VOP消息批量消费操作 */
    handleBatchConsume() {
      // 获取表格勾选项
      const selection = this.$refs.multipleTable ? this.$refs.multipleTable.selection : [];
      if (selection && selection.length > 0) {
        // 有勾选项，逆序后批量消费
        const messageIds = selection.slice().reverse().map(item => item.messageId);
        this.$modal.confirm('确定要批量消费选中的消息吗？').then(() => {
          return batchConsume(messageIds);
        }).then(() => {
          this.$modal.msgSuccess("批量消费成功");
          this.getList();
        }).catch(() => {});
      } else {
        // 没有勾选项，弹出输入框
        this.reset();
        this.open = true;
        this.title = "VOP消息批量消费";
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 批量消费成功
        if (this.form.content != null) {
          var messageIds = this.form.content.replace("\n", ",").replace(" ", ",").replace("，", ",").replace(";", ",").replace("|", ",").split(",");
          batchConsume(messageIds).then(response => {
            this.$modal.msgSuccess("批量消费成功");
            this.open = false;
            this.getList();
          });
          return;
        }
      });
    },
    /** 消费按钮操作 */
    handleConsume(row) {
      const messageId = row.messageId;
      this.$modal.confirm('是否确认消费VOP消息编号为"' + messageId + '"的数据项?').then(function() {
          return consume(messageId);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("消费成功");
        }).catch(() => {});
    },
    copyContent(content) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(content).then(() => {
          this.$message.success('复制成功');
        }, () => {
          this.$message.error('复制失败');
        });
      } else {
        // 兼容旧浏览器
        const textarea = document.createElement('textarea');
        textarea.value = content;
        document.body.appendChild(textarea);
        textarea.select();
        try {
          document.execCommand('copy');
          this.$message.success('复制成功');
        } catch (err) {
          this.$message.error('复制失败');
        }
        document.body.removeChild(textarea);
      }
    },
  }
};
</script>
