<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单号" prop="orderNo">
        <el-input v-model="queryParams.orderNo" placeholder="请输入订单号" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="商品SKU" prop="skuId">
        <el-input v-model="queryParams.skuId" placeholder="请输入商品SKU" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['product:comment:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['product:comment:export']">导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="订单号" align="center" prop="orderNo" />
      <el-table-column label="商品SKU" align="center" prop="skuId" />
      <el-table-column label="供应商" align="center" prop="supplierName" />
      <!-- <el-table-column label="会员id" align="center" prop="memberId" /> -->
      <el-table-column label="会员昵称" align="center" prop="nickName" />
      <el-table-column label="评价星数" align="center" prop="score" width="80">
        <template slot-scope="{ row }">
          <el-tag :type="row.score < 4 ? 'warning' : 'success'">{{ row.score }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="评价内容" align="center" prop="content" />
      <!-- <el-table-column label="上传图片地址，以逗号隔开" align="center" prop="pics" /> -->
      <el-table-column label="是否匿名" align="center" prop="anonymousFlag" width="80" :formatter="anonymousFormat" />
      <el-table-column label="审核状态" align="center" prop="auditStatus" width="120">
        <template slot-scope="{ row }">
          <el-tag :type="['info','success','warning'][row.auditStatus]">{{ statusFormat(row) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime">
        <template slot-scope="{ row }">
          <div>{{ parseTime(row.createTime) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200px" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <!-- <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['product:comment:update']">修改</el-button> -->
          <el-button v-if="scope.row.auditStatus == 0" size="mini" type="text" icon="el-icon-edit" @click="handleAudit(scope.row, 1)"
                     v-hasPermi="['product:comment:audit']">通过</el-button>
          <el-button v-if="scope.row.auditStatus == 0" size="mini" type="text" icon="el-icon-edit" @click="handleAudit(scope.row, 2)"
                     v-hasPermi="['product:comment:audit']">驳回</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['product:comment:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="500px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="订单号" prop="orderNo">
          <el-input v-model="form.orderNo" placeholder="请输入订单号" />
        </el-form-item>
        <el-form-item label="商品SKU" prop="skuId">
          <el-input v-model="form.skuId" placeholder="请输入商品SKU" />
        </el-form-item>
        <el-form-item label="商品SPU" prop="spuId">
          <el-input v-model="form.spuId" placeholder="请输入商品SPU" />
        </el-form-item>
        <el-form-item label="供应商" prop="supplierId">
          <el-input v-model="form.supplierId" placeholder="请输入供应商id" />
        </el-form-item>
        <el-form-item label="会员" prop="memberId">
          <el-input v-model="form.memberId" placeholder="请输入会员id" />
        </el-form-item>
        <el-form-item label="会员昵称" prop="nickName">
          <el-input v-model="form.nickName" placeholder="请输入会员昵称" />
        </el-form-item>
        <el-form-item label="评价星数 1->5" prop="score">
          <el-input v-model="form.score" placeholder="请输入评价星数 1->5" />
        </el-form-item>
        <el-form-item label="评价内容">
          <editor v-model="form.content" :min-height="192"/>
        </el-form-item>
        <el-form-item label="上传图片地址，以逗号隔开" prop="pics">
          <el-input v-model="form.pics" placeholder="请输入上传图片地址，以逗号隔开" />
        </el-form-item>
        <el-form-item label="是否匿名 1-是 0 否" prop="anonymousFlag">
          <el-input v-model="form.anonymousFlag" placeholder="请输入是否匿名 1-是 0 否" />
        </el-form-item>
        <el-form-item label="审核状态" prop="auditStatus">
          <el-radio-group v-model="form.auditStatus">
            <el-radio label="1">请选择字典生成</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createComment, updateComment, deleteComment, getComment, getCommentPage, exportCommentExcel, auditComment } from "@/api/mall/product/comment.js";
import Editor from '@/components/Editor';

export default {
  name: "ProductComment",
  components: {
    Editor
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商品评价列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        orderNo: null,
        skuId: null,
        spuId: null,
        supplierId: null,
        memberId: null,
        nickName: null,
        score: null,
        content: null,
        pics: null,
        anonymousFlag: null,
        auditStatus: null,
        createTime: [],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        orderNo: [{ required: true, message: "订单号不能为空", trigger: "blur" }],
        skuId: [{ required: true, message: "商品SKU不能为空", trigger: "blur" }],
        spuId: [{ required: true, message: "商品SPU不能为空", trigger: "blur" }],
        supplierId: [{ required: true, message: "供应商不能为空", trigger: "blur" }],
        memberId: [{ required: true, message: "会员不能为空", trigger: "blur" }],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getCommentPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = Number(response.data.total) || 0;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        orderNo: undefined,
        skuId: undefined,
        spuId: undefined,
        supplierId: undefined,
        memberId: undefined,
        nickName: undefined,
        score: undefined,
        content: undefined,
        pics: undefined,
        anonymousFlag: undefined,
        auditStatus: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加商品评价";
    },
    /** 审核 */
    handleAudit(row, status) {
      this.reset();
      auditComment({
        id: row.id,
        auditStatus: status
      }).then(response => {
        this.getList();
      });
    },
    // 修改按钮操作
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getComment(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改商品评价";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.id != null) {
          updateComment(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createComment(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除商品评价编号为"' + id + '"的数据项?').then(function() {
          return deleteComment(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有商品评价数据项?').then(() => {
          this.exportLoading = true;
          return exportCommentExcel(params);
        }).then(response => {
          this.$download.excel(response, '商品评价.xls');
          this.exportLoading = false;
        }).catch(() => {});
    },
    // 是否匿名
    anonymousFormat(row) {
      const type = ['否', '是']
      return type[row.anonymousFlag]
    },
    statusFormat(row) {
      const type = ['待审核', '审批通过', '审批驳回']
      return type[row.auditStatus] || '--'
    }
  }
};
</script>
