import request from '@/utils/request'

// 检查VOP配置
export function checkVopConfig(id) {
  return request({
    url: '/mall/vop-config/check?supplierId=' + id,
    method: 'get'
  })
}

// 创建vop配置
export function createVopConfig(data) {
  return request({
    url: '/mall/vop-config/create',
    method: 'post',
    data: data
  })
}

// 查询VOP配置
export function getVopConfig(id) {
  return request({
    url: '/mall/vop-config/get?id=' + id,
    method: 'get'
  })
}

// 更新VOP配置
export function updateVopConfig(data) {
  return request({
    url: '/mall/vop-config/update',
    method: 'put',
    data: data
  })
}

// 删除VOP配置
export function deleteVopConfig(id) {
  return request({
    url: '/mall/vop-config/delete?id=' + id,
    method: 'delete'
  })
}

// 查询VOP AccessToken
export function getVopAccessToken() {
  return request({
    url: '/mall/vop-access-token/get',
    method: 'get'
  })
}

// 查询VOP AccessToken获取授权跳转链接
export function getVopTokenAuthorizeUrl() {
  return request({
    url: '/mall/vop-access-token/authorize/url',
    method: 'get'
  })
}

// 刷新VOP AccessToken
export function refreshVopAccessToken() {
  return request({
    url: '/mall/vop-access-token/refresh',
    method: 'post'
  })
}

// 查询VOP 商品池商品总数量
export function getVopTotal() {
  return request({
    url: '/product/vopgoods/getSyncTotal',
    method: 'get'
  })
}

// 同步VOP 商品池商品
export function syncVopAllGoods() {
  return request({
    url: '/product/vopgoods/syncAllGoods',
    method: 'post'
  })
}

// 同步VOP 单个商品
export function syncVopSingleGoods(data) {
  return request({
    url: '/product/vopgoods/syncSinglleGood',
    method: 'post',
    params: data
  })
}