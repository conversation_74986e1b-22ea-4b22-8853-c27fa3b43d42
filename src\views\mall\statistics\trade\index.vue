<template>
  <div class="app-container">
    <div class="card-panel-wrapper">
      <sell-card-panel></sell-card-panel>
      <div class="order-row-1">
        <tradeStatisticsByDept></tradeStatisticsByDept>
        <!-- <trade-statistics-by-project></trade-statistics-by-project> -->
        <tradeStatisticsBySupplier></tradeStatisticsBySupplier>
      </div>
      <div class="order-row-2">
        <tradeStatisticsByAfterSale></tradeStatisticsByAfterSale>
        <tradeStatisticsByStatus></tradeStatisticsByStatus>
      </div>
    </div>
  </div>
</template>

<script>
import sellCardPanel from '../components/sellCardPanel.vue'
import tradeStatisticsByDept from '../components/orderStatisticsByDept.vue'
// import tradeStatisticsByProject  from '../components/orderStatisticsByProject.vue'
import tradeStatisticsBySupplier from '../components/orderStatisticsBySupplier.vue'
import tradeStatisticsByAfterSale from '../components/orderStatisticsByAfterSale.vue'
import tradeStatisticsByStatus from '../components/orderStatisticsByStatus.vue'

export default {
  name: "StatisticsTrade",
  components: {
    sellCardPanel,
    tradeStatisticsByDept,
    // tradeStatisticsByProject,
    tradeStatisticsBySupplier,
    tradeStatisticsByAfterSale,
    tradeStatisticsByStatus
  },
  data() {
    return {
      activeName: 'order'
    };
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
.order-row-1 {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.order-row-2 {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}
</style>