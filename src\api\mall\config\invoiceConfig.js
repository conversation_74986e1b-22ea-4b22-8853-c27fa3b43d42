import request from '@/utils/request'


// 查询配置
export function getInvoiceConfig() {
  return request({
    url: '/mall/invoice-config/get',
    method: 'get'
  })
}

// 更新配置
export function updateInvoiceConfig(data) {
  return request({
    url: '/mall/invoice-config/update',
    method: 'put',
    data: data
  })
}

// 更新配置
export function createInvoiceConfig(data) {
  return request({
    url: '/mall/invoice-config/create',
    method: 'post',
    data: data
  })
}

// 清除配置
export function cleanInvoiceConfig(id) {
  return request({
    url: '/mall/invoice-config/delete?id=' + id,
    method: 'delete'
  })
}
