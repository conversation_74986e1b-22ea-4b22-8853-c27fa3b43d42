<template>
  <div class="app-container">
    <el-tabs class="form" v-model="activeName">
      <el-tab-pane label="每日上架数" name="days">
      </el-tab-pane>
      <el-tab-pane label="商品销量图" name="products">
      </el-tab-pane>
      <el-tab-pane label="商品分类图" name="category">
      </el-tab-pane>
    </el-tabs>
   
    <daily v-if="activeName === 'days'"></daily>
    <product v-if="activeName === 'products'"></product>
    <category v-if="activeName === 'category'"></category>
  </div>
</template>

<script>
import daily from '../components/daily'
import product from '../components/productSales'
import category from '../components/categorySales'

export default {
  name: "StatisticsEchart",
  components: {
    daily,
    product,
    category
  },
  data() {
    return {
      activeName: 'days',
    };
  }
};
</script>

<style lang="scss" scoped>
</style>