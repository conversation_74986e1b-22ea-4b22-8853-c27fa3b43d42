<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="分类名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入分类名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="分类编码" prop="categoryId">
        <el-input v-model.number="queryParams.categoryId" placeholder="请输入分类编码" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <template v-if="isProject && notSupplierUser()">
        <el-form-item label="经济分类状态" prop="haveEconomyClass" >
          <el-select v-model="queryParams.haveEconomyClass" clearable>
            <el-option label="有" :value="true"></el-option>
            <el-option label="无" :value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="经济分类编码" prop="economyClass">
          <el-input v-model="queryParams.economyClass" placeholder="请输入经济分类编码" clearable @keyup.enter.native="handleQuery"/>
        </el-form-item>
      </template>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5" v-if="notSupplierUser()">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['product:category:create']">手动新增
        </el-button>
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addProductCategory"
                   v-hasPermi="['product:category:create']">批量复制
        </el-button>
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleImport"
                   v-hasPermi="['product:category:create']">批量导入
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <!-- <el-button type="info" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll">展开/折叠</el-button> -->
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="handleQuery"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-if="refreshTable" v-loading="loading" :data="list"  row-key="id" :default-expand-all="isExpandAll"
              :tree-props="{children: 'children', hasChildren: 'hasChildren'}" lazy :load="getChildrenList">
      <el-table-column label="分类ID" prop="categoryId"/>
      <el-table-column label="分类名称" prop="categoryName"/>
      <el-table-column label="分类图标" align="center" prop="icon" v-if="notSupplierUser()">
        <template v-slot="scope">
          <el-image v-if="scope.row.icon" style="height: 16px;" :src="scope.row.icon" fit="fit"></el-image>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="H5分类图标" align="center" prop="iconH5" v-if="notSupplierUser()">
        <template v-slot="scope">
          <el-image v-if="scope.row.iconH5" style="height: 16px;" :src="scope.row.iconH5" fit="fit"></el-image>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="经济分类" align="center" prop="economyClass" v-if="isProject && notSupplierUser()"/>
      <el-table-column label="固资经济分类" align="center" prop="economyClass2" v-if="isProject && notSupplierUser()"/>
      <el-table-column label="分类排序" align="center" prop="orderSort" v-if="notSupplierUser()"/>
      <el-table-column label="类别" key="type" align="center" v-if="notSupplierUser()">
        <template v-slot="scope">
          <el-tag :type="scope.row.type === 1 ? 'info' : 'primary'">{{ scope.row.type === 1 ? '京东' : '平台' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column v-if="checkPermi(['product:category:update'])  && notSupplierUser()" label="开启状态" key="status" align="center">
        <template v-slot="scope">
          <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" @change="handleStatusChange(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" v-if="notSupplierUser()">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" v-if="notSupplierUser()">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-plus" @click="handleAdd(scope.row)"
                     v-hasPermi="['product:category:create']">新增
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['product:category:update']">修改
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['product:category:delete']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      layout="total"
      :total="totalCount">
    </el-pagination>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="980px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="130px">
        <el-row> 
          <el-col :span="12">
            <el-form-item label="上级分类ID" prop="parentId">
              <el-input v-model="form.parentId" placeholder="请输入分类名称" :disabled="!!form.id"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分类ID" prop="categoryId">
              <el-input v-model="form.categoryId" placeholder="请输入分类名称" :disabled="!!form.id"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row> 
          <el-col :span="12">
            <el-form-item label="分类名称" prop="categoryName">
              <el-input v-model="form.categoryName" placeholder="请输入分类名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分类排序" prop="orderSort">
              <el-input-number v-model="form.orderSort" controls-position="right" :min="0" />
              <span> 值越小，排序越靠前</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="isProject"> 
          <el-col :span="12">
            <el-form-item label="经济分类编码" prop="economyClass">
              <el-input v-model="form.economyClass" placeholder="请输入经济分类编码" :maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="固资经济分类编码" prop="economyClass2">
              <el-input v-model="form.economyClass2" placeholder="请输入固资经济分类编码" :maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row> 
          <el-col :span="12">
            <el-form-item label="分类图标" prop="icon">
              <div style="margin: 0 0 10px">
                <el-input v-model="form.icon" placeholder="请输入分类图标地址" />
              </div>
              <imageUpload v-model="form.icon" :limit="1"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="H5分类图标" prop="iconH5">
              <div style="margin: 0 0 10px">
                <el-input v-model="form.iconH5" placeholder="请输入H5分类图标地址" />
              </div>
              <imageUpload v-model="form.iconH5" :limit="1"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="开启状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.PRODUCT_CATEGORY_STATUS)"
                      :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <ProductCategoryTree ref="productCategoryTree" @on-select="handleSelectProductCategory" />

    <!-- 商品分类导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="600px" append-to-body>
      <div class="flex-vertical-center">
        <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
          :action="upload.url" :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip text-center" slot="tip">
            <span>仅允许导入xls、xlsx格式文件，建议单次导入不超过5000条</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="downloadImportTemplate">下载模板</el-link>
          </div>
        </el-upload>
        <ImportAlert v-if="upload.open" ref="importAlert" @on-complete="handleQuery"></ImportAlert>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="importLoading" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import * as api from "@/api/mall/product/category.js"
import ProductCategoryTree from "@/components/ProductCategoryTree"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"
import {getBaseHeader} from "@/utils/request"
import ImportAlert from '@/components/AsyncTaskAlert/import'
import ImageUpload from '@/components/ImageUpload';
import { configMixins } from '@/views/mall/config/components/configMixin.js'
export default {
  name: "ProductCategory",
  mixins: [ configMixins ],
  components: {
    Treeselect, ProductCategoryTree, ImportAlert, ImageUpload
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      originalList: [],
      // 分类列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        name: null,
        categoryId: null,
        haveEconomyClass: null,
        economyClass: null,
      },
      // 表单参数
      form: {},
      totalCount: 0,
      // 表单校验
      rules: {
        parentId: [{required: true, message: "请选择上级分类", trigger: "blur"}],
        name: [{required: true, message: "分类名称不能为空", trigger: "blur"}],
        icon: [{type: 'url', message: "图标地址格式不正确", trigger: "blur"}],
        sort: [{required: true, message: "分类排序不能为空", trigger: "blur"}],
        status: [{required: true, message: "状态不能为空", trigger: "blur"}]
      },
      statusLoading: false,
      importLoading: false,
      // 导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/product/category/import'
      },
    };
  },
  created() {
    this.getRootList();
  },
  methods: {
    async getTotalCount() {
      let res = await api.getCategoryTotalCount()
      if(res.code === 0) {
        this.totalCount = res.data
      }
    },
    /** 查询列表 */
    getRootList() {
      this.loading = true;
      // 处理查询参数
      let params = {...this.queryParams};
      // 执行查询
      api.getCategoryRootList(params).then(response => {
        this.originalList = response.data
        this.list = this.handleTree(response.data, "categoryId", "parentId");
        this.list.forEach(item => {
          item.hasChildren = true
        })
        console.log('nodes===', this.list)
        this.loading = false;
      });
      this.getTotalCount()
    },
    getChildrenList(row, treeNode, resolve) {
      // 处理查询参数
      let params = {
        parentCategoryId: row.categoryId
      };
      // 执行查询
      api.getCategoryChildTreeList(params).then(response => {
        this.originalList = response.data
        let nodes = this.handleTree(response.data, "categoryId", "parentId");
        nodes.forEach(item => {
          item.hasChildren = item.childCategoryList && item.childCategoryList.length > 0
        })
        console.log('nodes===', nodes)
        resolve(nodes)
      });
    },
    /** 查询列表 */
    getSearchList() {
      this.loading = true;
      // 处理查询参数
      let params = {...this.queryParams};
      // 执行查询
      api.getProductCategoryList(params).then(response => {
        this.originalList = response.data
        this.totalCount = response.data.length
        this.list = this.handleTree(response.data, "categoryId", "parentId");
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        parentId: undefined,
        categoryName: undefined,
        icon: undefined,
        iconH5: undefined,
        categoryId: undefined,
        economyClass: undefined,
        economyClass2: undefined,
        orderSort: 0,
        categoryLevel: undefined,
        status: 1
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      let searchMode = false
      if(this.queryParams.name || this.queryParams.categoryId || (this.queryParams.haveEconomyClass || this.queryParams.haveEconomyClass == false) || this.queryParams.economyClass) {
        searchMode = true
      }
      if(searchMode) {
        this.getSearchList()
      } else {
        this.getRootList();
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false
      this.isExpandAll = !this.isExpandAll
      setTimeout(() => {
        this.refreshTable = true
      }, 200)
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.categoryName,
        children: node.children
      };
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      if(row) this.form.parentId = row.categoryId
      this.open = true;
      this.title = "手动新增分类";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      api.getProductCategory(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改商品分类";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 新增的提交
        if (!this.form.id) {
          api.createProductCategory(this.form).then(response => {
            this.$modal.msgSuccess("保存成功")
            this.open = false
            this.handleQuery()
          })
        } else 
          // 修改的提交
          api.updateProductCategory(this.form).then(response => {
            this.$modal.msgSuccess("修改成功")
            this.open = false
            this.handleQuery()
        })
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.categoryId;
      this.$modal.confirm('是否确认删除分类编号为"' + id + '"的数据项?').then(function () {
        return api.deleteProductCategory(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    // 状态修改
    handleStatusChange(row) {
      if(this.statusLoading) {
        this.$modal.msgWarning("正在处理中，请稍后重试")
        row.status = [1,0][row.status]
        return
      }
      this.statusLoading = true
      let text = row.status === 1 ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '"分类吗?').then(function() {
          let params = { id: row.id, status: row.status }
          return api.updateProductCategoryStatus(params);
        }).then(() => {
          this.$modal.msgSuccess(text + "成功，可能在1分钟左右生效");
        }).catch(function() {
          row.status = [1,0][row.status]
        }).finally(() => {
          this.statusLoading = false
        });
    },
    addProductCategory() {
      this.$refs.productCategoryTree.show()
    },
    async handleSelectProductCategory(nodes) {
      console.log('nodes===', nodes)
      if(nodes && nodes.length) {
        let ids = nodes.map(item => item.categoryId)
        let params = { categoryIdList: ids }
        let res = await api.cloneProductCategory(params)
        if(res.code === 0) {
          this.$modal.msgSuccess("添加成功");
          this.handleQuery()
        } else {
          this.$modal.msgSuccess(res.msg);
        } 
      }
      
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "商品分类导入";
      this.importLoading = false
      this.upload.open = true;
      this.$nextTick(() => {
        this.$refs.upload.clearFiles();
      })
    },
    /** 下载模板操作 */
    downloadImportTemplate() {
      api.getImportTemplate().then(response => {
        this.$download.excel(response, '商品分类导入模板.xls');
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.importLoading = false
      if (response.code !== 0) {
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        this.$modal.msgError(response.msg)
        return;
      }
      // this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$refs.importAlert.init2(response.data)
    },
    // 提交上传文件
    submitFileForm() {
      let files = this.$refs.upload.uploadFiles
      if(!files.length) {
        this.$modal.msg("请上传导入文件")
        return
      }

      this.importLoading = true
      this.$refs.upload.submit()
    }
  }
};
</script>