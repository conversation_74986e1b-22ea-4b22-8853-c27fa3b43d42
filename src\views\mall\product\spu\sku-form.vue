<template>
  <div class="app-container" v-show="open">
    <el-dialog title="修改商品SKU库存价格" :visible.sync="open" @close="close" width="1200px">
      <el-form ref="ratesForm" :model="ratesForm" v-loading="loading">
        <el-form-item label="修改字段">
          <el-radio-group v-model="editType">
            <el-radio :label="1">价格</el-radio>
            <el-radio :label="2">库存</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
            <el-table :data="ratesForm.rates" :border="true" style="width: 100%" ref="ratesTable">
              <template>
                <el-table-column :key="index" v-for="(item, index) in dynamicSpec" :label="item" width="140">
                  <template v-slot="scope">
                    <el-input v-if="scope.row.specValueList" :value="getSpecValue(item, scope.row.specValueList)" disabled/>
                  </template>
                </el-table-column>
              </template>
              <el-table-column label="SKU平台编码" key="92"  >
                <template v-slot="scope">
                  <el-form-item>
                    <el-input :value="scope.row.id" disabled/>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="SKU外部编码" key="921" >
                <template v-slot="scope">
                  <el-form-item v-if="scope.row.supplierType === 1">
                    <el-input :value="scope.row.skuInnerId" disabled/>
                  </el-form-item>
                  <span v-else>--</span>
                </template>
              </el-table-column>
              <template v-if="editType === 1">
                <el-table-column label="市场价(元)" key="93" width="180">
                  <template v-slot="scope">
                    <el-form-item>
                      <el-input :value="scope.row.marketPrice" disabled/>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="原销售价(元)"  key="94" width="140">
                  <template v-slot="scope">
                    <el-form-item>
                      <el-input :value="scope.row.salePrice" disabled />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="新销售价(元)" :render-header="addRedStar" key="95" width="240">
                  <template v-slot="scope">
                    <el-form-item :prop="'rates.'+ scope.$index + '.newSalePrice'"
                                  :rules="[{required: true, trigger: 'blur'}]">
                      <el-input-number v-model="scope.row.newSalePrice" controls-position="right" :precision="2" :max="999999.99" />
                    </el-form-item>
                  </template>
                </el-table-column>
              </template>
              <template v-if="editType === 2">
                <el-table-column label="预警库存" key="96" width="120">
                  <template v-slot="scope">
                    <el-form-item>
                      <el-input :value="scope.row.warnStock" disabled />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="库存" key="97" width="120">
                  <template v-slot="scope">
                    <el-form-item>
                      <el-input :value="scope.row.stock" disabled />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="调整方式" :render-header="addRedStar" key="98" width="160">
                  <template v-slot="scope">
                    <el-form-item :prop="'rates.'+ scope.$index + '.adjustType'" :rules="[{required: true, trigger: 'change'}]">
                      <el-radio-group v-model="scope.row.adjustType">
                        <el-radio :label="1">增加</el-radio>
                        <el-radio :label="2">减少</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="调整数量" :render-header="addRedStar" key="99" width="180">
                  <template v-slot="scope">
                    <el-form-item :prop="'rates.'+ scope.$index + '.adjustStock'" :rules="[{required: true, trigger: 'blur'}]">
                      <el-input-number v-model="scope.row.adjustStock" controls-position="right" :max="999999" style="width: 150px"/>
                    </el-form-item>
                  </template>
                </el-table-column>
              </template>
            </el-table>
          </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
        <el-button @click="close">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as skuApi from "@/api/mall/product/sku.js"
export default {
  name: 'ProductSkuForm',
  props: {
    spuId: {
      type: [Number, String],
      default: () => null
    },
    skuId: {
      type: [Number, String],
      default: () => null
    },
    open: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      editType: 1,
      // 价格库存
      ratesForm: {
        // 规格明细
        rates: [{}]
      },
      dynamicSpec: []
    }
  },
  watch: {
    open(newVal, oldVal) {
      if(newVal) {
        this.loadSkuList()
      }
    }
  },
  methods: {
    close() {
      this.$emit('update:open', false)
    },
    async loadSkuList() {
      if(!this.spuId) return
      this.loading = true
      let res = await skuApi.getSkuListBySpu({spuId: this.spuId})
      if(res.code === 0) {
        if(this.skuId) {
          res.data = (res.data || []).filter(item => item.id === this.skuId)
        }
        this.ratesForm.rates = res.data || []
        let specDic = []
        this.ratesForm.rates.forEach((sku, index) => {
          sku.specValueList.map(specValue => {
            if(!specDic.includes(specValue.specName)) {
              specDic.push(specValue.specName)
            }
          })
          let cloneSku = JSON.parse(JSON.stringify(sku))
          cloneSku.adjustType = 1
          cloneSku.adjustStock = 0
          cloneSku.newSalePrice = cloneSku.salePrice

          this.$set(this.ratesForm.rates, index, cloneSku)
        })
        this.dynamicSpec = specDic
      }
      this.loading = false
    },
    getSpecValue(specName, specValueList) {
      let sv = specValueList.find(item => item.specName === specName)
      if(sv) return sv.specValue
      return ''
    },
    // 必选标识
    addRedStar(h, {column}) {
      return [
        h('span', {style: 'color: #F56C6C'}, '*'),
        h('span', ' ' + column.label)
      ];
    },
    submitForm() {
      this.$refs.ratesForm.validate((valid) => {
        console.log('ratesForm:', this.ratesForm)
        if (!valid) {
          return;
        }
        let rates = JSON.parse(JSON.stringify(this.ratesForm.rates));
        let skus = []
        rates.forEach(rate => {
          if(this.editType === 1) {
            skus.push({
              id: rate.id,
              spuId: rate.spuId,
              salePrice: rate.newSalePrice
            })
          } else {
            skus.push({
              id: rate.id,
              spuId: rate.spuId,
              adjustStock: rate.adjustStock * (rate.adjustType === 1 ? 1 : -1)
            })
          }
        })

        let func = this.editType === 1 ? skuApi.updateSkuPrice : skuApi.updateSkuStock
        this.submitLoading = true
        func(skus).then(res => {
          if(res.code === 0) {
            this.$modal.msgSuccess("保存成功");
            this.$emit("update-success");
            this.close()
          }
        }).finally(() => {
          this.submitLoading = false
        })
      })
    },
    cancel() {
      this.close()
    }
  }
}
</script>

<style lang="scss">

</style>