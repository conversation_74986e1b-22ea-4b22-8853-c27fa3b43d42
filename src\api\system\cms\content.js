import request from '@/utils/request'

// 创建CMS内容
export function createCmsContent(data) {
  return request({
    url: '/system/cms-content/create',
    method: 'post',
    data: data
  })
}

// 更新CMS内容
export function updateCmsContent(data) {
  return request({
    url: '/system/cms-content/update',
    method: 'put',
    data: data
  })
}

// 更新CMS内容排序
export function updateCmsContentSort(data) {
  return request({
    url: '/system/cms-content/update-sort',
    method: 'put',
    data: data
  })
}

// 更新CMS内容发布状态
export function updateCmsContentPublish(data) {
  return request({
    url: '/system/cms-content/update-publish',
    method: 'put',
    data: data
  })
}

// 删除CMS内容
export function deleteCmsContent(id) {
  return request({
    url: '/system/cms-content/delete?id=' + id,
    method: 'delete'
  })
}

// 获得CMS内容
export function getCmsContent(id) {
  return request({
    url: '/system/cms-content/get?id=' + id,
    method: 'get'
  })
}

// 获得CMS内容分页
export function getCmsContentPage(query) {
  return request({
    url: '/system/cms-content/page',
    method: 'get',
    params: query
  })
}

// 导出CMS内容 Excel
export function exportCmsContentExcel(query) {
  return request({
    url: '/system/cms-content/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
