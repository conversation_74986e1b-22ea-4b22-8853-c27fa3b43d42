<template>
  <div class="app-container">
    <el-tabs v-model="activeGroup" @tab-click="handleClick" type="card" addable @tab-add="handleTabAdd">
      <el-tab-pane v-for="(gi, index) in configGroups" :key="index" :label="gi.name" :name="gi.id + ''"></el-tab-pane>
      <el-alert v-if="currentGroup.memo" :title="currentGroup.memo" type="info" show-icon :closable="false"></el-alert>
      <div style="margin: 10px 0;" v-if="currentGroup.id">
        <!-- <el-button type="primary" plain icon="el-icon-edit" size="mini" @click="handleUpdate1">修改配置分组</el-button> -->
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd2">增加配置项</el-button>
      </div>
      <el-table v-if="comItems && comItems.length" :data="comItems" style="width: 100%">
        <el-table-column prop="name" label="名称"></el-table-column>
        <el-table-column prop="itemKey" label="配置键"></el-table-column>
        <el-table-column prop="itemValue" label="配置值"></el-table-column>
        <el-table-column prop="createTime" label="创建时间">
          <template v-slot="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="memo" label="备注"></el-table-column>
        <el-table-column prop="id" label="操作">
          <template v-slot="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate2(scope.row)"
                      v-hasPermi="['system:tenant-config-item:update']">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete2(scope.row)"
                      v-hasPermi="['system:tenant-config-item:delete']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-else></el-empty>
    </el-tabs>

    <!-- 添加或修改分组对话框 -->
    <el-dialog :title="title1" :visible.sync="open1" width="500px" append-to-body>
      <el-form ref="form1" label-position="top" :model="form1" :rules="rules1" label-width="80px">
        <el-form-item label="分组名称" prop="name">
          <el-input v-model="form1.name" maxlength="30" placeholder="请输入分组名称" />
        </el-form-item>
        <el-form-item label="分组备注" prop="memo">
          <el-input v-model="form1.memo" maxlength="200" type="textarea" placeholder="请输入分组备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm1">确 定</el-button>
        <el-button @click="cancel1">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改配置项对话框 -->
    <el-dialog :title="title2" :visible.sync="open2" width="500px" append-to-body>
      <el-form ref="form2" label-position="top" :model="form2" :rules="rules2" label-width="80px">
        <el-form-item label="配置项名称" prop="name">
          <el-input v-model="form2.name" maxlength="50" placeholder="请输入配置项名称" />
        </el-form-item>
        <el-form-item label="配置项键" prop="itemKey">
          <el-input v-model="form2.itemKey" maxlength="100" placeholder="请输入配置项键" />
        </el-form-item>
        <el-form-item label="配置项值" prop="itemValue">
          <el-input v-model="form2.itemValue" maxlength="250" placeholder="请输入配置项值" />
        </el-form-item>
        <el-form-item label="配置项备注" prop="memo">
          <el-input v-model="form2.memo" maxlength="200" type="textarea" placeholder="请输入配置项备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm2">确 定</el-button>
        <el-button @click="cancel2">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import * as api from "@/api/system/tenantConfig";

export default {
  name: "SystemTenantConfig",
  components: {
  },
  data() {
    return {
      activeGroup: '',
      configGroups: [],
      configItems: [],
      // 配置分组相关
      title1: '',
      open1: false,
      form1: {
        id: undefined,
        name: '',
        memo: ''
      },
      rules1: {
        name: [
          { required: true, trigger: 'blur', message: '请输入分组名称' }
        ]
      },
      // 配置项相关
      title2: '',
      open2: false,
      form2: {
        id: undefined,
        name: '',
        memo: '',
        itemKey: '',
        itemValue: '',
        groupId: undefined
      },
      rules2: {
        name: [
          { required: true, trigger: 'blur', message: '请输入配置项名称' }
        ],
        itemKey: [
          { required: true, trigger: 'blur', message: '请输入配置项键' }
        ],
        itemValue: [
          { required: true, trigger: 'blur', message: '请输入配置项值' }
        ]
      },

    };
  },
  computed: {
    comItems() {
      if(this.configItems) {
        return this.configItems.filter(item => item.groupId === parseInt(this.activeGroup))
      }
      return []
    },
    currentGroup() {
      return this.configGroups.find(item => item.id === parseInt(this.activeGroup)) || {}
    }
  }, 
  created() {
    this.loadGroupList()
    this.loadItemList()
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    },
    async loadGroupList() {
      let resp = await api.getConfigGroupList();
      console.log('res===', resp);
      this.configGroups = resp.data || [];
      if(this.activeGroup === '0' && this.configGroups.length) {
        this.activeGroup = this.configGroups[0].id + ''
      }
    },
    async loadItemList() {
      let resp = await api.getConfigItemList();
      console.log('res===', resp);
      this.configItems = resp.data;
    },
    handleTabAdd() {
      this.handleAdd1();
    },
    /** 新增按钮操作 */
    handleAdd1() {
      this.reset1();
      this.open1 = true;
      this.title1 = "添加配置分组";
    },
    /** 修改按钮操作 */
    handleUpdate1() {
      this.reset1();
      let row = this.configGroups.find(item => item.id === parseInt(this.activeGroup))
      const id = row.id
      api.getConfigGroup(id).then(response => {
        this.form1 = response.data;
        this.open1 = true;
        this.title1 = "修改配置分组";
      });
    },
    reset1() {
      this.form1 = {
        id: undefined,
        name: '',
        memo: ''
      };
      this.resetForm("form1");
    },
    submitForm1() {
      this.$refs["form1"].validate(valid => {
        if (valid) {
          if (this.form1.id !== undefined) {
            api.updateConfigGroup(this.form1).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open1 = false;
              this.loadGroupList();
            });
          } else {
            api.createConfigGroup(this.form1).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open1 = false;
              this.activeGroup = response.data + ''
              this.loadGroupList();
            });
          }
        }
      });
    },
    cancel1() {
      this.open1 = false;
      this.reset1();
    },
    /** 删除按钮操作 */
    handleDelete1(name) {
      let row = this.configGroups.find(item => item.id === parseInt(name))
      const ids = row.id;
      this.$modal.confirm(`你是否删除分组:${row.name}?`).then(function() {
          return api.deleteConfigGroup(ids);
        }).then(() => {
          this.activeGroup = ''
          this.loadGroupList();
          this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    // 配置项相关
    /** 新增按钮操作 */
    handleAdd2() {
      this.reset2();
      this.form2.groupId = this.activeGroup
      this.open2 = true;
      this.title2 = "添加配置项";
    },
    /** 修改按钮操作 */
    handleUpdate2(row) {
      this.reset2();
      const id = row.id
      api.getConfigItem(id).then(response => {
        this.form2 = response.data;
        this.open2 = true;
        this.title2 = "修改配置项";
      });
    },
    reset2() {
      this.form2 = {
        id: undefined,
        name: '',
        memo: '',
        itemKey: '',
        itemValue: '',
        groupId: undefined
      };
      this.resetForm("form2");
    },
    submitForm2() {
      this.$refs["form2"].validate(valid => {
        if (valid) {
          if (this.form2.id !== undefined) {
            api.updateConfigItem(this.form2).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open2 = false;
              this.loadItemList();
            });
          } else {
            api.createConfigItem(this.form2).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open2 = false;
              this.loadItemList();
            });
          }
        }
      });
    },
    cancel2() {
      this.open2 = false;
      this.reset2();
    },
    /** 删除按钮操作 */
    handleDelete2(row) {
      const ids = row.id;
      this.$modal.confirm(`你是否删除配置项:${row.name}?`).then(function() {
          return api.deleteConfigItem(ids);
        }).then(() => {
          this.loadItemList();
          this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

  }
};
</script>
