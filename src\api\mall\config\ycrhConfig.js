import request from '@/utils/request'


// 查询配置
export function getYcrhConfig() {
  return request({
    url: '/mall/ycrh-config/get',
    method: 'get'
  })
}

// 更新配置
export function updateYcrhConfig(data) {
  return request({
    url: '/mall/ycrh-config/update',
    method: 'put',
    data: data
  })
}

// 更新配置
export function createYcrhConfig(data) {
  return request({
    url: '/mall/ycrh-config/create',
    method: 'post',
    data: data
  })
}

// 清除配置
export function cleanYcrhConfig() {
  return request({
    url: '/mall/ycrh-config/clean',
    method: 'post'
  })
}

// 业财通用接口调用
export function requestYcrhApi(data) {
  return request({
    url: '/mall/ycrh-config/ycrh-api',
    method: 'post',
    data
  })
}
