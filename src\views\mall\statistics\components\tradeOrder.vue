<template>
  <div class="app-container">
    <el-alert title="展示不超过30天范围内的订单信息" type="info" style="margin-bottom: 24px;"></el-alert>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="100px">
      <el-form-item label="订单类型" prop="orderType">
        <el-select v-model="queryParams.orderType" placeholder="订单类型" clearable style="width: 240px">
          <el-option label="新增订单" value="1"></el-option>
          <el-option label="确认订单" value="2"></el-option>
          <el-option label="发货订单" value="3"></el-option>
          <el-option label="完成订单" value="4"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日期范围" prop="createTime">
        <el-date-picker v-model="queryParams.queryDate" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange" :picker-options="pickerOptions"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" @blur="blurDate" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <div id="order-echart" class="order-echart"></div>
    <div id="sales-echart" style="margin-top: 16px;" class="order-echart"></div>
  </div>
</template>

<script>
import * as api from "@/api/mall/statistics/trade";
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  data() {
    const that = this
    return {
      chart: null,
      chart1: null,
      queryParams: {
        orderType: '1',
        queryDate: []
      },
      pickerMinDate: '',
      pickerOptions: {  
        onPick: obj => {
          that.pickerMinDate = new Date(obj.minDate).getTime()
        },
        disabledDate(time) {  
          // 禁用今天和之后的日期，跨度不能超过30天
          if (new Date().toDateString() === time.toDateString()) {
            return true
          }
          if (that.pickerMinDate) {
            const day1 = 30 * 24 * 3600 * 1000 // 时间跨度不超过30天
            const minTime = that.pickerMinDate - day1
            const maxTime = that.pickerMinDate + day1
            return !(time.getTime() > minTime && time.getTime() < maxTime) || time.getTime() > Date.now()
          }
          return time.getTime() > Date.now()
        }
      }
    };
  },
  methods: {
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        queryDate: [],
        orderType: ''
      }
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
      if (this.chart1) {
        this.chart1.dispose()
        this.chart1 = null
      }
    },
    blurDate() {
      this.pickerMinDate = ''
    },
    getList() {
      if (!this.queryParams.orderType) {
        this.$message.error('请选择订单类型')
        return
      }
      if (!this.queryParams.queryDate || this.queryParams.queryDate.length != 2) {
        this.$message.error('请选择日期范围')
        return
      }
      api.getSummarizeOrderStatsByDays({
        type: this.queryParams.orderType,
        startTime: this.queryParams.queryDate[0],
        endTime: this.queryParams.queryDate[1]
      }).then(response => {
        if (response.code === 0) {
          if (response.data && response.data.length > 0) {
            this.initChart(response.data)
          } else {
            if (this.chart) {
              this.chart.dispose()
              this.chart = null
              this.$message.info('当前范围内没有数据')
            }
            if (this.chart1) {
              this.chart1.dispose()
              this.chart1 = null
              this.$message.info('当前范围内没有数据')
            }
          }
        }
      })
    },
    initChart(data) {
      const chartDom = document.getElementById('order-echart');
      this.chart = echarts.init(chartDom, 'macarons')
      this.chart.setOption({
        legend: {
          data: ['订单数量']
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        grid: {
          left: '1%',
          right: '1%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.map(x => x.date)
        },
        yAxis: {
          type: 'value'
        },
        series: [
          { 
            name: '订单数量',
            type: 'bar',
            data: data.map(x => x.orderCount)
          }
        ]
      })
      const chartDom1 = document.getElementById('sales-echart');
      this.chart1 = echarts.init(chartDom1, 'macarons')
      this.chart1.setOption({
        legend: {
          data: ['订单金额']
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        grid: {
          left: '1%',
          right: '1%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.map(x => x.date)
        },
        yAxis: {
          type: 'value'
        },
        series: [
          { 
            name: '订单金额',
            type: 'bar',
            data: data.map(x => x.orderAmount),
            itemStyle: {  
              color: '#ffa500'
            }
          }
        ]
      })
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    if (this.chart1) {
      this.chart1.dispose()
      this.chart1 = null
    }
  }
};
</script>

<style lang="scss" scoped>
.order-echart {
  width: 100%;
  height: 400px;
}
</style>