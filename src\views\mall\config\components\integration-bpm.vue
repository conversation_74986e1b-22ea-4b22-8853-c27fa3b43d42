<template>
  <div class="app-container">
    <el-empty v-if="!configInfo.id && formMode === 'init'">
      <el-button type="text" icon="el-icon-plus" @click="beginConfig">开始配置</el-button>
    </el-empty>
    <div v-if="configInfo.id && formMode === 'init'">
      <el-descriptions title="配置信息" :column="1" :border="true">
        <template slot="extra">
          <el-button type="primary" size="small" @click="editConfig">修改</el-button>
        </template>
        <el-descriptions-item label="系统名称">{{ configInfo.sysName || '--' }} / {{ configInfo.sysCode || '--' }}</el-descriptions-item>
        <el-descriptions-item label="状态"><dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="configInfo.status" /></el-descriptions-item>
        <el-descriptions-item label="扩展参数1">{{ configInfo.paramExt1 || '--' }}</el-descriptions-item>
        <el-descriptions-item label="扩展参数2">{{ configInfo.paramExt2 || '--' }}</el-descriptions-item>
        <el-descriptions-item label="扩展参数3">{{ configInfo.paramExt3 || '--' }}</el-descriptions-item>
        
      </el-descriptions>
    </div>
    <el-form v-if="formMode === 'edit'" ref="configForm" :model="configForm" :rules="configRules"
      label-width="200px" style="width:700px">
      <el-form-item label="对接系统" prop="sysCode">
        <el-select v-model="configForm.sysCode" placeholder="请选择系统" style="width:100%">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MALL_INTEGRATION_BPM_TYPE)"
                  :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="configForm.status">
          <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
        </el-radio>
      </el-radio-group>
      </el-form-item>
      <el-form-item label="扩展参数1" prop="paramExt1">
        <el-input v-model="configForm.paramExt1" maxlength="50" placeholder="请输入扩展参数1" />
      </el-form-item>
      <el-form-item label="扩展参数2" prop="paramExt2">
        <el-input v-model="configForm.paramExt2" maxlength="50" placeholder="请输入扩展参数2" />
      </el-form-item>
      <el-form-item label="扩展参数3" prop="paramExt3">
        <el-input v-model="configForm.paramExt3" maxlength="50" placeholder="请输入扩展参数3" />
      </el-form-item>
      <el-form-item>
        <el-button type="default" @click="cancelForm">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">提 交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import * as api from "@/api/mall/config/bpmConfig";
export default {
  name: "MallBpmConfig",
  components: {},
  data() {
    return {
      configInfo: {},
      formMode: 'init',
      submitLoading: false,
      configForm: {
        id: undefined,
        sysName: '',
        sysCode: '',        
        status: 0,
        paramExt1: null,
        paramExt2: null,
        paramExt3: null
      },
      configRules: {
        sysCode: [
          { required: true, trigger: 'change', message: '请选择系统' }
        ]
      },
    }
  },
  computed: {
    sysName() {
      return this.getDictDataLabel(this.DICT_TYPE.MALL_INTEGRATION_BPM_TYPE, this.configForm.sysCode) || ''
    }
  },
  created() {
    this.loadConfig()
  },
  methods: {
    beginConfig() {
      this.formMode = 'edit'
    },
    async loadConfig() {
      let res = await api.getBpmConfig();
      this.configInfo = res.data || {}
    },
    editConfig() {
      this.formMode = 'edit'
      Object.assign(this.configForm, this.configInfo)
    },
    cancelForm() {
      this.formMode = 'init'
    },
    async doSubmit() {
      this.submitLoading = true
      try {
        this.configForm.sysName = this.sysName
        if (this.configForm.id !== undefined) {
          await api.updateBpmConfig(this.configForm)
          this.$modal.msgSuccess("修改成功");
        } else {
          await api.createBpmConfig(this.configForm)
          this.$modal.msgSuccess("保存成功");
        }
        this.submitLoading = false
        this.formMode = 'init'
        this.loadConfig()
      } catch(e) {
        this.$modal.msgError("保存失败，请稍后重试")
        this.submitLoading = false
      }
    },
    submitForm() {
      this.$refs["configForm"].validate(valid => {
        if (!valid) {
          return;
        }
        this.doSubmit()
      });
    }
  }
}
</script>

<style lang="scss" scoped></style>