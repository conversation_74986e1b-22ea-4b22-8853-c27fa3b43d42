<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="项目名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入项目名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="项目编号" prop="no">
        <el-input v-model="queryParams.no" placeholder="请输入项目编号" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="项目类型编号" prop="typeCode">
        <el-input v-model="queryParams.typeCode" placeholder="请输入项目类型编号" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="项目类型名称" prop="typeName">
        <el-input v-model="queryParams.typeName" placeholder="请输入项目类型名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="负责人工号" prop="chargeNo">
        <el-input v-model="queryParams.chargeNo" placeholder="请输入负责人工号" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="负责人姓名" prop="chargeName">
        <el-input v-model="queryParams.chargeName" placeholder="请输入负责人姓名" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="部门名称" prop="departmentName">
        <el-input v-model="queryParams.departmentName" placeholder="请输入部门名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="部门编号" prop="departmentNo">
        <el-input v-model="queryParams.departmentNo" placeholder="请输入部门编号" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['mall:zc-project:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-refresh" size="mini" @click="handleSyncAll"
                   v-hasPermi="['mall:zc-project:collect']">同步全部</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-refresh" size="mini" @click="syncSingle"
                   v-hasPermi="['mall:zc-project:collect']">同步单用户</el-button>

      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['mall:zc-project:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list" 
      show-summary
      :summary-method="getSummaries"
      @sort-change="handleSortChange"
      :default-sort = "{prop: 'createTime', order: 'descending'}">
      <el-table-column label="项目名称" align="center" prop="name" show-overflow-tooltip/>
      <el-table-column label="项目编号" align="center" prop="no" />
      <el-table-column label="项目类型" align="center" prop="typeName" >
        <template v-slot="scope">
          <span>{{ scope.row.typeName }} / {{ scope.row.typeCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="负责人" align="center" prop="chargeName">
        <template v-slot="scope">
          <span>{{ scope.row.chargeName }} / {{ scope.row.chargeNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="部门" align="center" prop="departmentName">
        <template v-slot="scope">
          <span>{{ scope.row.departmentName }} / {{ scope.row.departmentNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="余额" align="center" prop="balance" sortable="custom"> 
        <template v-slot="scope">
          <span>{{ formatMoneyV2(scope.row.balance) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180" sortable="custom">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-eye" @click="handleView(scope.row)"
                     v-hasPermi="['mall:zc-project:query']">查看</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['mall:zc-project:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['mall:zc-project:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="800px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入项目名称" :maxlength="100"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目编号" prop="no">
              <el-input v-model="form.no" placeholder="请输入项目编号" :maxlength="50"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目类型编号" prop="typeCode">
              <el-input v-model="form.typeCode" placeholder="请输入项目类型编号" :maxlength="50"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目类型名称" prop="typeName">
              <el-input v-model="form.typeName" placeholder="请输入项目类型名称" :maxlength="100"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="负责人工号" prop="chargeNo">
              <el-input v-model="form.chargeNo" placeholder="请输入负责人工号" :maxlength="50"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人姓名" prop="chargeName">
              <el-input v-model="form.chargeName" placeholder="请输入负责人姓名" :maxlength="100"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="部门名称" prop="departmentName">
              <el-input v-model="form.departmentName" placeholder="请输入部门名称" :maxlength="100"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门编号" prop="departmentNo">
              <el-input v-model="form.departmentNo" placeholder="请输入部门编号" :maxlength="50"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="一级大类" prop="rootClass">
              <el-input v-model="form.rootClass" placeholder="请输入一级大类" :maxlength="500" />
            </el-form-item>
          </el-col>
          <el-col :span="12"></el-col>
        </el-row>
        
        <el-form-item label="允许经济分类" prop="yesEconomyClass">
          <el-input v-model="form.yesEconomyClass" placeholder="请输入允许经济分类" :maxlength="200" />
        </el-form-item>
        <el-form-item label="禁用经济分类" prop="noEconomyClass">
          <el-input v-model="form.noEconomyClass" placeholder="请输入禁用经济分类" :maxlength="200"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-drawer
      title="项目详情"
      :visible.sync="drawerVisible"
      direction="rtl"
      class="cust-drawer"
      size="1200px">
      <ProjectDetail ref="projectDetail"></ProjectDetail>
    </el-drawer>

  </div>
</template>

<script>
import * as api from "@/api/mall/project/project";
import ProjectDetail from "./project-detail.vue";
export default {
  name: "MallProjectList",
  components: {
    ProjectDetail
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      drawerVisible: false,
      // 总条数
      total: 0,
      // 直采项目经费卡列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        sortType: 10,
        name: null,
        no: null,
        typeCode: null,
        typeName: null,
        chargeNo: null,
        chargeName: null,
        departmentName: null,
        departmentNo: null,
        createTime: [],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: "项目名称不能为空", trigger: "blur" }],
        no: [{ required: true, message: "项目编号不能为空", trigger: "blur" }],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      api.getZcProjectPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.getBalance()
        this.loading = false;
      });
    },
    getBalance() {
      if(!this.list || !this.list.length) {
        return
      }
      let params = this.list.map(item => {
        return {
          departmentNo: item.departmentNo,
          projectNo: item.no,
          chargeNo: item.chargeNo
        }
      })
      api.batchQueryBalance(params).then(res => {
        if(res.data && res.data.length) {
          this.list.forEach(p => {
            let pbalance = res.data.find(pb => p.no === pb.xmbh && p.departmentNo === pb.bmbh)
            if(pbalance) {
              p.balance = pbalance.xmye
            }
          })
          this.list = JSON.parse(JSON.stringify(this.list))
          this.updateBalance()
        }
      })
    },
    async updateBalance() {
      if(!this.list || !this.list.length) {
        return
      }

      let data = this.list.filter(item => item.balance || item.balance === 0).map(item => {
        return {
          id: item.id,
          balance: item.balance
        }
      })
      api.updateZcProjectBalance(data)
    },
    handleSortChange({ prop, order }) {
      this.queryParams.sortType = 10
      if(prop === 'createTime' && order === 'descending') {
        this.queryParams.sortType = 10
      } else if(prop === 'createTime' && order === 'ascending') {
        this.queryParams.sortType = 11
      } else if(prop === 'balance' && order === 'descending') {
        this.queryParams.sortType = 20
      } else if(prop === 'balance' && order === 'ascending') {
        this.queryParams.sortType = 21
      }  
      console.log(prop, order)
      this.handleQuery()
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        no: undefined,
        typeCode: undefined,
        typeName: undefined,
        chargeNo: undefined,
        chargeName: undefined,
        departmentName: undefined,
        departmentNo: undefined,
        rootClass: undefined,
        yesEconomyClass: undefined,
        noEconomyClass: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加直采项目经费卡";
    },
    handleView(row) {
      this.drawerVisible = true
      this.$nextTick(() => {
        this.$refs.projectDetail.init(row)
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      api.getZcProject(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改直采项目经费卡";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.id != null) {
          api.updateZcProject(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        api.createZcProject(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除直采项目经费卡编号为"' + id + '"的数据项?').then(function() {
          return api.deleteZcProject(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有直采项目经费卡数据项?').then(() => {
          this.exportLoading = true;
          return api.exportZcProjectExcel(params);
        }).then(response => {
          this.$download.excel(response, '直采项目经费卡.xls');
          this.exportLoading = false;
        }).catch(() => {});
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      sums[4] = '合计'
      let s1 = 0
      this.list.forEach(item => {
        s1 += item.balance
      })
      sums[5] = this.formatMoneyV2(s1)

      return sums;
    },
    async handleSyncAll() {
      let res = await api.collectZcProject()
      this.$modal.msgSuccess("经费卡项目已经开始同步，此操作会较耗时.");
    },
    syncSingle() {
      this.$prompt('请输入员工工号', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^.{3,30}$/,
          inputErrorMessage: '员工工号不能为空且长度为3-30个字符'
        }).then(({ value }) => {
          this.handleSync(value)
        }).catch(() => {    
        });
    },
    async handleSync(userNo) {
      if(!userNo) {
        return
      }
      let res = await api.collectZcProject({userNo:userNo})
      this.$modal.msgSuccess("经费卡项目已经开始同步")
    }
  }
};
</script>
