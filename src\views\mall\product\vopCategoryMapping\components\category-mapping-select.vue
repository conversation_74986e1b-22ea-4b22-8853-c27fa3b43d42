<template>
  <div class="category-select">
    <el-dialog title="选择分类映射" width="100%" :visible.sync="dialogVisible"  :append-to-body="appendToBody" @close="close">
    <div style="display: flex; align-items: center; gap: 20px">
      <div style="flex: 1">
        <h4 style="margin-bottom: 10px">平台分类</h4>
        <CategorySelect ref="platformSelect" :extProps="extProps" :extParams="extParams" :panel="true" @change="handleChange"/>
      </div>

      <el-button type="primary" @click="handleBind">绑定分类</el-button>

      <div style="flex: 1">
        <h4 style="margin-bottom: 10px">京东分类</h4>
        <CategorySelect ref="jdSelect" :extProps="extProps" :extParams="extParams" :panel="true" type="jd" @change="handleJdChange"/>
      </div>

      <div style="flex: 1; border-left: 1px solid #eee; padding-left: 20px">
        <h4>绑定结果</h4>
        <div v-if="bindings.length > 0" style="margin-top: 10px; max-height: 400px; overflow-y: auto">
          <div v-for="(item, index) in bindings" :key="index" style="margin-bottom: 10px; padding: 10px; background: #f5f5f5; border-radius: 4px">
            <div>平台分类: {{ item.platformLabel }}</div>
            <div>京东分类: {{ item.jdLabel }}</div>
          </div>
        </div>
        <div v-else style="color: #999; margin-top: 10px">
          暂无绑定数据
        </div>
      </div>
    </div>


      <div slot="footer" class="dialog-footer">
        <el-button @click="close">取 消</el-button>
      </div>




    </el-dialog>
  </div>
</template>

<script>
import { createVopCategoryMapping } from "@/api/mall/product/vopCategoryMapping";
import CategorySelect from "@/views/mall/product/spu/components/category-select";
export default {
  name: 'CategoryMappingSelect',
  components: { CategorySelect },
    watch: {
    open(newVal) {
      this.dialogVisible = newVal;

      if (newVal) {
        // 重置所有数据
        this.bindings = [];
        this.platformLabel = '';
        this.jdLabel = '';
        this.labelString = '';
        this.jdLabelString = '';
        this.form = {
          lastCategoryId: undefined,
          lastCategoryName: undefined,
          fullCategoryId: undefined,
          fullCategoryName: undefined,
          vopLastCategoryId: undefined,
          vopLastCategoryName: undefined,
          vopFullCategoryId: undefined,
          vopFullCategoryName: undefined,
        };
        // 重新加载分类数据
      this.$nextTick(() => {
        if (this.$refs.platformSelect && this.$refs.platformSelect.loadData) {
          this.$refs.platformSelect.loadData();
        }
        if (this.$refs.jdSelect && this.$refs.jdSelect.loadData) {
          this.$refs.jdSelect.loadData();
        }
      });
      }
    }
  },
  data() {
    return {
      bindings: [],
      platformLabel: '',
      jdLabel: '',
      labelString: '',
      jdLabelString: '',
      form: {},
      dialogVisible: false

    }
  },
  props: {
    open: {
      type: Boolean,
      default: () => false
    },
    extProps: {
      type: Object,
      default: () => {}
    },
    extParams: {
      type: Object,
      default: () => {}
    },
    appendToBody: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    close() {
      this.$emit('update:open', false);
      // 清空所有数据
      this.bindings = [];
      this.platformLabel = '';
      this.jdLabel = '';
      this.labelString = '';
      this.jdLabelString = '';
      // 如果有form对象也需要清空
      this.form = {
        lastCategoryId: undefined,
        lastCategoryName: undefined,
        fullCategoryId: undefined,
        fullCategoryName: undefined,
        vopLastCategoryId: undefined,
        vopLastCategoryName: undefined,
        vopFullCategoryId: undefined,
        vopFullCategoryName: undefined,
      }
      this.$emit('refresh');
    },

    //平台分类绑定京东分类
    handleBind() {
      if (this.platformLabel && this.jdLabel) {
        createVopCategoryMapping(this.form).then(response => {
          this.$modal.msgSuccess("绑定成功");
          this.bindings.unshift({
            platformLabel: this.platformLabel,
            jdLabel: this.jdLabel
          });
        }).catch(error => {
          this.$modal.msgError(error.message || "绑定失败");
        });
      }
      this.$emit('refresh');
    },
    handleChange(val, pathInfo) {
      this.labelString = pathInfo.labels.join('/')
      this.platformLabel = this.labelString
      this.form.fullCategoryId = val.join('-')
      this.form.fullCategoryName= pathInfo.labels.join('-')
      this.form.lastCategoryId = val[ val.length -1]
      this.form.lastCategoryName = pathInfo.labels[ pathInfo.labels.length - 1 ]
    },
    handleJdChange(val, pathInfo){
      this.jdLabelString = pathInfo.labels.join('/')
      this.jdLabel = this.jdLabelString
      this.form.vopLastCategoryId = val[val.length - 1]
      this.form.vopLastCategoryName = pathInfo.labels[pathInfo.labels.length - 1]
      this.form.vopFullCategoryId = val.join('-')
      this.form.vopFullCategoryName = pathInfo.labels.join('-')
    },
    cancel() {
      this.close()
    }
  }
}
</script>

<style lang="scss">
.category-select {
  .el-cascader-menu__wrap {
    height: 100%;
  }
}
.category-mapping-container {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.path-display {
  padding: 8px 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  color: #606266;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  align-self: center;
}
</style>
