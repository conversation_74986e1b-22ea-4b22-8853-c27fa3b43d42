<template>
  <div>
    <el-card class="box-card" v-loading="loading">
      <div slot="header" class="clearfix">
        <span class="title">收款账号</span>
        <el-button style="float: right; padding: 3px 0" type="text" v-hasPermi="['mall:supplier-profile:account']" v-if="accountInfo.id && formMode === 'init'" @click="edit">修改</el-button>
      </div>
      <div v-if="formMode === 'init'">
        <el-descriptions title="" :column="1" :border="true" v-if="accountInfo.id">
          <el-descriptions-item label="单位名称">{{ accountInfo.orgName || '--'}}</el-descriptions-item>
          <el-descriptions-item label="所在省份">{{ accountInfo.province || '--'}}</el-descriptions-item>
          <el-descriptions-item label="所在城市">{{ accountInfo.city || '--' }}</el-descriptions-item>
          <el-descriptions-item label="开户行名称">{{ accountInfo.bankName || '--' }}</el-descriptions-item>
          <el-descriptions-item label="开户行账号">{{ accountInfo.accountNum || '--' }}</el-descriptions-item>
          <el-descriptions-item label="开户行联行号">{{ accountInfo.unifyBankNum || '--' }}</el-descriptions-item>
        </el-descriptions>
        <el-empty v-else>
          <el-button type="text" icon="el-icon-plus" @click="add">开始配置</el-button>
        </el-empty>
      </div>
      <div style="width:800px;">
        <el-form v-if="formMode === 'edit'" ref="form" label-position="left" :model="form" :rules="rules" label-width="150px">
          <el-form-item label="单位名称" prop="orgName">
            <el-input v-model="form.orgName" :maxlength="100" placeholder="请输入单位名称" />
          </el-form-item>
          <el-form-item label="所在省份" prop="province">
            <el-input v-model="form.province" :maxlength="30" placeholder="请输入所在省份" />
          </el-form-item>
          <el-form-item label="所在城市" prop="city">
            <el-input v-model="form.city" :maxlength="30" placeholder="请输入所在城市" />
          </el-form-item>
          <el-form-item label="开户行名称" prop="bankName">
            <el-input v-model="form.bankName" :maxlength="100" placeholder="请输入开户行名称" />
          </el-form-item>
          <el-form-item label="开户行账号" prop="accountNum">
            <el-input v-model="form.accountNum" :maxlength="100" placeholder="请输入开户行账号" />
          </el-form-item>
          <el-form-item label="开户行联行号" prop="unifyBankNum">
            <el-input v-model="form.unifyBankNum" :maxlength="100" placeholder="请输入开户行联行号" />
          </el-form-item>
          <el-form-item>
            <el-button type="default" @click="cancelForm">取 消</el-button>
            <el-button type="primary" @click="submitForm" :loading="submitLoading">提 交</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script>
import * as api from '@/api/mall/supplier/supplier-profile'
export default {
  name: 'SupplierAccount',
  props: {
    supplierInfo: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      formMode: 'init',
      submitLoading: false,
      loading: false,
      accountInfo: {},
      form: {
        id: undefined,
        orgName: '',
        province: '',
        city: '',
        bankName: '',
        accountNum: '',
        unifyBankNum: ''
      },
      rules: {
        orgName: [
          { required: true, trigger: 'blur', message: '请输入单位名称' }
        ],
        province: [
          { required: true, trigger: 'blur', message: '请输入所在省份' }
        ],
        city: [
          { required: true, trigger: 'blur', message: '请输入所在城市' }
        ],
        bankName: [
          { required: true, trigger: 'blur', message: '请输入开户行名称' }
        ],
        accountNum: [
          { required: true, trigger: 'blur', message: '请输入开户账号' }
        ],
        unifyBankNum: [
          { required: true, trigger: 'blur', message: '请输入开户行联行号' }
        ],
      }
    }
  },
  mounted() {
    this.loadAccountInfo()
  },
  methods: {
    async loadAccountInfo() {
      let res = await api.getSupplierAccount()
      if(res.data) {
        this.accountInfo = res.data
      }
      this.loading = false
    },
    add() {
      this.formMode = 'edit'
      this.form.orgName = this.supplierInfo.fullName
    },
    edit() {
      this.formMode = 'edit'
      Object.keys(this.form).forEach(key => {
        this.form[key] = this.accountInfo[key]
      })
    },
    cancelForm() {
      this.formMode = 'init'
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return
        }
        this.doSubmit()
      })
    },
    async doSubmit() {
      this.submitLoading = true
      try {
        let data = {...this.accountInfo}
        Object.assign(data, this.form)
        if(this.form.id) {
          await api.updateSupplierAccount(data)
        } else {
          await api.createSupplierAccount(data)
        }
        
        this.$modal.msgSuccess("修改成功")
        this.$emit('on-update')
        this.loadAccountInfo()
        this.submitLoading = false
        this.formMode = 'init'
      } catch(e) {
        this.$modal.msgError("保存失败，请稍后重试")
        this.submitLoading = false
      }
    },
  }

}
</script>
