<template>
  <div class="app-container">
    <el-alert title="展示不超过30天范围内的不同供应商订单信息" type="info" style="margin-bottom: 24px;"></el-alert>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="100px">
      <el-form-item label="供应商" prop="supplierId">
        <supplier-select v-model="queryParams.supplierId" size="small" placeholder="请选择供应商" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="日期范围" prop="createTime">
        <el-date-picker v-model="queryParams.queryDate" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange" :picker-options="pickerOptions"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" @blur="blurDate" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="40" class="panel-group">
      <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-people">
            <svg-icon icon-class="number" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              销售订单数
            </div>
            <count-to :start-val="0" :end-val="showCount.orderCount" :duration="2600" class="card-panel-num" />
          </div>
        </div>
      </el-col>
      <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-message">
            <svg-icon icon-class="pay" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              销售总金额
            </div>
            <count-to :start-val="0" :end-val="showCount.orderAmount" :duration="3000" :decimals="decimals" class="card-panel-num" />
          </div>
        </div>
      </el-col>
      <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-money">
            <svg-icon icon-class="number" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              结算订单数
            </div>
            <count-to :start-val="0" :end-val="showCount.settleCount" :duration="3200" class="card-panel-num" />
          </div>
        </div>
      </el-col>
      <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-money">
            <svg-icon icon-class="pay" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              结算总金额
            </div>
            <count-to :start-val="0" :end-val="showCount.settleAmount" :duration="3400" :decimals="decimals" class="card-panel-num" />
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as api from "@/api/mall/statistics/trade";
import SupplierSelect from "@/views/mall/product/spu/components/supplier-select";
import CountTo from 'vue-count-to'

export default {
  components: {
    SupplierSelect,
    CountTo
  },
  data() {
    const that = this
    return {
      decimals: 2,
      queryParams: {
        supplierId: '',
        queryDate: []
      },
      pickerMinDate: '',
      pickerOptions: {  
        onPick: obj => {
          that.pickerMinDate = new Date(obj.minDate).getTime()
        },
        disabledDate(time) {  
          // 禁用今天和之后的日期，跨度不能超过30天
          if (new Date().toDateString() === time.toDateString()) {
            return true
          }
          if (that.pickerMinDate) {
            const day1 = 30 * 24 * 3600 * 1000 // 时间跨度不超过30天
            const minTime = that.pickerMinDate - day1
            const maxTime = that.pickerMinDate + day1
            return !(time.getTime() > minTime && time.getTime() < maxTime) || time.getTime() > Date.now()
          }
          return time.getTime() > Date.now()
        }
      },
      showCount: {
        orderAmount: 0,
        orderCount: 0,
        settleAmount: 0,
        settleCount: 0
      }
    };
  },
  methods: {
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        queryDate: [],
        supplierId: ''
      }
      this.showCount = {
        orderAmount: 0,
        orderCount: 0,
        settleAmount: 0,
        settleCount: 0
      }
    },
    blurDate() {
      this.pickerMinDate = ''
    },
    getList() {
      if (!this.queryParams.supplierId) {
        this.$message.error('请选择供应商')
        return
      }
      if (!this.queryParams.queryDate || this.queryParams.queryDate.length != 2) {
        this.$message.error('请选择日期范围')
        return
      }
      api.getSupplierSalesSettleSummary({
        supplierId: this.queryParams.supplierId,
        startTime: this.queryParams.queryDate[0],
        endTime: this.queryParams.queryDate[1]
      }).then(response => {
        if (response.code === 0) {
          this.showCount = response.data
        }
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.panel-group {
  margin-top: 18px;

  .card-panel-col {
    margin-bottom: 32px;
  }

  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-shopping {
        background: #34bfa3
      }
    }

    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-shopping {
      color: #34bfa3
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px 26px 26px 0;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}
</style>