<template>
  <div class="app-container">
    <el-card>
    <el-empty v-if="!configInfo.id && formMode === 'init'">
      <el-button type="text" icon="el-icon-add" @click="beginConfig">开始配置</el-button>
    </el-empty>
    <div v-if="configInfo.id && formMode === 'init'">
      <el-descriptions title="开票信息" direction="vertical" :column="2" :border="true">
        <template slot="extra">
          <el-button type="primary" size="small" @click="editConfig">修改</el-button>
        </template>
        <el-descriptions-item label="发票抬头">{{ configInfo.title || '--' }}</el-descriptions-item>
        <el-descriptions-item label="纳税人识别号">{{ configInfo.taxerId || '--' }}</el-descriptions-item>
        <el-descriptions-item label="企业注册地址">{{ configInfo.address || '--' }}</el-descriptions-item>
        <el-descriptions-item label="企业注册电话">{{ configInfo.telephone || '--' }}</el-descriptions-item>
        <el-descriptions-item label="开户行">{{ configInfo.bankName || '--' }}</el-descriptions-item>
        <el-descriptions-item label="开户行账号">{{ configInfo.bankAccount || '--' }}</el-descriptions-item>
        <el-descriptions-item label="开票机构ID（京东特有）">{{ configInfo.vopOrg || '--' }}</el-descriptions-item>
        <el-descriptions-item label="收票人联系电话">{{ configInfo.phoneNumber || '--' }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <el-form v-if="formMode === 'edit'" ref="configForm" label-position="top" :model="configForm" :rules="configRules"
      label-width="80px" style="width:900px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="发票抬头" prop="title">
            <el-input v-model="configForm.title" maxlength="100" placeholder="请输入发票抬头" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="纳税人识别号" prop="taxerId">
            <el-input v-model="configForm.taxerId" maxlength="50" placeholder="请输入纳税人识别号" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="企业注册地址" prop="address">
            <el-input v-model="configForm.address" maxlength="200" placeholder="请输入企业注册地址" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="企业注册电话" prop="telephone">
            <el-input v-model="configForm.telephone" maxlength="20" placeholder="请输入企业注册电话" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开户行" prop="bankName">
            <el-input v-model="configForm.bankName" maxlength="100" placeholder="请输入开户行" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户行账号" prop="bankAccount">
            <el-input v-model="configForm.bankAccount" maxlength="50" placeholder="请输入开户行账号" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开票机构ID（京东特有）" prop="vopOrg">
            <el-input v-model="configForm.vopOrg" maxlength="50" placeholder="请输入开票机构ID" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收票人联系电话" prop="phoneNumber">
            <el-input v-model="configForm.phoneNumber" maxlength="20" placeholder="请输入收票人联系电话" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item>
        <el-button type="default" @click="cancelForm">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">提 交</el-button>
      </el-form-item>
    </el-form>
    </el-card>
  </div>
</template>

<script>
import * as api from "@/api/mall/config/invoiceConfig";
export default {
  name: "MallInvoiceConfig",
  components: {},
  data() {
    return {
      configInfo: {},
      formMode: 'init',
      submitLoading: false,
      configForm: {
        id: undefined,
        title: '',
        taxerId: '',
        address: '',
        telephone: '',
        bankName: '',
        bankAccount: '',
        vopOrg: '',
        phoneNumber: ''
      },
      configRules: {
        title: [
          { required: true, trigger: 'blur', message: '请输入发票抬头' }
        ],
        taxerId: [
          { required: true, trigger: 'blur', message: '请输入纳税人识别号' }
        ]
      },
    }
  },
  created() {
    this.loadConfig()
  },
  methods: {
    beginConfig() {
      this.formMode = 'edit'
    },
    async loadConfig() {
      let res = await api.getInvoiceConfig();
      this.configInfo = res.data || {}
    },
    editConfig() {
      this.formMode = 'edit'
      Object.assign(this.configForm, this.configInfo)
    },
    cancelForm() {
      this.formMode = 'init'
    },
    async doSubmit() {
      this.submitLoading = true
      try {
        if (this.configForm.id !== undefined) {
          await api.updateInvoiceConfig(this.configForm)
          this.$modal.msgSuccess("修改成功");
        } else {
          await api.createInvoiceConfig(this.configForm)
          this.$modal.msgSuccess("保存成功");
        }
        this.submitLoading = false
        this.formMode = 'init'
        this.loadConfig()
      } catch(e) {
        this.$modal.msgError("保存失败，请稍后重试")
        this.submitLoading = false
      }
    },
    submitForm() {
      this.$refs["configForm"].validate(valid => {
        if (!valid) {
          return;
        }
        this.doSubmit()
      });
    }
  }
}
</script>

<style lang="scss" scoped></style>