<template>
  <div class="basis-config-form">
    <el-form ref="configForm" label-position="right" :model="configForm"
      :rules="configRules" label-width="180px">

      <el-tabs v-model="activeTab" @tab-click="handleTabChange">
        <el-tab-pane label="基础配置" name="tab1">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="采购方名称" prop="name">
                <el-input v-model="configForm.name" :maxlength="100" placeholder="请输入采购方名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="商城标题" prop="title">
                <el-input v-model="configForm.title" :maxlength="100" placeholder="请输入商城标题" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="商城域名" prop="domainList">
                <el-tag :key="tag" v-for="tag in configForm.domainList" closable :disable-transitions="false"
                  @close="handleDomainClose(tag)">
                  {{ tag }}
                </el-tag>
                <el-input class="input-new-tag" v-if="domainInputVisible" v-model="domainInputValue" ref="saveTagInput"
                  size="small" :maxlength="100" placeholder="请输入商城域名" @keyup.enter.native="handleInputConfirm"
                  @blur="handleInputConfirm">
                </el-input>
                <el-button v-else class="button-new-tag" size="small" @click="showDomainInput">+ 添加域名</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="提交订单开关" prop="orderSwitch">
                <el-switch v-model="configForm.orderSwitch" active-text="打开" inactive-text="关闭" />
              </el-form-item>
              <el-form-item v-if="!configForm.orderSwitch" label="提交订单白名单" prop="orderWhiteList">
                <el-input v-model="configForm.orderWhiteList" :maxlength="200" placeholder="请输入用户id，格式为 189,212" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="商品展示字段" prop="productField">
                <el-checkbox-group v-model="configForm.productField">
                  <el-checkbox v-for="dict in productFieldList" :key="dict.value" :label="dict.value">{{ dict.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="用户注册开关" prop="registerSwitch">
                <el-switch v-model="configForm.registerSwitch" active-text="打开" inactive-text="关闭" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="找回密码开关" prop="resetPasswordSwitch">
                <el-switch v-model="configForm.resetPasswordSwitch" active-text="打开" inactive-text="关闭" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="登录类型" prop="loginType">
                <el-radio-group v-model="configForm.loginType">
                  <el-radio v-for="dict in this.getDictDatas(
                    DICT_TYPE.MALL_CONFIG_LOGIN_TYPE
                  )" :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="false">
              <el-form-item label="退出方式" prop="logoutType">
                <el-radio-group v-model="configForm.logoutType">
                  <el-radio v-for="dict in this.getDictDatas(
                    DICT_TYPE.MALL_CONFIG_LOGOUT_TYPE
                  )" :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="登录方式" prop="loginMethodList">
                <el-checkbox-group v-model="configForm.loginMethodList">
                  <el-checkbox v-for="dict in this.getDictDatas(
                    DICT_TYPE.MALL_LOGIN_METHOD
                  )" :key="dict.value" :label="dict.value">{{ dict.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="登录地址" prop="loginUrl" v-if="configForm.loginType !== 1" :rules="[
                { required: true, trigger: 'blur', message: '请输入登录地址' },
                { type: 'url', trigger: 'blur', message: '登录地址格式不正确' }
              ]">
                <el-input v-model="configForm.loginUrl" :maxlength="240" placeholder="请输入登录地址" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="支付方式" prop="payMethodList">
                <el-checkbox-group v-model="configForm.payMethodList">
                  <el-checkbox v-for="dict in this.getDictDatas(
                    DICT_TYPE.MALL_PAYMENT_METHOD
                  )" :key="dict.value" :label="dict.value">{{ dict.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="查看价格强制登录开关" prop="getPriceMustLogin">
                <el-switch v-model="configForm.getPriceMustLogin" active-text="打开" inactive-text="关闭" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="商品评价开关" prop="productCommentSwitch">
                <el-switch v-model="configForm.productCommentSwitch" active-text="打开" inactive-text="关闭" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="收货地址备注开关" prop="addressRemarkSwitch">
                <el-switch v-model="configForm.addressRemarkSwitch" active-text="打开" inactive-text="关闭" />
              </el-form-item>
              <el-form-item label="收货地址备注" prop="addressRemark">
                <el-input v-model="configForm.addressRemark" :maxlength="100" placeholder="请输入收货地址备注" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="全局通知开关" prop="globalTipSwitch">
                <el-switch v-model="configForm.globalTipSwitch" active-text="打开" inactive-text="关闭" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="平台客服配置" prop="custService">
                <el-input v-model="configForm.custService" :maxlength="200" placeholder="请输入平台客服配置，格式为 k=v&k2=v2 小王=13599996666&小李=13966668888" />
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="configForm.globalTipSwitch">
              <el-form-item label="全局通知内容" prop="globalTipContent" :rules="[{ required: true, message: '请输入全局通知内容' }]">
                <el-input type="textarea" :rows="6" :maxlength="500" show-word-limit
                  v-model="configForm.globalTipContent" placeholder="请输入全局通知内容" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="管理后台入口展示开关" prop="adminEntrySwitch">
                <el-switch v-model="configForm.adminEntrySwitch" active-text="打开" inactive-text="关闭" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="管理后台入口地址" prop="adminEntryUrl" v-if="configForm.adminEntrySwitch" :rules="[
                { required: true, trigger: 'blur', message: '请输入管理后台入口地址' },
                { type: 'url', trigger: 'blur', message: '地址格式不正确' }
              ]">
                <el-input v-model="configForm.adminEntryUrl" :maxlength="100" placeholder="请输入管理后台入口地址" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="业务配置" name="tab2">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="自动审批开关" prop="autoApprove">
                <el-switch v-model="configForm.autoApprove" @change="autoApproveClick" active-text="打开" inactive-text="关闭"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="前置审批开关" prop="approveSwitch">
                <el-switch v-model="configForm.approveSwitch" @change="approveSwitchClick" active-text="打开" inactive-text="关闭" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目经费卡开关" prop="projectSwitch">
                <el-switch v-model="configForm.projectSwitch" @change="projectSwitchClick" active-text="打开" inactive-text="关闭" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="资产登记开关" prop="assetsSwitch">
                <el-switch v-model="configForm.assetsSwitch" @change="assetsSwitchClick" active-text="打开" inactive-text="关闭" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="手动推送固资开关" prop="assetsManualSwitch">
                <el-switch v-model="configForm.assetsManualSwitch" active-text="打开" inactive-text="关闭" :disabled="!configForm.assetsSwitch"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="固定资产商品混合下单开关" prop="assetsMixSwitch">
                <el-switch v-model="configForm.assetsMixSwitch" active-text="打开" inactive-text="关闭" :disabled="!configForm.assetsSwitch"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="固定资产线下结算开关" prop="assetsOfflinePaySwitch">
                <el-switch v-model="configForm.assetsOfflinePaySwitch" active-text="打开" inactive-text="关闭" :disabled="!configForm.assetsSwitch"/>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="设备仪器申请单开关" prop="instrumentApplyExport">
                <el-switch v-model="configForm.instrumentApplyExport" active-text="打开" inactive-text="关闭" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="电子签收单导出开关" prop="orderReceiptExport">
                <el-switch v-model="configForm.orderReceiptExport" active-text="打开" inactive-text="关闭" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="自动申请开票开关" prop="autoInvoiceSwitch">
                <el-switch v-model="configForm.autoInvoiceSwitch" active-text="打开" inactive-text="关闭" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="自动申请开票时间" prop="autoInvoiceThreshold"
                :rules="[{ required: true, message: '自动申请开票时间不能为空', trigger: 'blur' }]">
                <el-input-number v-model="configForm.autoInvoiceThreshold" :min="0" :max="60" :disabled="!configForm.autoInvoiceSwitch" />
                订单完成{{configForm.autoInvoiceThreshold}}天后自动申请
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="账单发票校验开关" prop="verifyInvoice">
                <el-switch v-model="configForm.verifyInvoice" active-text="打开" inactive-text="关闭" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="扩展配置" name="tab3">
          <el-row :gutter="16">
            <el-col :span="12" v-for="(extItem,extIndex) in configForm.extConfigItems" :key="extIndex">
              <el-form-item :label="extItem.label" :prop="'extConfigItems.' + extIndex + '.' + extItem.key">
                <template v-if="extItem.comType === 'input'">
                  <el-input v-model="extItem.value" :maxlength="extItem.maxLength" :placeholder="extItem.placeholder" />
                </template>
                <template v-if="extItem.comType === 'switch'">
                  <el-switch v-model="extItem.value" active-text="打开" inactive-text="关闭" />
                </template>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>

      <el-form-item>
        <el-button type="default" @click="cancelForm">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">提 交</el-button>
      </el-form-item>

    </el-form>
  </div>
</template>

<script>
import { productFieldList, extConfigDics } from '@/views/mall/config/configUtil'
import * as api from "@/api/mall/config/basisConfig"
export default {
  name: 'MallBasisConfigForm',
  data() {
    return {
      activeTab: 'tab1',
      submitLoading: false,
      productFieldList: productFieldList,
      configForm: {
        id: undefined,
        name: "",
        title: "金采通",
        autoApprove: false,
        multipleShop: true,
        approveSwitch: false,
        projectSwitch: false,
        getPriceMustLogin: false,
        assetsSwitch: false,
        assetsManualSwitch: false,
        orderSwitch: true,
        orderWhiteList: null,
        registerSwitch: false,
        resetPasswordSwitch: false,
        autoInvoiceSwitch: false,
        autoInvoiceThreshold: 0,
        verifyInvoice: false,
        payMethodList: [],
        payMethod: '',
        domain: "",
        domainList: [],
        loginType: "",
        loginMethodList: [],
        loginMethod: "",
        loginUrl: "",
        logoutType: "",
        globalTipSwitch: false,
        globalTipContent: '',
        productField: [],
        adminEntrySwitch: false,
        adminEntryUrl: null,
        productCommentSwitch: false,
        addressRemarkSwitch: false,
        addressRemark: "",
        assetsMixSwitch: true,
        assetsOfflinePaySwitch: false,
	      instrumentApplyExport: false,
        orderReceiptExport: false,
        custService: '',
        extConfig: '',
        extConfigItems: []
      },
      domainInputVisible: false,
      domainInputValue: "",
      configRules: {
        name: [{ required: true, trigger: "blur", message: "请输入名称" }],
        title: [{ required: true, trigger: "blur", message: "请输入商城标题" }],
        domainList: [
          { type: "array", required: true, trigger: "blur", message: "请输入商城域名" }
        ],
        payMethodList: [
          { type: "array", required: true, trigger: "blur", message: "请选择支付方式" }
        ],
        loginType: [
          { required: true, trigger: "change", message: "请选择登录类型" },
        ],
        logoutType: [
          { required: true, trigger: "change", message: "请选择退出类型" },
        ],
      },
    }
  },
  mounted() {
    this.readTabStatus()
  },
  methods: {
    handleTabChange() {
      let key = 'jctmall-basis-config-tab'
      sessionStorage.setItem(key, this.activeTab)
    },
    readTabStatus() {
      let key = 'jctmall-basis-config-tab'
      this.activeTab = sessionStorage.getItem(key) || 'tab1'
    },
    initExtConfig() {
      let obj = {}
      if(this.configForm.extConfig) {
        obj = JSON.parse(this.configForm.extConfig)
      }
      let extConfigItems = [...extConfigDics]
      extConfigItems.forEach(item => {
        console.log('obj-----', obj)
        item.value = obj[item.key] || item.defaultVal
      });
      this.configForm.extConfigItems = extConfigDics
    },
    assembleExtConfig() {
      let obj = {}
      this.configForm.extConfigItems.forEach(item => {
        obj[item.key] = item.value
      })
      return obj
    },
    init(configInfo) {
      Object.assign(this.configForm, configInfo)
      this.initExtConfig()
    },
    approveSwitchClick(newVal) {
      if (newVal) {
        this.$modal.msg("前置审批开关打开前，请检查是否已经完成外部集成中审批流配置;同时会关闭自动审批");
        this.configForm.autoApprove = false
      }
    },
    projectSwitchClick(newVal) {
      if (newVal) {
        this.$modal.msg("项目经费卡开关打开前，请检查是否已经完成外部集成中业财融合配置；");
      }
    },
    autoApproveClick(newVal) {
      if (newVal) {
        this.$modal.msg("自动审批打开时，会关闭前置审批");
        this.configForm.approveSwitch = false
      }
    },
    assetsSwitchClick(newVal) {
      if (!newVal) {
        this.$modal.msg("资产登访开关关闭时，相关配置开关自动关闭");
        this.configForm.assetsManualSwitch = false
        this.configForm.assetsMixSwitch = false
        this.configForm.assetsOfflinePaySwitch = false
      } else {
        this.$modal.msg("资产登访开关打开前，请检查是否已经完成外部集成中资产配置");
      }
    },
    async doSubmit() {
      this.submitLoading = true;
      try {
        let params = {...this.configForm}
        delete params.extConfigItems
        if (params.id !== undefined) {
          await api.updateBasisConfig(params);
          this.$modal.msgSuccess("修改成功");
        } else {
          await api.createBasisConfig(params);
          this.$modal.msgSuccess("保存成功");
        }
        this.submitLoading = false;
        this.$emit('update', '1')
      } catch (e) {
        this.$modal.msgError("保存失败，请稍后重试");
        this.submitLoading = false;
      }
    },
    submitForm() {
      this.$refs["configForm"].validate((valid) => {
        if (!valid) {
          return;
        }
        this.configForm.domain = this.configForm.domainList.join(",");
        this.configForm.payMethod = this.configForm.payMethodList.join(",");
        this.configForm.loginMethod = this.configForm.loginMethodList.join(",");
        this.configForm.productField = this.configForm.productField.join(",");
        this.configForm.extConfig = JSON.stringify(this.assembleExtConfig())
        this.doSubmit();
      });
    },
    cancelForm() {
      this.$emit('cancel', '1')
    },
    handleDomainClose(tag) {
      this.configForm.domainList.splice(
        this.configForm.domainList.indexOf(tag),
        1
      );
    },
    showDomainInput() {
      this.domainInputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    handleInputConfirm() {
      let inputValue = this.domainInputValue;
      if (inputValue) {
        this.configForm.domainList.push(inputValue);
      }
      this.domainInputVisible = false;
      this.domainInputValue = "";
    },
  }

}
</script>

<style lang="scss" scoped>
.basis-config-form {
  .el-form-item {
    margin-bottom: 20px;
  }
}


.basis-cnf-card {
  margin: 20px 0;
}

.el-tag+.el-tag {
  margin-left: 10px;
}

.button-new-tag {
  margin-left: 10px;
  height: 28px;
  line-height: 28px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 180px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
