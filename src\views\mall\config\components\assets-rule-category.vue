<template>
  <div>
    <el-dialog title="固资规则-商品分类配置" :visible.sync="dialogOpen" width="1200px" v-dialogDrag append-to-body :before-close="handleClose">
      <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" class="order-form" size="small" :inline="true" v-show="showSearch" label-width="100px">
          <el-form-item label="分类ID" prop="categoryId">
            <el-input v-model="queryParams.categoryId" clearable></el-input>
          </el-form-item>
          <el-form-item label="分类名称" prop="categoryName">
            <el-input v-model="queryParams.categoryName" clearable></el-input>
          </el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        </el-form>

        <!-- 操作工具栏 -->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="categoryOpen = true"
                      v-hasPermi="['mall:assets-config']">添加</el-button>
            <el-button type="primary" plain icon="el-icon-trash" size="mini" @click="deleteAll"
                      v-hasPermi="['mall:assets-config']">全部删除</el-button>
            <el-button type="primary" plain icon="el-icon-edit" size="mini" @click="updateAllPrice"
                      v-hasPermi="['mall:assets-config']" v-if="total > 0">更新全部价格</el-button>    
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="list" border>
          <el-table-column label="分类" align="center" prop="categoryId" width="200">
            <template v-slot="scope">
              <span>{{ scope.row.categoryName }} / {{ scope.row.categoryId }}</span>
            </template>
          </el-table-column>
          <el-table-column label="全路径分类" align="center" prop="fullCategoryName" show-overflow-tooltip >
            <template v-slot="scope">
              <span>{{ scope.row.fullCategoryName }} / {{ scope.row.fullCategoryId }}</span>
            </template>
          </el-table-column>
          <el-table-column label="外部类别" align="center" prop="extCategoryCode" width="160" show-overflow-tooltip>
            <template v-slot="scope">
              <span>{{ scope.row.extCategoryName }} / {{ scope.row.extCategoryCode }}</span>
            </template>
          </el-table-column>
          <el-table-column label="价格阈值" align="center" prop="priceThreshold" width="120">
            <template v-slot="scope">
              <span>{{ formatMoney(scope.row.priceThreshold) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding" width="150">
              <template v-slot="scope">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <pagination v-show="total > 0" hide-on-single-page :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
          @pagination="getList" />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="750px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="分类名称" prop="categoryName">
              <el-input v-model="form.categoryName" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分类ID" prop="categoryId">
              <el-input v-model="form.categoryId" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="全路径分类ID" prop="fullCategoryId">
              <el-input v-model="form.fullCategoryId" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="全路径分类名称" prop="fullCategoryName">
              <el-input v-model="form.fullCategoryName" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="外部类别编码" prop="extCategoryCode">
              <el-input v-model="form.extCategoryCode" :maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="外部类别名称" prop="extCategoryName">
              <el-input v-model="form.extCategoryName" :maxlength="60" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="价格阈值" prop="priceThreshold">
          <el-input-number :min="0" :max="999999" v-model="form.priceThreshold"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <category-dialog :open.sync="categoryOpen" @change="handleCategoryChange" :extParams="{status:null}" :extProps="{checkStrictly: true,multiple: true}"/>
  </div>
</template>

<script>
import * as api from "@/api/mall/config/assetsConfig";
import CategoryDialog from "@/views/mall/product/spu/components/category-dialog";
export default {
  name: "MallAssetsRuleCategory",
  components: { CategoryDialog },
  data() {
    return {
      categoryOpen: false,
      dialogOpen: false,
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 品牌列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        categoryId: null,
        categoryName: null
      },
      ruleInfo: undefined,
      ruleId: undefined,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        priceThreshold: [{required: true, message: "价格阈值不能为空", trigger: "blur"}]
      }
    }
  },
  methods: {
    show(ruleInfo) {
      this.ruleInfo = ruleInfo
      this.ruleId = ruleInfo.id
      this.handleQuery()
      this.dialogOpen = true
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      this.queryParams.ruleId = this.ruleId
      // 执行查询
      api.getAssetsRuleCategoryList(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = parseInt(response.data.total);
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        ruleId: undefined,
        categoryId: undefined,
        categoryName: undefined,
        priceThreshold: 0
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      api.getAssetsRuleCategory(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改规则分类";
      });
    },
    handleClose() {
      this.dialogOpen = false
      this.updateState()
    },
    updateState() {
      this.$emit('update', this.ruleInfo)
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.id != null) {
          api.updateAssetsRuleCategory(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除分类为"' + row.categoryName + '"的数据项?').then(function () {
        return api.deleteAssetsRuleCategory(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    deleteAll() {
      const ruleId = this.ruleId;
      this.$modal.confirm('是否确认删除当前规则下所有分类吗?').then(function () {
        return api.deleteAllByRule(ruleId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    updateAllPrice() {
      this.$prompt('请输入价格', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^[1-9]\d{1,6}$/,
        inputErrorMessage: '输入不正确'
      }).then(({ value }) => {
        api.updateAllPrice({ruleId: this.ruleId,priceThreshold: value}).then(res => {
          this.$modal.msgSuccess("更新成功");
          this.getList();
        })
      }).catch(() => {});
    },
    handleCategoryChange(ids, nodes) {
      console.log('category-change: ', ids, nodes)
      if(nodes) {
        let items = nodes.map(node => {
          return {
            ruleId: this.ruleId,
            categoryId: node.data.categoryId,
            categoryName: node.data.categoryName,
            fullCategoryId: node.data.fullCategoryId,
            fullCategoryName: node.data.fullCategoryName,
            extCategoryCode: '',
            extCategoryName: '',
            priceThreshold: 0
          }
        })
        api.createAssetsRuleCategoryBatch(items).then(res => {
            this.getList();
            this.$modal.msgSuccess("添加成功");
          }
        )
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .box-card {
    margin: 10px 0;
  }
}
</style>