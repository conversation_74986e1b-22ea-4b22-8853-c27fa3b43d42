import request from '@/utils/request'

// 创建规则关联分类
export function createProjectRuleCategory(data) {
  return request({
    url: '/mall/project/project-rule-category/create',
    method: 'post',
    data: data
  })
}

// 创建规则关联分类
export function createProjectRuleCategoryBatch(data) {
  return request({
    url: '/mall/project/project-rule-category/create-batch',
    method: 'post',
    data: data
  })
}

// 更新规则关联分类状态
export function updateProjectRuleCategoryStatus(data) {
  return request({
    url: '/mall/project/project-rule-category/update-status',
    method: 'post',
    data: data
  })
}

// 删除规则关联分类
export function deleteProjectRuleCategory(id) {
  return request({
    url: '/mall/project/project-rule-category/delete?id=' + id,
    method: 'delete'
  })
}

// 删除规则关联分类
export function deleteProjectRuleCategoryBatch(data) {
  return request({
    url: '/mall/project/project-rule-category/delete-batch',
    method: 'delete',
    data: data
  })
}

// 删除规则关联分类
export function deleteProjectRuleCategoryByRule(id) {
  return request({
    url: '/mall/project/project-rule-category/delete-by-rule?ruleId=' + id,
    method: 'delete'
  })
}


// 获得规则关联分类分页
export function getProjectRuleCategoryPage(query) {
  return request({
    url: '/mall/project/project-rule-category/page',
    method: 'get',
    params: query
  })
}
