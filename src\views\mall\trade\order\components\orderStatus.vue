<template>
  <div class="order-status-con"> 
    <el-tag :type="order.status | orderStatusStyle">
      {{ order.status | orderStatusInfo }} {{ order.userDeleted ? ": 用户删除" : "" }}
    </el-tag>
    <template v-if="showAuditStatus"> 
      <br>
      <el-tag :type="order.auditStatus | orderAuditStatusStyle">{{ order.auditStatus | orderAuditStatusInfo }}</el-tag>
    </template>
    
  </div>
</template>

<script>
import { isBusinessPay } from '@/utils/mallUtil'
export default {
  name: 'OrderStatusCom',
  props: {
    order: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  computed: {
    showAuditStatus() {
      return isBusinessPay(this.order.paymentMethod) && this.order.status === 1 && this.order.auditStatus > 1
    },
    auditStatusStyle() {
      return this.order.auditStatus
    }
  }

}
</script>

<style lang="scss" scoped>
.order-status-con {
  .el-tag {
    margin: 0 5px 5px 0;
  }
}
</style>