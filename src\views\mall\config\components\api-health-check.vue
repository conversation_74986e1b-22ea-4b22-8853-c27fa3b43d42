<template>
  <el-dialog
    title="外部接口检测"
    :visible.sync="visible"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="api-check-container">
      <div class="check-header">
        <el-button 
          type="primary" 
          size="small" 
          @click="checkAllApis"
          :loading="checking"
        >
          {{ checking ? '检测中...' : '开始检测' }}
        </el-button>
        <span class="check-summary" v-if="checkResults.length > 0">
          成功: {{ successCount }} / 失败: {{ failCount }} / 总计: {{ checkResults.length }}
        </span>
      </div>
      
      <el-table 
        :data="checkResults" 
        style="width: 100%; margin-top: 15px;"
        max-height="500"
      >
        <el-table-column prop="name" label="接口名称" min-width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}/{{ scope.row.code }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="调用状态" width="120" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.checking">
              <i class="el-icon-loading" style="color: #409EFF;"></i>
              <span style="margin-left: 5px; color: #409EFF;">检测中</span>
            </div>
            <div v-else-if="scope.row.status === 'success'">
              <i class="el-icon-check" style="color: #67C23A; font-size: 16px;"></i>
              <span style="margin-left: 5px; color: #67C23A;">成功</span>
            </div>
            <div v-else-if="scope.row.status === 'failed'">
              <i class="el-icon-close" style="color: #F56C6C; font-size: 16px;"></i>
              <span style="margin-left: 5px; color: #F56C6C;">失败</span>
            </div>
            <div v-else>
              <span style="color: #909399;">待检测</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="message" label="详情" min-width="200">
          <template slot-scope="scope">
            <span :style="{ color: scope.row.status === 'failed' ? '#F56C6C' : '#606266' }">
              {{ scope.row.message || '--' }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import * as api from "@/api/mall/config/ycrhConfig"
export default {
  name: "ApiHealthCheck",
  data() {
    return {
      visible: false,
      checking: false,
      checkResults: []
    }
  },
  computed: {
    successCount() {
      return this.checkResults.filter(item => item.status === 'success').length;
    },
    failCount() {
      return this.checkResults.filter(item => item.status === 'failed').length;
    }
  },
  methods: {
    show() {
      this.visible = true;
      this.initApiList();
    },
    
    handleClose() {
      this.visible = false;
      this.checking = false;
      this.checkResults = [];
    },
    
    initApiList() {
      // 初始化接口列表
      this.checkResults = [
        { code: 'getYgInfo', name: '人员信息查询', status: '', message: '', checking: false },
        { code: 'getFzrxmxx', name: '项目列表查询', status: '', message: '', checking: false },
        { code: 'checkXmye', name: '项目余额校验', status: '', message: '', checking: false },
        { code: 'getXmed', name: '项目额度查询', status: '', message: '', checking: false },
        { code: 'getXmyeByBatch', name: '项目余额批量查询', status: '', message: '', checking: false },
        { code: 'getXmedByBatch', name: '项目额度批量查询', status: '', message: '', checking: false },
        { code: 'getYybxd', name: '预约报销单查询', status: '', message: '', checking: false },
        { code: 'getZwzgzd', name: '检索人员列表', status: '', message: '', checking: false },
        { code: 'pushOrder', name: '订单推送', status: '', message: '', checking: false },
        { code: 'cancelOrder', name: '订单取消', status: '', message: '', checking: false },
        { code: 'splitOrder', name: '订单拆分', status: '', message: '', checking: false },
        { code: 'afsNotice', name: '订单售后', status: '', message: '', checking: false },
        { code: 'queryOrder', name: '订单状态查询', status: '', message: '', checking: false },
        { code: 'queryOrderNumber', name: '订单编号批量查询', status: '', message: '', checking: false },
        { code: 'pushOrderZcInfo', name: '订单资产信息追加', status: '', message: '', checking: false },
        { code: 'pushBill', name: '账单推送', status: '', message: '', checking: false },
        { code: 'cancelBill', name: '账单撤销', status: '', message: '', checking: false },
        { code: 'getBillStatus', name: '账单查询', status: '', message: '', checking: false },
        { code: 'voucherUpload', name: '凭证附件上传', status: '', message: '', checking: false },
        { code: 'sp.uploadJsonStr', name: '审批流-上传单据', status: '', message: '', checking: false },
        { code: 'sp.deleteJsonStr', name: '审批流-删除单据', status: '', message: '', checking: false },
        { code: 'sp.getSpxxdlJsonStr', name: '审批流-查询每级单据审批状态', status: '', message: '', checking: false },
        { code: 'sp.getSpztJsonStr', name: '审批流-查询单据最终审批状态', status: '', message: '', checking: false },
        { code: 'sp.completeApproval', name: '审批流-归档审批单', status: '', message: '', checking: false },
        { code: 'sp.uploadAccessoryAutoByJsonParam', name: '审批流-审批流上传附件', status: '', message: '', checking: false }
      ];
    },
    getApiData(code) {
      let param = {
        method: code,
        data: {}
      }
      if(code === 'getYgInfo') {
        param.data.ygbh = '112354'
        return param
      }
      if(code === 'getFzrxmxx') {
        param.data.type = '1'
        param.data.ygbh = '112354'
        return param
      }
      if(code === 'getXmyeByBatch') {
        param.data.xmList = [{bmbh:"106",xmbh:"106009901",xmfzrbh:"8824020"}]
        return param
      }
      if(code === 'getXmedByBatch') {
        param.data.xmList = [{bmbh:"106",xmbh:"106009901",xmfzrbh:"8824020",jjflkmbh:"300101"}]
        return param
      }
      if(code === 'pushOrder') {
        param.data = {
          businessType: '01', wbShopOrder: {
            orderNo: '10239382',
            totalPrice: 536.1,
            payType: 1,
            freight: 0,
            singleProject: '1',
            orderTime: '2025-08-04 19:14:13',
            wbSkuInfo: [],
            bmbh: '0011',
            xmbh: '06610011',
            xmfzrbh: '06601'
          }
        }
        return param
      }
      if(code === 'cancelOrder') {
        param.data = { cancelReason : "1", cancelTime: "2025-08-02 09:49:33", orderNo: "****************"}
        return param
      }
      if(code === 'splitOrder') {
        param.data = { isOverwrite : "1", parentOrderNo: "1025080116070113", splitShopOrderList: []}
        return param
      }
      if(code === 'afsNotice') {
        param.data = { afsDetailList : [], afsXmList: [], orderNo: '**********'}
        return param
      }
      if(code === 'queryOrder') {
        param.data = { orderNo: '**********'}
        return param
      }
      if(code === 'queryOrderNumber') {
        param.data = { startDate: '2024-01-01', endDate:'2024-02-02'}
        return param
      }
      if(code === 'pushOrderZcInfo') {
        param.data = {  orderNo: "****************", wbSkuInfoZcs: []}
        return param
      }
      if(code === 'pushBill') {
        param.data = { account: {}, billAmount:39465.00,billNo:"1940316981347041281",billStartDate:"2025-07-17",businessType:"01",orderInfo: []}
        return param
      }

      if(code === 'cancelBill') {
        param.data = { billNo:"1940316981347041281",cancelReason:"撤销",cancelTime:"2025-07-17 10:00:00",orderNoList: []}
        return param
      }
      if(code === 'getBillStatus') {
        param.data = { billNo:"1940316981347041281"}
        return param
      }
      if(code === 'voucherUpload') {
        param.data = { file: 'JVBERi0xLjcKJcfsj6IKJSVVTEM6MS4wMS4wMSA1IDAgb2JqCjw8L0xlbmd0aCA2IDAgUi9GaWx0ZXIgL0ZsYXRlRGVjb2RlPj4Kc3RyZWFtCnic'}
        return param
      }
      if(code === 'sp.uploadJsonStr') {
        param.data = { tc_spxxdl: [], tc_spzt:{
          mkdm: "YC",
          ywbh: "CG",
          ywlsh: "***************",
          sqr: "韩松来",
          sqrbh: "218002",
          sqrq: "********",
          sqsj: "2025-08-04"
        }}
        return param
      }
      if(code === 'sp.deleteJsonStr') {
        param.data = { mkdm:"YC",ywbh:"CG",ywlsh:"925072809280916" }
        return param
      }
      if(code === 'sp.getSpxxdlJsonStr') {
        param.data = { mkdm:"YC",ywbh:"CG",ywlsh:"925072809280916" }
        return param
      }
      if(code === 'sp.getSpztJsonStr') {
        param.data = { mkdm:"YC",ywbh:"CG",ywlsh:"925072809280916" }
        return param
      }
      if(code === 'sp.completeApproval') {
        param.data = { mkdm:"YC",ywbh:"CG",ywlsh:"925072809280916" }
        return param
      }
      if(code === 'sp.uploadAccessoryAutoByJsonParam') {
        param.data = { ywlsh: new Date().getTime() + '-1', fjnr: 'aGVsbG8K', wjm:'hello', sfzyfj: '0', mkdm:"YC",ywbh:"CG", xh: "111" }
        return param
      }

      return param
    },
    async checkAllApis() {
      this.checking = true;
      
      // 重置所有状态
      this.checkResults.forEach(item => {
        item.status = '';
        item.message = '';
        item.checking = false;
      });
      
      // 逐个检测接口
      for (let i = 0; i < this.checkResults.length; i++) {
        const apiItem = this.checkResults[i];
        apiItem.checking = true;
        
        try {
          const result = await api.requestYcrhApi(this.getApiData(apiItem.code));
          apiItem.status = result.code === 0 ? 'success' : 'failed';
          apiItem.message = result.data.resultMsg || (result.code === 0 ? '接口正常' : '接口异常');
        } catch (error) {
          console.log('error----', error)
          apiItem.status = 'failed';
          apiItem.message = error.msg || '网络错误';
        } finally {
          apiItem.checking = false;
        }
        
        // 添加小延迟，避免请求过于频繁
        if (i < this.checkResults.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }
      
      this.checking = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.api-check-container {
  .check-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .check-summary {
      font-size: 14px;
      color: #606266;
    }
  }
}
</style>