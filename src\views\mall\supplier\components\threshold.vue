<template>
  <div> 
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span class="title">起售金额</span>
        <el-button style="float: right; padding: 3px 0" type="text" v-hasPermi="['mall:supplier-profile:update']" v-if="formMode === 'init'" @click="edit">修改</el-button>
      </div>
      <div v-if="formMode === 'init'">
        <el-descriptions title="" :column="1" :border="true">
          <el-descriptions-item label="起售金额">{{ formatMoney(supplierInfo.saleAmountMin) }}元</el-descriptions-item>
          <el-descriptions-item label="包邮订单最小金额">{{ formatMoney(supplierInfo.freightThreshold) }}元</el-descriptions-item>
          <el-descriptions-item label="统一运费">{{ formatMoney(supplierInfo.freight) }}元</el-descriptions-item>
        </el-descriptions>
      </div>
      <div style="width:800px;">
        <el-form v-if="formMode === 'edit'" ref="form" label-position="left" :model="form" :rules="rules" label-width="150px">
          <el-form-item label="起售金额" prop="saleAmountMin">
            <el-input-number :min="0" :max="99999" v-model="form.saleAmountMin" placeholder="请输入起售金额" />
          </el-form-item>
          <el-form-item label="包邮订单最小金额" prop="freightThreshold">
            <el-input-number :min="0" :max="99999" v-model="form.freightThreshold" placeholder="请输入包邮订单最小金额" />
          </el-form-item>
          <el-form-item label="统一运费" prop="freight">
            <el-input-number :min="0" :max="9999" v-model="form.freight" placeholder="请输入统一运费" />
          </el-form-item>
          <el-form-item>
            <el-button type="default" @click="cancelForm">取 消</el-button>
            <el-button type="primary" @click="submitForm" :loading="submitLoading">提 交</el-button>
          </el-form-item>
        </el-form>
      </div>

    </el-card>
  </div>
</template>

<script>
import * as api from '@/api/mall/supplier/supplier-profile'
export default {
  name: 'SupplierProfileThresold',
  props: {
    supplierInfo: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      formMode: 'init',
      submitLoading: false,
      form: {
        saleAmountMin: 0,
        freightThreshold: 0,
        freight: 0
      },
      rules: {
        saleAmountMin: [
          { required: true, trigger: 'blur', message: '请输入起售金额' }
        ],
        freightThreshold: [
          { required: true, trigger: 'blur', message: '请输入包邮订单最小金额' }
        ],
        freight: [
          { required: true, trigger: 'blur', message: '请输入统一运费' }
        ],
      }
    }
  },
  methods: {
    edit() {
      this.formMode = 'edit'
      Object.keys(this.form).forEach(key => {
        this.form[key] = this.supplierInfo[key]
      })
    },
    cancelForm() {
      this.formMode = 'init'
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return
        }
        this.doSubmit()
      })
    },
    async doSubmit() {
      this.submitLoading = true
      try {
        let data = {...this.supplierInfo}
        Object.assign(data, this.form)
        await api.updateSupplierProfile(data)
        this.$modal.msgSuccess("修改成功")

        this.$emit('on-update')
        this.submitLoading = false
        this.formMode = 'init'
      } catch(e) {
        this.$modal.msgError("保存失败，请稍后重试")
        this.submitLoading = false
      }
    },
  }

}
</script>

<style>

</style>