<template>
  <div>
    <el-cascader-panel
      v-if="panel"
      ref="elCascader"
      :value="selectedVal"
      :options="options"
      :props="selectProps"
      :clearable="clearable"
      :disabled="disabled"
      :separator="separator"
      :show-all-levels="showAllLevels"
      @input="onInput" 
      @change="handleChange"
      style="width: 100%; max-height: 490px; overflow-y: auto"
      >
    </el-cascader-panel>
    <el-cascader
      v-else
      ref="elCascader"
      :value="selectedVal"
      :options="options"
      :props="selectProps"
      :clearable="clearable"
      :disabled="disabled"
      :separator="separator"
      :show-all-levels="showAllLevels"
      @input="onInput" 
      @change="handleChange"
      style="width: 100%; max-height: 490px; overflow-y: auto"
      >
    </el-cascader>
  </div>
</template>

<script>
import { getProductCategoryRoots, getProductCategoryChildrenTree } from '@/api/mall/product/spu'
import {getJdChildCategoryList, getJdChildCategoryListByParentId} from '@/api/mall/product/vopCategoryMapping'
export default {
  name: 'ProductCategorySelect',
  props: {
    value: {
      type: Array,
      default: () => null
    },
    extParams: {
      type: Object,
      default: () => {}
    },
    extProps: {
      type: Object,
      default: () => {}
    },
    placeholder: {
      type: String,
      default: () => '请选择商品分类'
    },
    separator: {
      type: String,
      default: () =>  '/'
    },
    autofocus: {
      type: Boolean,
      default: () => false
    },
    borderless: {
      type: Boolean,
      default: () => false
    },
    disabled: {
      type: Boolean,
      default: () => false
    },
    panel: {
      type: Boolean,
      default: () => false
    },
    clearable: {
      type: Boolean,
      default: () => true
    },
    filterable: {
      type: Boolean,
      default: () => false
    },
    showAllLevels: {
      type: Boolean,
      default: () => true
    },
    multiple: {
      type: Boolean,
      default: () => false
    },
    readonly: {
      type: Boolean,
      default: () => false
    },
    size: {
      type: String,
      default: () => ''
    },
    type: {
      type: String,
      default: 'platform',
      validator: (value)=>['platform','jd'].includes(value)
    }
  },
  data() {
    return {
      options: []
    }
  },
  computed: {
    selectedVal() {
      return this.value
    },
    selectProps() {
      let defaults = {
        multiple: false,
        checkStrictly: false,
        emitPath: true,
        value: 'categoryId',
        label: 'categoryName',
        children: 'childCategoryList',
        lazy: true,
        lazyLoad: (node, resolve) => {
          if (this.type === 'jd' && (node.level === 1 || node.level === 2 || node.level ===3)) {
            this.getJdChildrenCategory(node).then(res => {
              resolve(res)
            })
          }
          else if (node.level === 1 && this.type != 'jd') {
            this.getChildrenCategory(node).then(res => {
              resolve(res)
            })
          } else if (node.children) {
            resolve({})
          }
        }
      }
      return Object.assign(defaults, this.extProps)
    }
  },
  created() {
    this.loadRootCategory()
  },
  methods: {
    async loadRootCategory() {
      let params = Object.assign({status: 'ENABLE'}, this.extParams)
      let res;
      if(this.type === 'jd') {
        res = await getJdChildCategoryList()
      }
      else{
       res = await getProductCategoryRoots(params)
      }
      if(res.code === 0) {
        this.options = res.data
      }
    },
    loadData(){
      this.loading= true;
      this.options =[];
      this.loadRootCategory()
    },
    async getJdChildrenCategory(node){
      let params = this.extParams ? Object.assign({}, this.extParams) : {};
      params.parentCategoryId = node.value
      let res = await getJdChildCategoryListByParentId(params)
      if(res.code ===0){
        return res.data.map(x => {
          x.leaf = node.level === 2 || node.level === 3
          return x
        })
      }
      this.$message.error(res.msg)
      return []
    },


    async getChildrenCategory(node) {
      let params = Object.assign({status: 'ENABLE'}, this.extParams)
      params.parentCategoryId = node.value
      let res = await getProductCategoryChildrenTree(params)
      if (res.code === 0) {
        return res.data.map(x => {
          if (x.childCategoryList) {
            x.childCategoryList.map(y => {
              y.leaf = !y.childCategoryList || y.childCategoryList.length === 0
              return y
            })
          }
          x.leaf = !x.childCategoryList || x.childCategoryList.length === 0
          return x
        })
      }
      this.$message.error(res.msg)
      return []
    },
    getCheckedNodes(leafOnly = false) {
      return this.$refs.elCascader.getCheckedNodes(leafOnly)
    },
    getPathInfo(leafOnly = false) {
      let nodes =  this.getCheckedNodes(leafOnly)
      let node = nodes && nodes.length ? nodes[0] : {};
      return  {
        ids: node.path,
        labels: node.pathLabels
      }
    },
    onInput(val){
      this.$emit("input", val);
    },
    handleChange(val) {
      let pathInfo = this.getPathInfo()
      this.$emit("change", val, pathInfo);
    }
  }
}
</script>

<style>

</style>