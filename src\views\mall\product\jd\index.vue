<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="商品名称" prop="skuName">
        <el-input v-model="queryParams.skuName" placeholder="请输入商品名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="京东sku" prop="skuInnerId">
        <el-input v-model="queryParams.skuInnerId" placeholder="请输入京东sku" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="商品分类" prop="fullCategoryName">
        <el-input v-model="queryParams.fullCategoryName" placeholder="请输入分类名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['mall:vop-sku-category:create']">添加</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-collection-tag" size="mini" @click="handleImport"
                   v-hasPermi="['mall:vop-sku-category:import']">导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['mall:vop-sku-category:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list"
    @selection-change="handleSelectionChange"
    @sort-change="handleSortChange"
    :default-sort = "{prop: 'createTime', order: 'descending'}">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column label="商品信息" align="center" width="260">
        <template v-slot="scope">
          <div class="product-info">
            <img v-if="scope.row.picUrl" :src="scope.row.picUrl" alt="分类图片" class="img-height" />
            <div :title="scope.row.skuName" class="message">{{ scope.row.skuName }}</div>
          </div>
        </template>
        <!-- TODO 前端优化：可以有个 + 号，点击后，展示每个 sku -->
      </el-table-column>
      <el-table-column label="京东sku" align="center" prop="skuInnerId" />
      <el-table-column label="商品分类" align="center" prop="fullCategoryName" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180" sortable="custom">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['mall:vop-sku-category:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" 
                    :loading="deleteLoading" v-hasPermi="['mall:vop-sku-category:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="500px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="京东sku" prop="skuInnerId">
          <el-input :readonly="form.id" v-model="form.skuInnerId" placeholder="请输入京东sku" />
        </el-form-item>
        <el-form-item label="商品分类" prop="fullCategoryId">
          <span class="category-path">{{ form.fullCategoryName }}</span> 
          <el-button size="small" type="text" text @click="categoryOpen = true">编辑分类</el-button>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <category-dialog :open.sync="categoryOpen" @change="handleCategoryChange"/>

    <!-- 京东商品导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="550px" append-to-body>
      <div class="flex-vertical-center">
        <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
          :action="upload.url" :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip text-center" slot="tip">
            <span>仅允许导入xls、xlsx格式文件，建议单次导入不超过5000条</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
          </div>
        </el-upload>
        <ImportAlert v-if="upload.open" ref="importAlert" @on-complete="getList"></ImportAlert>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="importLoading" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { createVopSkuCategory, updateVopSkuCategory, deleteVopSkuCategory, getVopSkuCategory, getVopSkuCategoryPage, exportVopSkuCategoryExcel, getImportTemplate } from "@/api/mall/product/vopSkuCategory";
import CategoryDialog from "@/views/mall/product/spu/components/category-dialog";
import {getBaseHeader} from "@/utils/request";
import ImportAlert from '@/components/AsyncTaskAlert/import'
export default {
  name: "VopSkuCategory",
  components: {
    CategoryDialog,
    ImportAlert
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      importLoading: false,
      // 导出遮罩层
      exportLoading: false,
      // 提交表单遮罩层
      submitLoading: false,
      // 删除遮罩层
      deleteLoading: false,
      // 选择商品分类对话框
      categoryOpen: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 京东sku分类关系列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        skuInnerId: null,
        categoryId: null,
        categoryName: null,
        fullCategoryId: null,
        fullCategoryName: null,
        createTime: [],
        skuName: null,
        picUrl: null,
      },
      multipleSelection: [],
      // 导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/mall/vop-sku-category/import',
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        categoryId: [{ required: true, message: "分类ID不能为空", trigger: "blur" }],
        categoryName: [{ required: true, message: "分类名称不能为空", trigger: "blur" }],
        fullCategoryId: [{ required: true, message: "完整分类id -分隔不能为空", trigger: "blur" }],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    resetCategoryIdNames() {
      this.form.categoryId = null
      this.form.categoryName = ''
      this.form.fullCategoryId = ''
      this.form.fullCategoryName = ''
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      if(this.queryParams.skuInnerId === ''){
        this.queryParams.skuInnerId = null
      }
      // 执行查询
      getVopSkuCategoryPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        skuInnerId: undefined,
        categoryId: undefined,
        categoryName: undefined,
        fullCategoryId: undefined,
        fullCategoryName: undefined,
        skuName: undefined,
        picUrl: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "导入京东商品";
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "京东商品导入";
      this.importLoading = false
      this.upload.open = true;
      this.$nextTick(() => {
        this.$refs.upload.clearFiles();
      })
    },
    /** 下载模板操作 */
    importTemplate() {
      getImportTemplate().then(response => {
        this.$download.excel(response, '供应商商品导入模板.xls');
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.importLoading = false
      if (response.code !== 0) {
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        this.$modal.msgError(response.msg)
        return;
      }
      // this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$refs.importAlert.init2(response.data)
    },
    // 提交上传文件
    submitFileForm() {
      let files = this.$refs.upload.uploadFiles
      if(!files.length) {
        this.$modal.msg("请上传导入文件")
        return
      }

      this.importLoading = true
      this.$refs.upload.submit();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getVopSkuCategory(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改京东商品分类";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        this.submitLoading = true
        // 修改的提交
        if (this.form.id != null) {
          updateVopSkuCategory(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.submitLoading = false
            this.getList();
          });
        }
        else {
          // 添加的提交
          createVopSkuCategory(this.form).then(response => {
            this.submitLoading = false;
            this.$modal.msgSuccess("导入京东sku成功");
            this.open = false;
            this.getList();
          }).catch(error => {
            this.submitLoading = false;
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      var self = this
      this.$modal.confirm('是否确认删除京东商品?').then(function() {
          self.deleteLoading = true
          return deleteVopSkuCategory(id);
        }).then(() => {
          self.deleteLoading = false;
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {
          self.deleteLoading = false;
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有京东sku分类关系数据项?').then(() => {
          this.exportLoading = true;
          return exportVopSkuCategoryExcel(params);
        }).then(response => {
          this.$download.excel(response, '京东sku分类关系.xls');
          this.exportLoading = false;
        }).catch(() => {});
    },
    handleCategoryChange(val, pathInfo) {
      console.log('category-change: ', val, pathInfo)
      this.resetCategoryIdNames()
      this.form.fullCategoryId = val.join("-")
      this.form.fullCategoryName = pathInfo.labels.join("/")
      if(val && val.length) {
        if(val.length >= 1) {
          this.form.categoryId = pathInfo.ids[pathInfo.ids.length - 1]
          this.form.categoryName = pathInfo.labels[pathInfo.labels.length - 1]
        }
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleSortChange({ prop, order }) {
      this.queryParams.sortType = 10
      if(prop === 'createTime' && order === 'descending') {
        this.queryParams.sortType = 10
      } else if(prop === 'createTime' && order === 'ascending') {
        this.queryParams.sortType = 11
      } else if(prop === 'salesCount' && order === 'descending') {
        this.queryParams.sortType = 20
      } else if(prop === 'salesCount' && order === 'ascending') {
        this.queryParams.sortType = 21
      } else if(prop === 'salePrice' && order === 'descending') {
        this.queryParams.sortType = 40
      } else if(prop === 'salePrice' && order === 'ascending') {
        this.queryParams.sortType = 41
      }
      console.log(prop, order)
      this.handleQuery()
    }
  }
};
</script>
<style lang="scss">
.app-container {
  .product-info {
    display: flex;

    .img-height {
      height: 50px;
      width: 50px;
    }

    .message {
      margin-left: 10px;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      word-break: break-all;
      -webkit-box-orient: vertical;
      white-space: normal;
      overflow: hidden;
      height: 50px;
      line-height: 25px;
    }
  }
}
</style>