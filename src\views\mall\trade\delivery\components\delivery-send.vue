<template>
  <div class="delivery-send">
    <el-dialog title="订单发货" width="900px" v-dialogDrag append-to-body :visible.sync="open">
      <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="120px">
        <el-form-item label="发货方式" prop="deliveryOrderWay">
          <el-radio-group v-model="deliveryOrderWay">
            <el-radio :label="0">物流发货</el-radio>
            <el-radio :label="1">供应商自配送</el-radio>
            <el-radio :label="2">虚拟商品/服务类</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item v-if="deliveryOrderWay === 0" label="物流单号" prop="num">
              <el-input v-model="form.num" placeholder="请输入物流单号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="deliveryOrderWay === 0" label="物流公司" prop="com">
              <!-- <el-select style="width: 100%;" v-model="form.com" placeholder="请选择公司名称">
                <el-option v-for="item in deliveryCompanyList" :key="item.com" :label="item.name"
                  :value="item.com"></el-option>
              </el-select> -->
              <el-autocomplete class="inline-input" v-model="form.com" :fetch-suggestions="querySearch"
                placeholder="请选择快递" @select="handleSelectCom"></el-autocomplete>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="物流详情" required v-if="deliveryOrderWay === 1">
          <el-input type="textarea" :autosize="{ minRows: 5, maxRows: 10 }" placeholder="请输入物流详情"
            v-model="selfDeliveryContent" :maxlength="300" show-word-limit />
        </el-form-item>
        <el-form-item label="订单商品" required v-if="deliveryOrderWay !== 2 && skuList">
          <el-table ref="multipleTable" v-if="skuList" :data="skuList" style="width: 100%;"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="skuName" label="商品">
              <template v-slot="{ row }">
                <div class="flex-start">
                  <img v-if="row.imageUrl" :src="row.imageUrl" alt="图片" style="height: 30px;" />
                  <span>{{ row.skuName }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="skuId" label="商品SKU" width="200"></el-table-column>
            <el-table-column prop="skuPrice" label="单价(元)" width="120">
              <template v-slot="{ row }">
                {{ formatMoney(row.skuPrice) }}
              </template>
            </el-table-column>
            <el-table-column prop="skuNum" label="数量" width="100" />
          </el-table>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as orderApi from "@/api/mall/trade/order.js"
import * as deliveryApi from "@/api/mall/trade/delivery"
import * as deliveryCompanyApi from "@/api/mall/trade/deliveryCompany"
import { formatDate } from "@/utils/dateUtils";
export default {
  name: 'DeliverySend',
  props: {
    orderId: {
      type: Number,
      default: null
    },
    orders: {
      type: Array,
      default: () => ([])
    },
    open: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      rules: {
        num: [{ required: true, message: "物流单号不能为空", trigger: "blur" }],
        com: [{ required: true, message: "物流公司不能为空" }]
      },
      deliveryOrderWay: 0, // 发货方式 0-物流发货 1-自配送 2-虚拟商品/服务类
      selfDeliveryContent: '您的包裹已出库，正在配送中......联系人：某某某，联系方式：00000000000',
      loading: false, // 加载状态
      form: {
        num: '', // 物流单号
        com: '', // 物流公司
        skuId: [] // 订单商品SKU列表
      },
      deliveryCompanyList: [],
      skuList: [], // 订单商品列表
    };
  },
  watch: {
    orderId(newVal, oldVal) {
      if (newVal && newVal !== oldVal) {
        this.initOrderDetail()
      }
    },
    orders(newVal, oldVal) {
      if (newVal && newVal !== oldVal) {
        this.initOrderDetail()
      }
    },
    open(newVal, oldVal) {
      if (newVal && newVal !== oldVal) {
        this.deliveryOrderWay = 0
        this.$nextTick(() => {
          if (this.$refs.form) {
            this.$refs.form.clearValidate(); // 清除校验提示
          }
          if (document.activeElement) {
            document.activeElement.blur()
          }
        })
      }
      // 关闭时也移除焦点
      if (!newVal) {
        this.$nextTick(() => {
          if (document.activeElement) {
            document.activeElement.blur()
          }
        })
      }
    },
    deliveryOrderWay(newVal, oldVal) {
      this.$nextTick(() => {
        if (this.$refs.multipleTable) {
          this.$refs.multipleTable.clearSelection()
          this.$refs.multipleTable.toggleAllSelection()
        }
      })
    }
  },
  mounted() {
  },
  computed: {
    // 计算属性可以在这里定义
  },
  created() {
    // 组件创建时的逻辑
    this.deliveryOrderWay = 0
    this.initOrderDetail()
  },
  methods: {
    initOrderDetail() {
      if (this.orders.length == 1) {
        this.skuList = this.orders[0].skuInfoList || this.orders[0].productList || []
      }
      else if (this.orderId !== null && this.orderId !== '') {
        orderApi.getOrderDetail({
          id: this.orderId || ''
        }).then(res => {
          if (res && res.data) {
            this.orders = [res.data]
            this.skuList = this.orders[0].skuInfoList || []
          }
        })
      }
      else {
        this.skuList = null
        this.form.skuId = null
      }

      this.$nextTick(() => {
        if (this.$refs.multipleTable) {
          this.$refs.multipleTable.toggleAllSelection()
        }
      })
    },
    querySearch(queryString, cb) {
      deliveryCompanyApi.getDeliveryCompanyPage({
        pageSize: 500,
        pageNo: 1,
        name: queryString == '' ? null : queryString
      }).then(res => {
        if (res.code == 0) {
          const list = (res.data.list || [])
          const filterComList = list.map(x => {
            return {
              'value': x.name,
              'code': x.com
            }
          })
          cb(filterComList);
        }
      })
    },
    handleSelectionChange(val) {
      this.form.skuId = val.map(x => x.skuId)
    },
    handleSelectCom(item) {
      this.form.com = item.value
      this.form.code = item.code
    },
    submitForm() {
      if (this.deliveryOrderWay === 0) {
        this.$refs["form"].validate(valid => {
          if (!valid) {
            return;
          }
          const params = {
            orderNo: this.formatOrderNos(),
            companyCode: this.form.code,
            companyName: this.form.com,
            num: this.form.num,
            skuIds: this.formatSkuIds()
          }
          if (this.orders.length == 1 && (this.form.skuId == null || this.form.skuId.length == 0)) {
            this.$message.warning('请选择订单商品')
            return;
          }
          this.productSend(params)
        });
      }
      else if (this.deliveryOrderWay === 1) {
        if (this.form.skuId == null || this.form.skuId.length == 0) {
          this.$message.warning('请选择订单商品')
          return;
        }
        if (this.selfDeliveryContent == null || this.selfDeliveryContent.length == 0) {
          this.$message.warning('请输入自配送物流详情')
          return;
        }
        else if (this.selfDeliveryContent.includes("某某某") || this.selfDeliveryContent.includes("00000000000")) {
          this.$message.warning('请修改物流详情中联系人和联系方式')
          return;
        }
        const params = {
          orderNo: this.formatOrderNos(),
          skuIds: this.formatSkuIds(),
          detail: this.selfDeliveryContent
        }
        this.selfSend(params)
      }
      else if (this.deliveryOrderWay === 2) {
        const params = {
          orderNo: this.formatOrderNos()
        }
        this.serviceSend(params)
      }
    },
    async productSend(params) {
      try {
        this.loading = true
        const res = await deliveryApi.productSend(params)
        if (res.code == 0) {
          this.$message.success('发货成功')
        }
        this.close(true)
      }
      finally {
        this.loading = false
      }
    },
    async serviceSend(params) {
      try {
        this.loading = true
        const res = await deliveryApi.serviceSend(params)
        if (res.code == 0) {
          this.$message.success('发货成功')
        }

        this.close(true)
      }
      finally {
        this.loading = false
      }
    },
    async selfSend(params) {
      try {
        this.loading = true
        const res = await deliveryApi.selfSend(params)
        if (res.code == 0) {
          this.$message.success('发货成功')
        }
        this.close(true)
      }
      finally {
        this.loading = false
      }
    },
    deleteDelivery() {
      this.$modal.confirm('该操作将会清理订单绑定的物流信息，确认执行此操作吗?').then(() => {
        this.loading = true
        deliveryApi.deleteByOrder(this.order.no).then(res => {
          this.loading = false
          if (res.code == 0) {
            this.$message.success('删除物流成功')
          }
          else {
            this.$message.error(res.msg || '删除物流失败')
          }

          this.close(true)
        })
      })
    },
    cancel() {
      this.close(false)
    },
    close(val) {
      this.deliveryOrderWay = 0
      this.form = {
        num: '',
        com: '',
        code: '',
        skuId: []
      }
      this.$nextTick(() => {
        if (document.activeElement) {
          document.activeElement.blur()
        }
      })

      this.$emit('update:open', false)
      this.$emit("close", val)
    },
    formatOrderNos() {
      if (this.orders.length === 1) {
        return this.orders[0].no
      }
      else {
        return this.orders.map(x => x.no).join(',')
      }
    },
    formatSkuIds() {
      if (this.form.skuId == null || this.form.skuId.length === 0) {
        return ''
      }
      else if (this.form.skuId.length === 1) {
        return this.form.skuId[0]
      }
      else {
        return this.form.skuId.map(x => {
          return x
        }).join(',')
      }
    }
  },
}
</script>

<style></style>
