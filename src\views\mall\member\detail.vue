<template>
  <div class="app-container orderList-detail-page" v-loading="loading">
    <template>
      <!-- 会员信息 -->
      <el-descriptions v-if="member" title="会员信息" direction="vertical" :column="4" border>
        <el-descriptions-item label="昵称">{{ member.nickname || '--' }}</el-descriptions-item>
        <el-descriptions-item label="员工编号">{{ member.userNo || '--' }}</el-descriptions-item>
        <el-descriptions-item label="部门编码">{{ member.deptCode || '--' }}</el-descriptions-item>
        <el-descriptions-item label="部门名称">{{ member.deptName || '--' }}</el-descriptions-item>
        <el-descriptions-item label="职务名称">{{ member.jobTitle || '--' }}</el-descriptions-item>
        <el-descriptions-item label="员工姓名">{{ member.name || '--' }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ member.mobile || '--' }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="member.status"/>
        </el-descriptions-item>
        <el-descriptions-item label="注册IP">{{ member.registerIp || '--' }}</el-descriptions-item>
        <el-descriptions-item label="最近登录IP">{{ member.loginIp || '--' }}</el-descriptions-item>
        <el-descriptions-item label="最近登录时间">{{ parseTime(member.loginDate) || '--' }}</el-descriptions-item>
      </el-descriptions>

      <!-- 订单信息 -->
      <el-descriptions style="margin-top: 20px;" title="订单信息" :colon="false" direction="vertical">
        <el-descriptions-item label="">
          <el-table :data="orderList" border >
            <el-table-column prop="no" label="订单号" width="200"></el-table-column>
            <el-table-column label="商品信息" >
              <template slot-scope="{ row }">
                <div class="goodsCard">
                  <div class="goodsCard-item" v-for="item in row.items" :key="item.skuId">
                    <div class="goodsCard-item-img">
                      <img :src="item.picUrl" alt="" width="30px" height="30px" />
                    </div>
                    <div class="goodsCard-item-info">
                      <div class="goodsCard-item-info-name">{{ item.skuName || '--' }}</div>
                      <div class="goodsCard-item-info-code">{{ item.skuId || '--' }}</div>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="下单金额" align="center" width="140">
              <template slot-scope="{ row }">
                <div>{{ formatMoney(row.orderPrice) }}</div>
              </template>
            </el-table-column>
            <el-table-column label="订单状态" align="center" width="140">
              <template slot-scope="{ row }">
                <div class="status-box">
                  <div class="status-ball" :style="{ backgroundColor: getStatusColor(row.status) }"></div>
                  <el-tag :type="row.status | orderStatusStyle">{{ row.status | orderStatusInfo}}</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="下单时间" align="center" width="200">
              <template slot-scope="{ row }">
                <div>{{ parseTime(row.createTime) }}</div>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="110">
              <template v-slot="scope">
                <el-button size="medium" type="text" @click="toDetail(scope.row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-descriptions-item>
      </el-descriptions>

      <pagination v-show="total > 0" :total="total" :page.sync="pageNo" :limit.sync="pageSize"
        @pagination="getRefOrder" />
    </template>
  </div>
</template>

<script>
  import { getDetail, getRefOrder } from "@/api/mall/member/member.js"

export default {
  name: "MemberDetail",
  data() {
    return {
      loading: false,
      member: null,
      orderList: [],
      total: 0,
      pageNo: 1,
      pageSize: 10
    }
  },
  created() {
    this.getDetail()
  },
  methods: {
    getStatusColor(status) {
      if ([1, 2, 3, 6].includes(status)) {
        return '#FF9500'
      } else if (status === 9) {
        return '#E5E6EB'
      }
      return '#00B42A'
    },
    async getDetail() {
      const res = await getDetail({
        id: this.$route.query.id
      })
      if (res.code === 0 && res.data) {
        this.member = res.data
        this.getRefOrder()
      }
    },
    async getRefOrder() {
      this.loading = true
      const res = await getRefOrder({
        userId: this.$route.query.id,
        pageNo: this.pageNo,
        pageSize: this.pageSize
      })
      if (res.code === 0 && res.data) {
        this.orderList = res.data.list
        this.total = Number(res.data.total)
      } else {
        this.total = 0
        this.orderList = []
      }
      this.loading = false
    },
    toDetail(row) {
      this.$router.push({ name: 'TradeOrderDetail', query: { id: row.id } })
    }
  }
}
</script>

<style lang="scss" scoped>
.goodsCard {
  display: block;
  .goodsCard-item {
    display: flex;
    width: 100%;
    + .goodsCard-item {
      margin-top: 20px;
    }
    .goodsCard-item-img {
      width: 30px;
      height: 30px;
      position: relative;
    }
    .goodsCard-item-info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-left: 8px;
      font-size: 12px;
      line-height: 20px;
      width: 100%;
      .goodsCard-item-info-name {
        width: 100%;
        white-space: pre-wrap;
        color: #1d2129;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .goodsCard-item-info-code {
        color: #86909c;
      }
    }
  }
}
.status-box {
  display: flex;
  align-items: center;
  justify-content: center;
  .status-ball {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 10px;
  }
}
</style>
