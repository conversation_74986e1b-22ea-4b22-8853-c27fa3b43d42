<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="API编码" prop="apiCode">
        <el-input v-model="queryParams.apiCode" placeholder="请输入API编码" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="API名称" prop="apiName">
        <el-input v-model="queryParams.apiName" placeholder="请输入API名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="状态" prop="statusList">
        <el-select v-model="queryParams.statusList" multiple @keyup.enter.native="handleQuery">
          <el-option :value="0" label="失败"/>
          <el-option :value="1" label="成功"/>
          <el-option :value="2" label="丢弃"/>
        </el-select>
      </el-form-item>
      <el-form-item label="调用时间" prop="callTime">
        <el-date-picker v-model="queryParams.callTime" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="API编码" align="center" prop="apiCode" />
      <el-table-column label="API名称" align="center" prop="apiName" />
      <el-table-column label="调用实体ID" align="center" prop="callEntityId" />
      <el-table-column label="调用结果" align="center" prop="callResp" width="200" />
      <el-table-column label="调用次数" align="center" prop="callCount" />
      <el-table-column label="调用状态" align="center" prop="status">
        <template v-slot="scope">
          <el-tag :type="['error','success','warning'][scope.row.status]">{{ ['失败','成功','丢弃'][scope.row.status] }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="callTime">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.callTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleRetry(scope.row)"
                     v-hasPermi="['mall:api-fail:update']">重试</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['mall:api-fail:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

  </div>
</template>

<script>
import * as api from "@/api/mall/config/apiFail";
export default {
  name: "ApiFailList",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        apiCode: null,
        apiName: null,
        sortType: 1,
        callTime: [],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      let params = {...this.queryParams}
      // fix 61
      if (!params.apiCode) {
        params.apiCode = null
      }
      if (!params.apiName) {
        params.apiName = null
      }
      if (params.callTime && params.callTime.length > 0) {
        params.callTime = params.callTime.join(',')
      }

      // 执行查询
      api.getApiFailPage(params).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 重试按钮操作 */
    handleRetry(row) {
      console.log(row)
      let params = {
        id: row.id
      }
      api.retryHandle(params).then(res => {
        this.getList();
        this.$modal.msgSuccess("重试成功");
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除数据项?').then(function() {
          return api.deleteApiFail(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
  }
};
</script>
