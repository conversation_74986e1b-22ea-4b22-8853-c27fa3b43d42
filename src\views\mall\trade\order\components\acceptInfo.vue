<template>
  <div v-if="enabled" class="item-acceptance-tag"> 
    <el-tag size="medium" title="报销材料上传状态" :type="acceptStatus | orderItemAcceptStatusStyle" @click="showInfo">{{ acceptStatus | orderItemAcceptStatusInfo }}</el-tag>

    <el-drawer
      title="查看详情"
      :visible.sync="drawerVisible"
      append-to-body
      direction="rtl"
      size="60%">
      <div class="app-container">
        <el-descriptions title="" direction="vertical" :column="1" border>
          <el-descriptions-item label="报销材料信息">
            <AcceptDetail :orderItemId="orderItemId"></AcceptDetail>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-drawer>

  </div>
</template>

<script>
import * as api from '@/api/mall/trade/acceptance'
import { configMixins } from '@/views/mall/config/components/configMixin.js'
import AcceptDetail from '@/views/mall/trade/order/components/acceptDetail'
export default {
  name: 'OrderItemAcceptFile',
  mixins: [ configMixins ],
  components: { AcceptDetail },
  props: {
    orderItem: {
      type: Object,
      default() {
        return null
      }
    },
    orderStatus: {
      type: Number,
      default() {
        return null
      }
    }
  },
  data() {
    return {
      drawerVisible: false,
      loading: false,
      acceptanceInfo: {}
    }
  },
  computed: {
    orderItemId() {
      return this.orderItem ? this.orderItem.orderItemId || this.orderItem.id : null
    },
    acceptStatus() {
      return this.orderItem ? this.orderItem.acceptStatus : null
    },
    enabled() {
      return this.isAcceptSwitch && this.orderItemId && this.orderStatus === 8
    }
  },
  methods: {
    async showInfo() {
      if(!this.acceptStatus) {
        return
      }
      this.drawerVisible = true
    }
  }

}
</script>

<style lang="scss" scoped>
.item-acceptance-tag {
  margin-left: 5px;
  cursor: pointer;
}

</style>