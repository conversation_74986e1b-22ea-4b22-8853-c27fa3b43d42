<template>
    <div class="app-container">

        <!-- 搜索工作栏 -->
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-width="68px">
            <el-form-item label="应用名" prop="name">
                <el-input v-model="queryParams.name" placeholder="请输入应用名" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="渠道编码" prop="code">
                <el-select v-model="queryParams.code" placeholder="请选择渠道编码" clearable size="small">
                    <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_SOCIAL_MESSAGE_CHANNEL)"
                        :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
                    <el-option v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)" :key="dict.value"
                        :label="dict.label" :value="dict.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="创建时间" prop="createTime">
                <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss"
                    type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                    :default-time="['00:00:00', '23:59:59']" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <!-- 操作工具栏 -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                    v-hasPermi="['system:social-message-channel:create']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                    :loading="exportLoading" v-hasPermi="['system:social-message-channel:export']">导出</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <!-- 列表 -->
        <el-table v-loading="loading" :data="list">
            <el-table-column label="编号" align="center" prop="id" />
            <el-table-column label="应用名" align="center" prop="name" />
            <el-table-column label="渠道编码" align="center" prop="code" />
            <el-table-column label="通道编号" align="center" prop="clientId" />
            <el-table-column label="通道密钥" align="center" prop="clientSecret" />
            <el-table-column label="代理编号" align="center" prop="agentId" />
            <el-table-column label="启用状态" align="center" prop="status">
                <template v-slot="scope">
                    <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
                </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                <template v-slot="scope">
                    <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template v-slot="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['system:social-message-channel:update']">修改</el-button>
                    <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['system:social-message-channel:delete']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页组件 -->
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
            @pagination="getList" />

        <!-- 对话框(添加 / 修改) -->
        <el-dialog :title="title" :visible.sync="open" width="800px" v-dialogDrag append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="应用名" prop="name">
                    <el-input v-model="form.name" placeholder="请输入应用名" />
                </el-form-item>
                <el-form-item label="渠道编码" prop="code">
                    <el-select v-model="form.code" placeholder="请选择渠道编码">
                        <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_SOCIAL_MESSAGE_CHANNEL)"
                            :key="dict.value" :label="dict.label" :value="dict.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="通道编号" prop="clientId">
                    <el-input v-model="form.clientId" placeholder="请输入客户端编号，对应各平台appKey" />
                </el-form-item>
                <el-form-item label="通道密钥" prop="clientSecret">
                    <el-input v-model="form.clientSecret" placeholder="请输入客户端密钥，对应各平台appSecret" />
                </el-form-item>
                <el-form-item v-if="showAgentId()" label="代理编号" prop="agentId">
                    <el-input v-model="form.agentId" placeholder="授权方的网页应用ID，有则填" />
                </el-form-item>
                <el-form-item label="状态">
                    <el-radio-group v-model="form.status">
                        <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)" :key="dict.value"
                            :label="parseInt(dict.value)">{{ dict.label }}</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { createSocialMessageChannel, updateSocialMessageChannel, deleteSocialMessageChannel, getSocialMessageChannel, getSocialMessageChannelPage, exportSocialMessageChannelExcel } from "@/api/system/social/message/channel";
import { DICT_TYPE, getDictDataLabel } from "@/utils/dict";
export default {
    name: "SocialMessageChannel",
    components: {
    },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 导出遮罩层
            exportLoading: false,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 三方消息通道列表
            list: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNo: 1,
                pageSize: 10,
                name: null,
                code: null,
                clientId: null,
                clientSecret: null,
                agentId: null,
                status: null,
                createTime: [],
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                name: [{ required: true, message: "应用名不能为空", trigger: "blur" }],
                code: [{ required: true, message: "渠道编码不能为空", trigger: "change" }],
                clientId: [{ required: true, message: "通道编号不能为空", trigger: "blur" }],
                clientSecret: [{ required: true, message: "通道密钥不能为空", trigger: "blur" }],
                status: [{ required: true, message: "状态不能为空", trigger: "blur" }],
            }
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询列表 */
        getList() {
            this.loading = true;
            // 执行查询
            getSocialMessageChannelPage(this.queryParams).then(response => {
                this.list = response.data.list;
                this.total = response.data.total;
                this.loading = false;
            });
        },
        /** 取消按钮 */
        cancel() {
            this.open = false;
            this.reset();
        },
        /** 表单重置 */
        reset() {
            this.form = {
                id: undefined,
                name: undefined,
                code: undefined,
                clientId: undefined,
                clientSecret: undefined,
                agentId: undefined,
                status: undefined,
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNo = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        showAgentId() {
            const socialLabel = getDictDataLabel(DICT_TYPE.SYSTEM_SOCIAL_MESSAGE_CHANNEL, this.form.code)
            if (socialLabel === '企业微信' || socialLabel === '华中农业大学门户待办') {
                return true;
            }
            return false;
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id;
            getSocialMessageChannel(id).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (!valid) {
                    return;
                }
                // 修改的提交
                if (this.form.id != null) {
                    updateSocialMessageChannel(this.form).then(response => {
                        this.$modal.msgSuccess("修改成功");
                        this.open = false;
                        this.getList();
                    });
                    return;
                }
                // 添加的提交
                createSocialMessageChannel(this.form).then(response => {
                    this.$modal.msgSuccess("新增成功");
                    this.open = false;
                    this.getList();
                });
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const id = row.id;
            this.$modal.confirm('是否确认删除三方消息通道编号为"' + id + '"的数据项?').then(function () {
                return deleteSocialMessageChannel(id);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => { });
        },
        /** 导出按钮操作 */
        handleExport() {
            // 处理查询参数
            let params = { ...this.queryParams };
            params.pageNo = undefined;
            params.pageSize = undefined;
            this.$modal.confirm('是否确认导出所有三方消息通道数据项?').then(() => {
                this.exportLoading = true;
                return exportSocialMessageChannelExcel(params);
            }).then(response => {
                this.$download.excel(response, '三方消息通道.xls');
                this.exportLoading = false;
            }).catch(() => { });
        }
    }
};
</script>