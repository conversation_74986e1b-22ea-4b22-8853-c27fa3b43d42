<template>
    <div class="supplierStatisticsAfterSale" :style="{ height: height, width: width }" />
</template>

<script>
import * as api from "@/api/mall/statistics/supplier";
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '300px'
        }
    },
    data() {
        return {
            chart: null
        }
    },
    created() {
        this.getAfterSaleOrderSummary();
    },
    methods: {
        getAfterSaleOrderSummary() {
            api.getAfterSaleOrderSummary({}).then(response => {
                if (response.code === 0) {
                    this.initChart(response.data)
                }
            });
        },
        initChart(data) {
            const names = data.map(item => item.supplierName)
            const orderAmountValues = data.map(item => item.orderAmount)
            const orderCountValues = data.map(item => item.orderCount)
            this.chart = echarts.init(this.$el, 'macarons')
            this.chart.setOption({
                title: {
                    text: '供应商售后统计',
                    right: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { type: 'shadow' }
                },
                legend: {
                    data: ['售后金额', '售后单量'],
                    right: 'left',
                    top: 'top'

                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: names
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '售后金额'
                    },
                    {
                        type: 'value',
                        name: '售后单量',
                        position: 'right'
                    }
                ],
                series: [
                    {
                        name: '售后金额',
                        type: 'line',
                        yAxisIndex: 0,
                        data: orderAmountValues
                    },
                    {
                        name: '售后单量',
                        type: 'line',
                        yAxisIndex: 1,
                        data: orderCountValues
                    }
                ]
            })
        }
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    }
}
</script>