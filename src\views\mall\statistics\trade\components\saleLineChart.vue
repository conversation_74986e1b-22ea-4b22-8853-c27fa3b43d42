<template>
  <div style="margin: 10px 0"> 
    <div :id="conId" class="sale-line-chart" style="width: 100%; height: 500px;"></div>
  </div>
</template>

<script>
import resize from '@/views/dashboard/mixins/resize'
require('echarts/theme/macarons') // echarts theme
import * as echarts from 'echarts'
export default {
  name: 'SaleLineChart',
  mixins: [resize],
  props: {
  },
  data() {
    return {
      conId: 'line-' + new Date().getTime(),
      chart: null
    }
  },
  methods: {
    init(opts) {
      const chartDom = document.getElementById(this.conId);
      this.chart = echarts.init(chartDom, 'macarons')
      this.chart.setOption({
        legend: {
          data: opts.legend
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        grid: {
          left: '1%',
          right: '1%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: opts.xAxis,
        yAxis: opts.yAxis,
        series: opts.series
      })
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  }
}
</script>

<style lang="scss" scoped>

</style>