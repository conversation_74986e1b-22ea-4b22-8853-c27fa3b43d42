<template>
  <div class="app-container">
    <el-empty v-if="!configInfo.id && formMode === 'init'">
      <el-button type="text" icon="el-icon-plus" @click="beginConfig">开始配置</el-button>
    </el-empty>
    <div v-if="configInfo.id && formMode === 'init'">
      <el-descriptions title="配置信息" :column="1" :border="true">
        <template slot="extra">
          <el-button type="primary" size="small" @click="editConfig">修改</el-button>
        </template>
        <el-descriptions-item label="单位名称">{{ configInfo.orgName || '--' }}</el-descriptions-item>
        <el-descriptions-item label="所在省份">{{ configInfo.province || '--' }}</el-descriptions-item>
        <el-descriptions-item label="所在城市">{{ configInfo.city || '--' }}</el-descriptions-item>
        <el-descriptions-item label="开户行">{{ configInfo.bankName || '--' }}</el-descriptions-item>
        <el-descriptions-item label="开户行账号">{{ configInfo.accountNum || '--' }}</el-descriptions-item>
        <el-descriptions-item label="开户行联行号">{{ configInfo.unifyBankNum || '--' }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <el-form v-if="formMode === 'edit'" ref="configForm" label-position="top" :model="configForm" :rules="configRules"
      label-width="80px" style="width:900px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="单位名称" prop="orgName">
            <el-input v-model="configForm.orgName" maxlength="200" placeholder="请输入单位名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所在省份" prop="province">
            <el-input v-model="configForm.province" maxlength="30" placeholder="请输入所在省份" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所在城市" prop="city">
            <el-input v-model="configForm.city" maxlength="30" placeholder="请输入所在城市" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开户行" prop="bankName">
            <el-input v-model="configForm.bankName" maxlength="100" placeholder="请输入开户行" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户行账号" prop="accountNum">
            <el-input v-model="configForm.accountNum" maxlength="30" placeholder="请输入开户行账号" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开户行联行号" prop="unifyBankNum">
            <el-input v-model="configForm.unifyBankNum" maxlength="30" placeholder="请输入开户行联行号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
        </el-col>
      </el-row>
     
      <el-form-item>
        <el-button type="default" @click="cancelForm">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">提 交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import * as api from "@/api/mall/config/supplierAccount";
export default {
  name: "MallConfigSupplierAccount",
  components: {},
  props: {
    id: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      configInfo: {},
      formMode: 'init',
      submitLoading: false,
      configForm: {
        id: undefined,
        orgName: '',
        province: '',
        city: '',
        bankName: '',
        accountNum: '',
        unifyBankNum: ''
      },
      configRules: {
        orgName: [
          { required: true, trigger: 'blur', message: '请输入单位名称' }
        ],
        province: [
          { required: true, trigger: 'blur', message: '请输入所在省份' }
        ],
        city: [
          { required: true, trigger: 'blur', message: '请输入所在城市' }
        ],
        bankName: [
          { required: true, trigger: 'blur', message: '请输入开户行' }
        ],
        accountNum: [
          { required: true, trigger: 'blur', message: '请输入开户行账号' }
        ],
        unifyBankNum: [
          { required: true, trigger: 'blur', message: '请输入开户行联行号' }
        ]
      },
    }
  },
  created() {
    this.loadConfig()
  },
  methods: {
    beginConfig() {
      this.formMode = 'edit'
    },
    async loadConfig() {
      if(!this.id) {
        return
      }
      let res = await api.getSupplierAccountInfo(this.id);
      this.configInfo = res.data || {}
    },
    editConfig() {
      this.formMode = 'edit'
      Object.assign(this.configForm, this.configInfo)
    },
    cancelForm() {
      this.formMode = 'init'
    },
    async doSubmit() {
      this.submitLoading = true
      this.configForm.supplierId = this.id
      try {
        if (this.configForm.id !== undefined) {
          await api.updateSupplierAccount(this.configForm)
          this.$modal.msgSuccess("修改成功");
        } else {
          await api.createSupplierAccount(this.configForm)
          this.$modal.msgSuccess("保存成功");
        }
        this.submitLoading = false
        this.formMode = 'init'
        this.loadConfig()
      } catch(e) {
        this.$modal.msgError("保存失败，请稍后重试")
        this.submitLoading = false
      }
    },
    submitForm() {
      this.$refs["configForm"].validate(valid => {
        if (!valid) {
          return;
        }
        this.doSubmit()
      });
    }
  }
}
</script>

<style lang="scss" scoped></style>