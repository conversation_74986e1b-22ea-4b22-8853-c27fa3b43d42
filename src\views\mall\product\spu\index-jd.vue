<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" @keyup.enter.native="handleQuery" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="">
        <el-input v-model="queryParams.keyword" style="width: 280px">
          <el-select v-model="searchType" slot="prepend" style="width: 100px">
            <el-option v-for="dict in searchTypes" :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-input>
      </el-form-item>
      <!-- <el-form-item label="商品分类">
        <el-cascader v-model="categoryIds" placeholder="请输入商品分类" style="width: 360px"
                     :options="categoryList" :props="propCategory" clearable ref="category"/>
      </el-form-item> -->
      <el-form-item label="商品价格" style="width: 520px" class="form-item-range">
        <div style="display: flex;">
          <el-form-item>
            <el-input v-model="queryParams.minPrice" placeholder="最低价格" clearable/>
          </el-form-item>
          <div style="margin: 0 12px 0 2px;">-</div>
          <el-form-item>
            <el-input v-model="queryParams.maxPrice" placeholder="最高价格" clearable/>
          </el-form-item>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"/>
    </el-row>

    <el-table v-loading="loading" :data="list" border>
      <el-table-column label="商品编码" align="center" prop="skuId" />
      <el-table-column label="商品信息" align="center" width="360" show-overflow-tooltip>
        <template v-slot="scope">
          <div class="product-info">
            <img v-if="scope.row.imageUrl" :src="scope.row.imageUrl" alt="分类图片" class="img-height" />
            <div :title="scope.row.skuName" class="message">{{ scope.row.skuName }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="价格" align="center" prop="salePrice">
        <template v-slot="scope">
          <div>{{ formatMoney(scope.row.salePrice) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="库存" align="center" prop="stockStateDesc"/>
      <el-table-column label="商品分类" align="center">
        <template v-slot="scope">
          {{ scope.row.categoryName1 }} / {{ scope.row.categoryName2 }} / {{ scope.row.categoryName3 }}
        </template>
      </el-table-column>
      <el-table-column label="商品状态" align="center">
        <template v-slot="scope">
          {{ scope.row.skuState === 1 ? '上架' : '下架' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="medium" type="text"  icon="el-icon-view" @click="handleView(scope.row)">预览</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageIndex" :limit.sync="queryParams.pageSize"
      @pagination="getList"/>

  </div>
</template>

<script>
import { searchJDGoodsPageList } from "@/api/mall/product/sku.js"
import { getCategoryRootList, getCategoryChildTreeList } from "@/api/mall/product/category.js"
import * as configApi from "@/api/mall/config/basisConfig.js"
export default {
  name: "ProductSpuJd",
  data() {
    return {
      basisConfig: {},
      searchTypes: [
        { label: '商品名称', value: 'name' },
        { label: '商品编码', value: 'skuId' },
      ],
      searchType: 'name',
      propCategory: {
        checkStrictly: true,
        value: 'categoryId',
        label: 'categoryName',
        children: 'childCategoryList',
        lazy: true,
        lazyLoad: (node, resolve) => {
          if (node.level === 1) {
            this.getChildrenCategory(node).then(res => {
              resolve(res)
            })
          } else if (node.children) {
            resolve({})
          }
        }
      },
      categoryIds: [],
      categoryList: [],
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商品sku列表
      list: [],
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        keyword: '',
        minPrice: '',
        maxPrice: ''
      },
    };
  },
  watch: {
    "queryParams": {
      deep: true,
      handler(newVal, oldVal) {
        const ck = 'sku-jd-params'
        sessionStorage.setItem(ck, JSON.stringify(newVal))
      }
    }
  },
  created() {
    this.initParams();
    this.getList();
    this.getListCategory()
    this.loadConfig()
  },
  methods: {
    initParams() {
      const ck = 'sku-jd-params'
      const val = sessionStorage.getItem(ck)
      if(val) {
        Object.assign(this.queryParams, JSON.parse(val))
      }
    },
    async loadConfig() {
      let res = await configApi.getBasisConfig()
      if(res.code === 0) {
        this.basisConfig = res.data || {}
      }
    },
    /** 查询分类列表 */
    async getListCategory() {
      // 执行查询
      const res = await getCategoryRootList()
      if (res.code === 0) {
        this.categoryList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    async getChildrenCategory(node) {
      // 执行查询
      const res = await getCategoryChildTreeList({
        parentCategoryId: node.value
      })
      if (res.code === 0) {
        return res.data.map(x => {
          if (x.childCategoryList) {
            x.childCategoryList.map(y => {
              y.leaf = !y.childCategoryList || y.childCategoryList.length === 0
              return y
            })
          }
          x.leaf = !x.childCategoryList || x.childCategoryList.length === 0
          return x
        })
      }
      this.$message.error(res.msg)
      return []
    },
    /** 查询列表 */
    async getList() {
      this.loading = true;
      const [categoryId1, categoryId2, categoryId3] = this.categoryIds || []
      let { pageIndex, pageSize, keyword, minPrice, maxPrice } = this.queryParams
      if (minPrice && maxPrice && Number(minPrice) > Number(maxPrice)) {
        const temp = minPrice
        minPrice = maxPrice
        maxPrice = temp
        this.queryParams.minPrice = minPrice
        this.queryParams.maxPrice = maxPrice
      }
      const res = await searchJDGoodsPageList({
        pageIndex,
        pageSize,
        categoryId1,
        categoryId2,
        categoryId3,
        keyword,
        minPrice,
        maxPrice
      })
      if (res.code === 0) {
        this.list = res.data.pageResult.list
        this.total = Number(res.data.pageResult.total)
      } else {
        this.list = []
        this.total = 0
        this.$message.error(res.msg)
      }
      this.loading = false
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.categoryIds = []
      this.queryParams = {
        pageIndex: 1,
        pageSize: 10,
        keyword: '',
        minPrice: '',
        maxPrice: ''
      }
      this.handleQuery();
    },
    handleView(row) {
      let url = `http://${this.basisConfig.domain}/#/detail/${row.supplierId}/${row.skuId}`
      window.open(url, '_blank')
    }
  },
};
</script>
<style lang="scss">
.app-container {
  .product-info {
    display: flex;

    .message {
      margin-left: 10px;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      word-break: break-all;
      -webkit-box-orient: vertical;
      white-space: normal;
      overflow: hidden;
      height: 50px;
      line-height: 25px;
    }
  }
}
</style>
