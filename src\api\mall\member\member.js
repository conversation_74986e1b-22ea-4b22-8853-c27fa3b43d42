import request from '@/utils/request'

// 会员列表
export function getUserPage(data) {
  return request({
    url: '/member/user/page',
    method: 'post',
    data
  })
}

// 获取会员详情
export function getDetail(params) {
  return request({
    url: '/member/user/get-detail',
    method: 'get',
    params
  })
}

// 更新获得会员关联订单账单
export function getRefOrder(data) {
  return request({
    url: '/member/user/get-ref-order',
    method: 'post',
    data
  })
}

// 创建会员
export function createMemberUser(data) {
  return request({
    url: '/member/user/create',
    method: 'post',
    data: data
  })
}

// 更新会员
export function updateMemberUser(data) {
  return request({
    url: '/member/user/update',
    method: 'put',
    data: data
  })
}

// 删除会员
export function deleteMemberUser(params) {
  return request({
    url: '/member/user/delete',
    method: 'delete',
    params
  })
}
