<template>
  <div class="skuCountCategoryRanking" :style="{ height: height, width: width }" />
</template>

<script>
import * as api from "@/api/mall/statistics/commodity";
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  created() {
    Promise.all([
      this.getProductCountByCategory(),
      this.getSellProductCategorySummary()
    ]).then(([productCountData, sellProductData]) => {
      const mergedData = [];

      productCountData.forEach(productCountItem => {
        sellProductData.forEach(sellProductItem => {
          if (productCountItem.categoryName === sellProductItem.categoryName) {
            mergedData.push({
              "categoryName": productCountItem.categoryName,
              "productCount": productCountItem.productCount,
              "sellTotal": sellProductItem.sellTotal
            })
          }
        })
      })

      this.initChart(mergedData);
    });
  },
  methods: {
    getProductCountByCategory() {
      return new Promise((resolve, reject) => {
        api.getProductCountByCategory({}).then(response => {
          if (response.code === 0) {
            resolve(response.data);
          } else {
            reject(new Error('Failed to fetch product count by category'));
          }
        });
      });
    },
    getSellProductCategorySummary() {
      return new Promise((resolve, reject) => {
        api.getSellProductCategorySummary({}).then(response => {
          if (response.code === 0) {
            resolve(response.data);
          } else {
            reject(new Error('Failed to fetch sell product category summary'));
          }
        });
      });
    },
    initChart(chartData) {
      const names = chartData.map(item => item.categoryName);
      const productCounts = chartData.map(item => item.productCount);
      const sellTotals = chartData.map(item => item.sellTotal);

      this.line = document.getElementsByClassName('sku-count-category-ranking')
      this.chart = echarts.init(this.$el, 'macarons');
      this.chart.setOption({
        title: {
          text: '商品品类数量和销量统计',          
          left: 'center',
          top: 'top'
        },
        legend: {
          data: ['品类商品数量', '品类商品销量'],
          right: 'left',
          top: 'top'

        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        grid: {
          left: '1%',
          right: '1%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: chartData.map(x => x.categoryName)
        },
        yAxis: [
          {
            type: 'value',
            name: '品类商品数量'
          },
          {
            type: 'value',
            name: '品类商品销量',
            position: 'right'
          }
        ],
        series: [
          {
            name: '品类商品数量',
            type: 'line',
            yAxisIndex: 0,
            data: chartData.map(x => x.productCount)
          },
          {
            name: '品类商品销量',
            type: 'line',
            yAxisIndex: 1,
            data: chartData.map(x => x.sellTotal)
          }
        ]
      })
    },
    beforeDestroy() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    }
  }
}
</script>
