<template>
  <el-row :gutter="40" class="panel-group">
    <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
      <div class="sku-card-panel">
        <div class="card-panel-icon-wrapper icon-people">
          <svg-icon icon-class="color" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            sku总数
          </div>
          <count-to :start-val="0" :end-val="total" :duration="2600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
      <div class="sku-card-panel">
        <div class="card-panel-icon-wrapper icon-message">
          <svg-icon icon-class="dashboard" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            上架sku总数
          </div>
          <count-to :start-val="0" :end-val="listingTotal" :duration="3000" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
      <div class="sku-card-panel">
        <div class="card-panel-icon-wrapper icon-money">
          <svg-icon icon-class="guide" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            下架sku总数
          </div>
          <count-to :start-val="0" :end-val="delistingTotal" :duration="3200" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
      <div class="sku-card-panel">
        <div class="card-panel-icon-wrapper icon-category">
          <svg-icon icon-class="link" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            一级分类总数
          </div>
          <count-to :start-val="0" :end-val="categoryCount" :duration="3200" class="card-panel-num" />
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import * as api from "@/api/mall/statistics/commodity";
import CountTo from 'vue-count-to'

export default {
  components: {
    CountTo
  },
  data() {
    return {
      delistingTotal: 0,
      listingTotal: 0,
      categoryCount: 0,
      total: 0
    };
  },
  created() {
    this.getListSkuTotal();
  },
  methods: {
    getListSkuTotal() {
      // 执行查询
      api.getSkuTotal({}).then(response => {
        if (response.code === 0) {
          const { delistingTotal, listingTotal, total, categoryCount } = response.data;
          this.delistingTotal = delistingTotal;
          this.listingTotal = listingTotal;
          this.categoryCount = categoryCount;
          this.total = total;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.panel-group {
  margin-top: 18px;

  .card-panel-col {
    margin-bottom: 32px;
  }

  .sku-card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-category {
        background: #2dfa1a;
      }
    }

    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-category {
      color: #2dfa1a;
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px 26px 26px 0;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}
</style>