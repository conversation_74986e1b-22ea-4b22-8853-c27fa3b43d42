<template>
  <div class="app-container" v-show="open">
    <el-dialog :title="title" :visible.sync="open" @close="close" width="550px">
      <el-form ref="form" :model="form" :rules="rules" >
        <el-form-item label="分组名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入品牌名称"/>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                      :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="memo">
          <el-input v-model="form.memo" type="textarea" placeholder="请输入内容" :maxlength="100"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
        <el-button @click="close">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as api from "@/api/mall/product/tag.js"
export default {
  name: 'ProductSkuTagGroupForm',
  data() {
    return {
      title: '',
      open: false,
      submitLoading: false,
      form: {
        name: null,
        memo: null,
        status: 0
      },
      rules: {
        name: [
          { required: true, message: '分组名称不能为空', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    reset() {
      this.form = {
        id: null,
        name: null,
        memo: null,
        status: 0
      }
    },
    show(id) {
      this.reset()
      this.title = '新建分组'
      this.open = true
      if(id) {
        this.title = '编辑分组'
        api.getGroup(id).then(res => {
          Object.assign(this.form, res.data)
        })
      } 
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (!valid) {
          return;
        }
        this.submitLoading = true
        let params = this.form
        let func = api.createGroup
        if(this.form.id) {
          func = api.updateGroup
        }
        func(params).then(res => {
          if(res.code === 0) {
            this.$modal.msgSuccess("保存成功");
            this.$emit("on-update");
            this.close()
          }
        }).finally(() => {
          this.submitLoading = false
        })
      })
    },
    close() {
      this.open = false
    }
  }
}
</script>

<style lang="scss">

</style>