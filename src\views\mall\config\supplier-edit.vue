<template>
  <div class="app-container">
    <el-card>
    <el-tabs v-model="activeTab">
      <el-tab-pane label="基础信息" name="basis" v-hasPermi="['mall:supplier']">
        <supplierBasis ref="basis" v-if="supplierId" :id="supplierId"></supplierBasis>
      </el-tab-pane>
      <el-tab-pane label="收款账号" name="account" v-hasPermi="['mall:supplier-account']">
        <supplierAccount v-if="supplierId" :id="supplierId"></supplierAccount>
      </el-tab-pane>
      <el-tab-pane label="开发者配置" name="openApp" v-if="supplierInfo.id && supplierInfo.type !== 1" v-hasPermi="['mall:openapp']">
        <supplierOpenApp v-if="supplierId" :id="supplierId"></supplierOpenApp>
      </el-tab-pane>
      <el-tab-pane label="京东VOP配置" name="jdvop" v-if="supplierInfo.type === 1" v-hasPermi="['mall:vop-config']">
        <supplierJdvop v-if="supplierId" :id="supplierId"></supplierJdvop>
      </el-tab-pane>
    </el-tabs>
    </el-card>
  </div>
</template>

<script>
import * as api from "@/api/mall/config/supplier";
import supplierBasis from './components/supplier-basis.vue'
import supplierAccount from './components/supplier-account.vue'
import supplierOpenApp from './components/supplier-open-app.vue'
import supplierJdvop from './components/supplier-jdvop.vue'
export default {
  name: "MallSupplierEdit",
  components: { supplierBasis, supplierAccount, supplierOpenApp, supplierJdvop },
  data() {
    return {
      activeTab: 'basis',
      supplierId: null,
      supplierInfo: {}
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    init() {
      let id = this.$route.query.id
      if(!id) {
        return
      }
      console.log('id==========', id)
      this.supplierId = parseInt(id)
      this.loadSupplierInfo();
    },
    async loadSupplierInfo () {
      let res = await api.getSupplierInfo(this.supplierId)
      this.supplierInfo = res.data || {}
      this.$refs.basis.configInfo = this.supplierInfo
    }
  }
}
</script>

<style lang="scss" scoped></style>