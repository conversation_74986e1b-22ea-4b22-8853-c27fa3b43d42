<template>
    <div class="orderStatisticsByDept" :style="{ height: height, width: width }" />
</template>

<script>
import * as api from "@/api/mall/statistics/trade";
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '300px'
        }
    },
    data() {
        return {
            chart: null
        }
    },
    created() {
        this.getOrderSummaryByDept();
    },
    methods: {
        getOrderSummaryByDept() {
            api.getOrderSummaryByDept({limit:10}).then(response => {
                if (response.code === 0) {
                    this.initChart(response.data)
                }
            });
        },
        initChart(data) {
            const formattedData = data.map(item => ({
                name: item.deptName,
                value: item.orderAmount
            }));
            this.chart = echarts.init(this.$el, 'macarons')
            this.chart.setOption({
                title: {
                    text: '部门采购额排行',
                    top: 'top',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item'
                },
                series: [
                    {
                        name: '采购金额',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        startAngle: 160,
                        avoidLabelOverlap: false,
                        data: formattedData,
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            })
        }
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    }
}
</script>