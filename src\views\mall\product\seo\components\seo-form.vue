<template>
  <div class="app-container" v-show="open">
    <el-dialog title="修改商品SEO信息" :visible.sync="open" @close="close" width="500px">
      <el-form ref="form" :model="form" :rules="rules" >
        <el-form-item label="初始销量">
          <el-input-number v-model="form.initSalesCount" :min="-999999" :max="999999" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
        <el-button @click="close">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as skuApi from "@/api/mall/product/sku.js"
export default {
  name: 'ProductSkuSeoForm',
  data() {
    return {
      open: false,
      submitLoading: false,
      ids: [],
      form: {
        initSalesCount: 1
      },
      rules: {
        initSalesCount: [
          { required: true, message: '初始销量不能为空', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    reset() {
      this.form.initSalesCount = null
    },
    init(rows) {
      this.ids = rows.map(item => item.id) || []
      this.reset()
      this.open = true
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (!valid) {
          return;
        }
        this.submitLoading = true
        let params = this.ids.map(item => {
          return {
            id: item,
            initSalesCount: this.form.initSalesCount
          }
        })
        skuApi.updateSeoSku(params).then(res => {
          if(res.code === 0) {
            this.$modal.msgSuccess("保存成功");
            this.$emit("update-success");
            this.close()
          }
        }).finally(() => {
          this.submitLoading = false
        })
      })
    },
    close() {
      this.open = false
    }
  }
}
</script>

<style lang="scss">

</style>