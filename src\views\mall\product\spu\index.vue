<template>
  <div class="app-container">
    <el-tabs v-model="activeTabs" type="card" @tab-click="saveTabCache">
      <el-tab-pane label="平台商品SKU" name="ProductSku"></el-tab-pane>
      <el-tab-pane label="平台商品SPU" name="ProductSpu"></el-tab-pane>
      <el-tab-pane v-if="checkPermi(['vop:query'])" label="京东商品SKU" name="ProductSpuJd"></el-tab-pane>
      <component :is="listComponent"></component>
    </el-tabs>
  </div>
</template>

<script>
import ProductSpu from "./index-spu.vue";
import ProductSku from "./index-sku.vue";
import ProductSpuJd from "./index-jd.vue";
export default {
  name: "ProductIndex",
  data() {
    return {
      activeTabs: 'ProductSku'
    }
  },
  components: { ProductSpu, ProductSku, ProductSpuJd},
  computed: {
    listComponent() {
      return this.activeTabs
    }
  },
  created() {
    this.initTab()
  },
  methods: {
     getCacheTabKey1() {
      return `mall-product-spu-tab-${this.$store.state.user.id}`
    },
    initTab() {
      let value = sessionStorage.getItem(this.getCacheTabKey1())
      if(value) {
        this.activeTabs = value
      } else {
        this.activeTabs = 'ProductSku'
      }
    },
    saveTabCache() {
      sessionStorage.setItem(this.getCacheTabKey1(), this.activeTabs)
    }
  },
};
</script>
