<template>
  <div class="category-select">
    <el-dialog title="选择商品分类" :visible.sync="open" width="700px" :append-to-body="appendToBody">
      <CategorySelect ref="categorySelect" :extProps="extProps" :extParams="extParams" :panel="true" />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="close">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CategorySelect from "@/views/mall/product/spu/components/category-select";
export default {
  name: 'ProductCategoryDialog',
  components: { CategorySelect },
  props: {
    open: {
      type: Boolean,
      default: () => false
    },
    extProps: {
      type: Object,
      default: () => {}
    },
    extParams: {
      type: Object,
      default: () => {}
    },
    appendToBody: {
      type: <PERSON><PERSON>an,
      default: false
    }
  },
  methods: {
    close() {
      this.$emit('update:open', false)
    },
    submitForm() {
      if(this.extProps && this.extProps.multiple) {
        let checkNodes = this.$refs.categorySelect.getCheckedNodes()
        let ids = checkNodes.map(item => item.data.id)
        this.$emit("change", ids, checkNodes);
      } else {
        let pathInfo = this.$refs.categorySelect.getPathInfo()
        this.$emit("change", pathInfo.ids, pathInfo);
      }
      
      this.close()
    },
    cancel() {
      this.close()
    }
  }
}
</script>

<style lang="scss">
.category-select {
  .el-cascader-menu__wrap {
    height: 100%;
  }
}
</style>