规则名称<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="规则名称" prop="ruleName">
        <el-input v-model="queryParams.ruleName" placeholder="请输入规则名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="规则类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择规则类型" clearable size="small">
          <el-option label="价格区间" value="1" />
          <el-option label="关键字过滤" value="2" />
          <el-option label="分类校验" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="启用状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择启用状态" clearable size="small">
          <el-option label="启用" value="0" />
          <el-option label="禁用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['mall:product-rule']">新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="编号" align="center" prop="id" />
      <el-table-column label="规则名称" align="center" prop="ruleName" />
      <el-table-column label="规则类型" align="center" prop="type">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.MALL_PRODUCT_RULE_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="规则内容" align="center" prop="ruleContent" />
      <el-table-column label="排序" align="center" prop="sort" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="启用状态" align="center" prop="status" >
        <!-- <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template> -->
        <template v-slot="scope">
          <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1" @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['mall:product-rule']">修改</el-button>
          <!-- <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['mall:product-rule']">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="700px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="规则名称" prop="ruleName">
          <el-input v-model="form.ruleName" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="规则类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择规则类型">
            <el-option label="价格区间" value="1" />
            <el-option label="关键字过滤" value="2" />
            <el-option label="分类校验" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="[1].includes(parseInt(form.type))" label="最低价格" prop="minPrice">
          <el-input-number v-model="form.minPrice" :min="0" :max="10000"></el-input-number>
        </el-form-item>
        <el-form-item v-if="[1].includes(parseInt(form.type))" label="最高价格" prop="maxPrice">
          <el-input-number v-model="form.maxPrice" :min="1" :max="1000000"></el-input-number>
        </el-form-item>
        <el-form-item v-if="[2].includes(parseInt(form.type))" label="关键字" prop="ruleContent">
          <el-input v-model="form.ruleContent" type="textarea" placeholder="请输入过滤关键字，用逗号分隔"></el-input>
        </el-form-item>
        <el-form-item label="启用状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">启用</el-radio>
            <el-radio label="1">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" placeholder="请输入排序" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createProductRule, updateProductRule, deleteProductRule, getProductRule, getProductRulePage, exportProductRuleExcel } from "@/api/mall/config/productRule";
import Editor from '@/components/Editor';

export default {
  name: "ProductRule",
  components: {
    Editor
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商品过滤规则列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        ruleName: null,
        type: null,
        status: null,
        ruleContent: null,
        sort: null,
        createTime: [],
      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: {
        ruleName: [{ required: true, message: "规则名称不能为空", trigger: "blur" }],
        type: [{ required: true, message: "分类不能为空", trigger: "change" }],
        status: [{ required: true, message: "启用状态, 0-启用，1-禁用不能为空", trigger: "blur" }],
        ruleContent: [{ required: true, message: "规则内容不能为空", trigger: "blur" }],
        minPrice: [{ required: true, message: "最低价格不能为空", trigger: "blur" }],
        maxPrice: [{ required: true, message: "最高价格不能为空", trigger: "blur" }],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getProductRulePage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        ruleName: undefined,
        type: "1",
        status: "0",
        ruleContent: undefined,
        minPrice: "0",
        maxPrice: undefined,
        sort: 4
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加商品过滤规则";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getProductRule(id).then(response => {
        this.form = response.data;
        this.form.status = response.data.status.toString();
        this.form.type = response.data.type.toString();
        if(response.data.type == "1"){
          let prices = response.data.ruleContent.split(" - ");
          this.form.minPrice = Number(prices[0]);
          this.form.maxPrice = Number(prices[1]);
          this.form.ruleContent = undefined
        }
        else if(response.data.type == "3"){
          this.form.ruleContent = undefined;
          this.form.minPrice = undefined;
          this.form.maxPrice = undefined;
        }
        this.open = true;
        this.title = "修改商品过滤规则";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.id != null) {
          updateProductRule(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        if(this.form.type == "1"){
          this.form.ruleContent = undefined
        }
        else if(this.form.type == "2"){
          this.form.minPrice = undefined;
          this.form.maxPrice = undefined;
        }
        else if(this.form.type == "3"){
          this.form.ruleContent = undefined;
          this.form.minPrice = undefined;
          this.form.maxPrice = undefined;
        }
        // 添加的提交
        createProductRule(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 更新启用状态 */
    handleStatusChange(row) {
      this.reset();
      const id = row.id;
      getProductRule(id).then(response => {
        this.form = response.data;
        this.form.status = response.data.status.toString();
        if (response.data.type == 1) {
          let prices = response.data.ruleContent.split(" - ");
          this.form.minPrice = Number(prices[0]);
          this.form.maxPrice = Number(prices[1]);
          this.form.ruleContent = undefined
        }
        else if (response.data.type == 3) {
          this.form.ruleContent = undefined;
          this.form.minPrice = undefined;
          this.form.maxPrice = undefined;
        }
        if(this.form.status == 0){
          this.form.status = 1;
        }
        else {
          this.form.status = 0;
        }
        updateProductRule(this.form).then(response => {
          this.$modal.msgSuccess(this.form.status == 0 ? "启用成功" : "禁用成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除商品过滤规则编号为"' + id + '"的数据项?').then(function() {
          return deleteProductRule(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有商品过滤规则数据项?').then(() => {
          this.exportLoading = true;
          return exportProductRuleExcel(params);
        }).then(response => {
          this.$download.excel(response, '商品过滤规则.xls');
          this.exportLoading = false;
        }).catch(() => {});
    }
  }
};
</script>
