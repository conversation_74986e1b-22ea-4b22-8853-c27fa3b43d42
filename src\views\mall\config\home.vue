<template>
  <div class="app-container" v-loading.lock="loading">
    <el-card>
    <el-tabs v-model="activeType">
      <el-tab-pane label="PC端首页" name="10"></el-tab-pane>
      <el-tab-pane label="PC端主题" name="11"></el-tab-pane>
      <el-tab-pane label="H5端首页" name="20"></el-tab-pane>
      <el-tab-pane label="H5端主题" name="21"></el-tab-pane>
    </el-tabs>

    <el-empty v-if="!configInfo.id && formMode === 'init'">
      <el-button type="text" icon="el-icon-add" @click="beginConfig">开始配置</el-button>
    </el-empty>

    <template v-if="configInfo.id || formMode === 'edit'">
      <el-row v-if="[10].includes(parseInt(activeType))" >
        <el-col :span="24" :offset="0"  v-if="[10].includes(parseInt(activeType))">
          <el-card shadow="hover" style="width: 100%;">
            <div style="width: 100%; display: inline-block; ">
              <span>精选好物 </span>
              <el-switch v-model="configContent.bestProductSwitch" :active-value="1" :inactive-value="0" />
              <div v-if="configContent.bestProductSwitch" style="display: inline-block;">
                <template v-if="configContent.bestProductCategorys">
                  <el-tag v-for="tag in configContent.bestProductCategorys" :key="tag.id" closable style="margin-left: 10px;" @close="handleClose(tag, 1)"
                  >{{tag.name}}</el-tag>
                </template>
                <el-button icon="el-icon-plus" @click="addProductCategory(1, configContent.bestProductCategorys)" circle size="small" style="margin-left: 10px;"></el-button>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="24" :offset="0"  v-if="[10].includes(parseInt(activeType))">
          <el-card shadow="hover" style="width: 100%;margin-top: 20px;">
            <div style="width: 100%; display: inline-block; ">
              <span>优选电商 </span> 
              <el-switch v-model="configContent.bestSupplierSwitch" :active-value="1" :inactive-value="0" />
              <div v-if="configContent.bestSupplierSwitch" style="display: inline-block;">
                <template v-if="configContent.bestSupplierCategorys">
                  <el-tag v-for="tag in configContent.bestSupplierCategorys" :key="tag.id" closable style="margin-left: 10px;" @close="handleClose(tag, 2)"
                  >{{tag.name}}</el-tag>
                </template>
                <el-button icon="el-icon-plus" @click="addProductCategory(2, configContent.bestSupplierCategorys)" circle size="small" style="margin-left: 10px;"></el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row v-if="[20].includes(parseInt(activeType))">
        <el-col :span="24" :offset="0">
          <el-card shadow="hover" style="width: 100%;margin-top: 20px;">
            <div class="config-container">
              <div class="config-item">
                <span>分类区块显示开关 </span>
                <el-switch v-model="configContent.h5CategorySwitch" :active-value="1" :inactive-value="0" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row v-if="[20].includes(parseInt(activeType))">
        <el-col :span="24" :offset="0">
          <el-card shadow="hover" style="width: 100%;margin-top: 20px;">
            <div class="config-container">
              <div class="config-item">
                <span>供应商区块显示开关 </span>
                <el-switch v-model="configContent.h5SupplierSwitch" :active-value="1" :inactive-value="0" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row v-if="[10,20].includes(parseInt(activeType))">
        <el-col :span="24" :offset="0">
          <el-card shadow="hover" style="width: 100%;margin-top: 20px;">
            <span>自定义商品区域 </span>
            <el-switch v-model="configContent.seoCardSwitch" :active-value="1" :inactive-value="0" />
            <div v-if="configContent.seoCardSwitch" style="display: inline-block;">
              <template v-if="configContent.seoCardList">
                <el-tag v-for="tag in configContent.seoCardList" :key="tag.id" closable style="margin-left: 10px;" @close="handleSeoCardClose(tag)"
                >{{tag.name}}</el-tag>
              </template>
              <el-button icon="el-icon-plus" @click="addSeoCard(configContent.seoCardList)" circle size="small" style="margin-left: 10px;"></el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row v-if="[10].includes(parseInt(activeType))">
        <el-col :span="24" :offset="0">
          <el-card shadow="hover" style="width: 100%;margin-top: 20px;">
            <div class="config-container">
              <div class="config-item">
                <span>流程图 </span> 
                <el-switch v-model="configContent.processImgSwitch" :active-value="1" :inactive-value="0" />
              </div>
              <div class="config-item">
                <span>热销商品 </span> 
                <el-switch v-model="configContent.hotProductSwitch" :active-value="1" :inactive-value="0" />
              </div>
              <div class="config-item">
                <span>最新上架 </span> 
                <el-switch v-model="configContent.freshProductSwitch" :active-value="1" :inactive-value="0" />
              </div>
              <div class="config-item">
                <span>校园风采 </span>
                <el-switch v-model="configContent.schoolPhotoSwitch" :active-value="1" :inactive-value="0" />
              </div>
              <div class="config-item">
                <span>公告资讯 </span>
                <el-switch v-model="configContent.noticeAndCmsSwitch" :active-value="1" :inactive-value="0" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row v-if="[10].includes(parseInt(activeType))">
        <el-col :span="24" :offset="0">
          <el-card shadow="hover" style="width: 100%;margin-top: 20px;">
            <div class="config-container">
              <div class="config-item">
                <span>移动端入口 </span>
                <el-switch v-model="configContent.h5Switch" :active-value="1" :inactive-value="0" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row v-if="[10].includes(parseInt(activeType))">
        <el-col :span="24" :offset="0">
          <el-card shadow="hover" style="width: 100%;margin-top: 20px;">
            <div style="width: 100%; display: inline-block; ">
              <span>底部及链接 </span>
              <el-switch v-model="configContent.footerSwitch" :active-value="1" :inactive-value="0" />
              <div v-if="configContent.footerSwitch" style="margin: 10px 20px;width:300px;display:inline-block;">
                <el-input ::maxlength="50" v-model="configContent.footerCode" placeholder="请输入样式编码"/>
              </div>
            </div>      
          </el-card>
        </el-col>
        <el-col :span="24" :offset="0" v-if="[10].includes(parseInt(activeType))">
          <el-card hover-shadow style="width: 85%;margin-top: 20px;">
            <div slot="header" class="clearfix">
              <span>定制导航菜单</span>
              <el-button style="float: right; padding: 3px 0"  @click="addExtraMenu" type="text">添加菜单</el-button>
            </div>
            <div>
              <el-table
                :data="configContent.extraMenuList"
                style="width: 100%">
                <el-table-column prop="name" label="名称" width="180"> </el-table-column>
                <el-table-column prop="url" label="链接" width="300"> </el-table-column>
                <el-table-column prop="isOutUrl" label="外部链接" width="150"> 
                  <template v-slot="scope">
                    {{ scope.row.isOutUrl ? '是' : '否' }}
                  </template>
                </el-table-column>
                <el-table-column prop="position" label="位置" width="150"> 
                  <template v-slot="scope">
                    {{ scope.row.position | positionInfo }}
                  </template>
                </el-table-column>
                <el-table-column prop="sort" label="排序" width="100"> </el-table-column>
                <el-table-column prop="name" label="操作" width="180"> 
                  <template v-slot="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="editExtraMenu(scope.row)">修改</el-button>
                    <el-button size="mini" type="text" icon="el-icon-delete" @click="deleteExtraMenu(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <template v-if="[11,21].includes(parseInt(activeType))">
        <el-row :gutter="16">
          <el-col :span="8" :offset="0">
            <div>
              <span class="config-title">LOGO（尺寸:506*80）</span>
            </div>
            <div style="margin: 10px 0;">
              <el-input v-model="configContent.logoUrl" :maxlength="240" placeholder="请输入LOGO地址" />
            </div>
            <imageUpload v-model="configContent.logoUrl" :limit="1" :fileSize="1"/>
          </el-col>
          <el-col :span="8" :offset="0" v-if="[21].includes(parseInt(activeType))">
            <div>
              <span class="config-title">logo图标（如：校徽）</span>
            </div>
            <div style="margin: 10px 0;">
              <el-input v-model="configContent.logoIconUrl" :maxlength="240" placeholder="请输入ogo图标地址" />
            </div>
            <imageUpload v-model="configContent.logoIconUrl" :limit="1" :fileSize="1"/>
          </el-col>
          <el-col :span="8" :offset="0" v-if="[11,21].includes(parseInt(activeType))">
            <div>
              <span class="config-title">收藏图标</span>
            </div>
            <div style="margin: 10px 0;">
              <el-input v-model="configContent.faviconUrl" :maxlength="240" placeholder="请输入收藏图标地址" />
            </div>
            <imageUpload v-model="configContent.faviconUrl" :limit="1" :fileSize="1"/>
          </el-col>
          <el-col :span="8" :offset="0" v-if="[11].includes(parseInt(activeType))">
            <div>
              <span class="config-title">登录页背景图（尺寸:1122*903）</span>
            </div>
            <div style="margin: 10px 0;">
              <el-input v-model="configContent.loginBgUrl" :maxlength="240" placeholder="请输入登录页背景图地址" />
            </div>
            <imageUpload v-model="configContent.loginBgUrl" :limit="1" :fileSize="1"/>
          </el-col>
        </el-row>
        <el-row>
          
        </el-row>
        <el-row  :gutter="16" style="margin-top:20px;">
          <el-col :span="8" :offset="0" v-if="[11].includes(parseInt(activeType))">
            <div>
              <span class="config-title">主题类型</span>
            </div>
            <div style="margin: 10px 0;">
              <el-radio-group v-model="configContent.themeCode">
                <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.MALL_CONFIG_THEME_CODE)"
                          :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
                </el-radio>
              </el-radio-group>
            </div>
          </el-col>
          <el-col :span="8" :offset="0" v-if="[11].includes(parseInt(activeType))">
            <div>
              <span class="config-title">自定义流程图（尺寸:1200*340）</span>
            </div>
            <div style="margin: 10px 0;">
              <el-input v-model="configContent.processImg" :maxlength="240" placeholder="请输入首页自定义流程图地址" />
            </div>
            <imageUpload v-model="configContent.processImg" :limit="1" :fileSize="1"/>
          </el-col>
          <el-col :span="8" :offset="0" v-if="[11].includes(parseInt(activeType))">
            <div>
              <span class="config-title">外部样式地址</span>
            </div>
            <div style="margin: 10px 0;">
              <el-input v-model="configContent.diyStyleUrl" type="url" :maxlength="240" placeholder="请输入外部样式地址" />
            </div>
            <fileUpload v-model="configContent.diyStyleUrl" :limit="1" :fileSize="1" :fileType="['css']" />
          </el-col>
        </el-row>

        <el-row v-if="[11,21].includes(parseInt(activeType))" :gutter="16" style="margin-top:20px;">
          <el-col :span="24" :offset="0">
            <div>
              <span class="config-title">自定义样式</span>
            </div>
            <div style="margin: 10px 0;">
              <el-input type="textarea" :rows="4" v-model="configContent.diyCss" :maxlength="1000" placeholder="请输入自定义样式" />
            </div>
          </el-col>
        </el-row>
      </template>

      <el-row>
        <el-col :span="24" :offset="0" style="margin-top: 20px;">
          <el-button type="primary" @click="submitForm" :loading="submitLoading">保存</el-button>
        </el-col>
      </el-row>
    </template>

    <ProductCategoryTree ref="productCategoryTree" :type="2" :checkStrictly="true" @on-select="handleSelectProductCategory" />
    <SeoCardDialog ref="seoCardDialog" @on-update="saveSeoCards"></SeoCardDialog>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="menuEditTitle" :visible.sync="menuEditOpen" width="600px" append-to-body>
      <el-form ref="menuEditForm" :model="menuEditForm" :rules="menuEditRules" label-width="100px">
        <el-form-item label="菜单名称" prop="name">
          <el-input v-model="menuEditForm.name" placeholder="请输入菜单名称"/>
        </el-form-item>
        <el-form-item label="菜单链接" prop="url">
          <el-input v-model="menuEditForm.url" />
        </el-form-item>
        <el-form-item label="外部链接" prop="isOutUrl">
          <el-radio-group v-model="menuEditForm.isOutUrl">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="位置" prop="position">
          <el-radio-group v-model="menuEditForm.position">
            <el-radio :label="1">主菜单导航栏</el-radio>
            <el-radio :label="2">导航栏欢迎区</el-radio>
            <!-- <el-radio :label="3">顶部栏欢迎区</el-radio> -->
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序排序" prop="sort">
          <el-input-number v-model="menuEditForm.sort" :min="1" :max="100" label="菜单排序"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitMenuEditForm">确 定</el-button>
        <el-button @click="cancelMenuEdit">取 消</el-button>
      </div>
    </el-dialog>
    </el-card>
  </div>
</template>

<script>
import * as api from "@/api/mall/config/homeConfig";
import ProductCategoryTree from "@/components/ProductCategoryTree";
import ImageUpload from '@/components/ImageUpload';
import FileUpload from '@/components/FileUpload';
import SeoCardDialog from '@/components/SeoCardDialog';
export default {
  name: "MallHomeConfig",
  components: {
    ProductCategoryTree, ImageUpload, FileUpload, SeoCardDialog
  },
  data() {
    return {
      loading: false,
      activeType: '10',
      submitLoading: false,
      formMode: 'init',
      configInfo: {
        id: undefined
      },
      fieldMap: {
        10: ["bestProductSwitch","bestProductCategorys","bestSupplierSwitch","bestSupplierCategorys","hotProductSwitch",
                "freshProductSwitch","schoolPhotoSwitch","h5Switch","noticeAndCmsSwitch","extraMenuList","footerSwitch",
                "footerCode","seoCardSwitch","seoCardList", "processImgSwitch"],
        11: ["logoUrl","faviconUrl","loginBgUrl","themeCode", "processImg", "diyCss", "diyStyleUrl"],
        20: ["h5CategorySwitch","h5SupplierSwitch","seoCardSwitch","seoCardList"],
        21: ["logoUrl", "logoIconUrl", "faviconUrl", "diyCss"]
      },
      configContent: {},
      // 定制导航菜单
      menuEditTitle: '',
      menuEditOpen: false,
      menuEditForm: {},
      menuEditRules: {
        name: [
          { required: true, message: '请输入菜单名称', trigger: 'blur'}
        ],
        url: [
          { required: true, message: '请输入菜单链接', trigger: 'blur'},
          { type: 'url', message: '菜单链接格式不正确', trigger: 'blur'}
        ],
        isOutUrl: [
          { required: true, message: '请选择是否外部链接', trigger: 'blur'}
        ],
        position: [
          { required: true, message: '请选择菜单位置', trigger: 'blur'}
        ],
        sort: [
          { required: true, message: '请输入菜单排序', trigger: 'blur'}
        ]
      }
    }
  },
  watch: {
    activeType() {
      this.loadConfig()
    }
  },
  filters: {
    positionInfo(val) {
      let dics = {
        1: '主菜单导航栏',
        2: '导航栏欢迎区',
        3: '顶部栏欢迎区'
      }
      return dics[val] || val
    }
  },
  created() {
    this.loadConfig()
  },
  methods: {
    beginConfig() {
      this.formMode = 'edit'
    },
    initConfig() {
      this.configContent = {
        version: 10,
        bestProductSwitch: 1,
        bestProductCategorys: [],
        bestSupplierSwitch: 1,
        bestSupplierCategorys: [],
        processImgSwitch: 1,
        hotProductSwitch: 1,
        freshProductSwitch: 1,
        schoolPhotoSwitch: 1,
        h5Switch: 0,
        noticeAndCmsSwitch: 1,
        extraMenuList: [],
        footerSwitch: 1,
        footerCode: null,
        logoUrl: null,
        faviconUrl: null,
        loginBgUrl: null,
        themeCode: null,
        processImg: null,
        diyStyleUrl: null,
        diyCss: null,
        h5CategorySwitch: false,
        h5SupplierSwitch: false,
        seoCardSwitch: false,
        seoCardList: undefined
      }
    },
    async loadConfig() {
      this.loading = true
      this.initConfig()
      let res = await api.getHomeConfig({type: parseInt(this.activeType)});
      this.configInfo = res.data || {}
      let content = this.configInfo.content
      if(content) {
        Object.assign(this.configContent, JSON.parse(content))
      } else {
        this.configContent = {}
      }
      this.loading = false
    },
    filterFields() {
      let dicFields = this.fieldMap[this.activeType]
      let builtinFieds = ['version']
      let objV2 = {}
      Object.keys(this.configContent).forEach(key => {
        if(dicFields.includes(key) || builtinFieds.includes(key)) {
          objV2[key] = this.configContent[key]
        }
      })
      return objV2
    },
    async submitForm() {
      this.submitLoading = true
      let configMap = this.filterFields()
      let data = {
        id: this.configInfo.id,
        type: parseInt(this.activeType),
        content: JSON.stringify(configMap)
      }
      await api.saveHomeConfig(data)
      this.$modal.msgSuccess("保存成功");
      this.submitLoading = false
      this.loadConfig()
    },
    addProductCategory(type, initArr) {
      this.$refs.productCategoryTree.show(type, initArr)
    },
    async handleSelectProductCategory(nodes, type) {
      console.log('nodes===', nodes)
      let items = []
      if(nodes && nodes.length) {
        items = nodes.map(node => {
          return {
              id: node.categoryId,
              name: node.categoryName,
              level: node.categoryLevel + 1
            }
        })
      }
      if(type === 2) {
        this.configContent.bestSupplierCategorys = items
      } else {
        this.configContent.bestProductCategorys = items
      }

    },
    handleClose(tag, type) {
      let tags = this.configContent.bestProductCategorys
      if(type === 2) {
        tags = this.configContent.bestSupplierCategorys
      }
      tags.splice(tags.indexOf(tag), 1);
    },
    saveSeoCards(items) {
      if(items && items.length) {
        let data = items
        this.configContent.seoCardList = data
      } else {
        this.configContent.seoCardList = []
      }
      this.$forceUpdate()
    },
    addSeoCard() {
      let list = this.configContent.seoCardList || []
      let ids = list.map(item => item.id)
      this.$refs.seoCardDialog.show(ids)
    },
    handleSeoCardClose(item) {
      let list = this.configContent.seoCardList || []
      list.splice(list.findIndex(t => t.id === item.id), 1);
      this.$forceUpdate()
    },
    resetMenuEditForm() {
      this.menuEditForm = {
        name: undefined,
        url: undefined,
        isOutUrl: 1,
        position: 1,
        sort: 3
      };
      this.resetForm("menuEditForm");
    },
    addExtraMenu() {
      this.menuEditTitle = '添加导航菜单'
      this.menuEditOpen = true
      this.resetMenuEditForm()
    },
    editExtraMenu(item) {
      this.menuEditTitle = '修改导航菜单'
      this.menuEditOpen = true
      this.resetMenuEditForm()
      Object.assign(this.menuEditForm, item)
    },
    deleteExtraMenu(row) {
      let index = this.configContent.extraMenuList.findIndex(item => item.name === row.name)
      this.configContent.extraMenuList.splice(index, 1)
    },
    cancelMenuEdit() {
      this.menuEditOpen = false
    },
    submitMenuEditForm() {
      this.$refs["menuEditForm"].validate(valid => {
        if (!valid) {
          return;
        }
        let obj = {
          name: this.menuEditForm.name,
          url: this.menuEditForm.url,
          isOutUrl: this.menuEditForm.isOutUrl,
          position: this.menuEditForm.position,
          sort: this.menuEditForm.sort
        }
        if(!this.configContent.extraMenuList) {
          this.configContent.extraMenuList = []
        }
        let index = this.configContent.extraMenuList.findIndex(item => item.name === obj.name)
        if(index >= 0) {
          Object.assign(this.configContent.extraMenuList[index], obj)
        } else {
          this.configContent.extraMenuList.push(obj)
        }
        this.menuEditOpen = false
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.config-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  .config-item {
    width: 200px;
    margin: 10px;
  }
}
.config-title {
  font-size: 1.1em;
  font-weight: 500;
}

</style>