import request from '@/utils/request'


// 查询申请列表
export function getUserApplyFormPage(params) {
  return request({
    url: '/mall/supplier-form/user/page',
    params: params,
    method: 'get'
  })
}

// 查询申请列表
export function getUserApplyFormDetail(params) {
  return request({
    url: '/mall/supplier-form/user/get',
    params: params,
    method: 'get'
  })
}

// 保存入驻申请
export function saveApplyForm(params) {
  return request({
    url: '/mall/supplier-form/user/save',
    data: params,
    method: 'post'
  })
}

// 提交入驻申请
export function submitApplyForm(params) {
  return request({
    url: '/mall/supplier-form/user/submit',
    data: params,
    method: 'post'
  })
}

// 查询申请列表
export function getAdminApplyFormPage(params) {
  return request({
    url: '/mall/supplier-form/admin/page',
    params: params,
    method: 'get'
  })
}

// 查询申请详情
export function getAdminApplyFormDetail(params) {
  return request({
    url: '/mall/supplier-form/admin/get',
    params: params,
    method: 'get'
  })
}

// 审批入驻申请
export function approveApplyForm(params) {
  return request({
    url: '/mall/supplier-form/admin/approve',
    data: params,
    method: 'post'
  })
}

// 校验统一信用代码唯一性
export function validateEnterpriseUnifiedId(params) {
  return request({
    url: '/mall/supplier-form/user/validate-enterprise-id',
    data: params,
    method: 'post'
  })
}

