<template>
  <div class="app-container"> 
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="企业全称" prop="fullName">
        <el-input v-model="queryParams.fullName" placeholder="请输入企业全称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

     <!-- Tab 选项：真正的内容在 Table -->
    <el-tabs v-model="queryParams.tabStatus" type="card" @tab-click="tabClick" style="margin-top: -40px;">
      <el-tab-pane label="全部" name="all" />
      <el-tab-pane label="待提交" name="0" />
      <el-tab-pane label="待审批" name="1" />
      <el-tab-pane label="审核驳回" name="3" />
      <el-tab-pane label="审核通过" name="2" />
    </el-tabs>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="企业全称" align="center" prop="enterpriseFullName" />
      <el-table-column label="企业简称" align="center" prop="enterpriseShortName" />
      <el-table-column label="信用代码" align="center" prop="enterpriseUnifiedId" />
      <el-table-column label="申请状态" align="center" prop="approveStatus">
        <template v-slot="{ row }">
          <el-tag :type="row.approveStatus | supplierFormStatusStyle">{{ row.approveStatus | supplierFormStatusInfo }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="联系人" align="center" prop="contact" />
      <el-table-column label="联系电话" align="center" prop="phone" />
      <el-table-column label="提交时间" align="center" prop="submitTime">
        <template v-slot="scope">
          <span>{{ scope.row.submitTime ? parseTime(scope.row.submitTime) : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <template>
            <el-button size="medium" type="text" @click="showDetail(scope.row)">查看</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <el-drawer
      title="查看详情"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="1200px">
      <ApplyFormDetail ref="applyFormDetail" :id="curApplyFormId" role="admin" @on-update="applyFormUpdateHandle" />
    </el-drawer>

  </div>
</template>

<script>
import * as api from '@/api/mall/supplier/supplier-form'
import ApplyFormDetail from '@/views/mall/supplier/components/apply-detail'
export default {
  name: 'SupplierFormApprove',
  components: { ApplyFormDetail },
  data() {
    return {
      curApplyFormId: null,
      drawerVisible: false,
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 消息列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        approveStatus: 1,
        tabStatus: '1',
        id: null
      },
    }
  },
  mounted() {
    this.handleQuery()
  },
  methods: {
    /** tab 切换 */
    tabClick(tab) {
      this.queryParams.approveStatus = tab.name === 'all' ? undefined : tab.name;
      this.getList();
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      let params = {...this.queryParams}
      if(!params.id || isNaN(params.id)) {
        delete params.id
      }
      // 执行查询
      api.getAdminApplyFormPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    applyFormUpdateHandle() {
      this.drawerVisible = false
      this.getList()
    },
    showDetail(row) {
      this.curApplyFormId = row.id
      this.drawerVisible = true
    }
  }

}
</script>

<style>

</style>