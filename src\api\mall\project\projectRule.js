import request from '@/utils/request'

// 创建项目规则
export function createProjectRule(data) {
  return request({
    url: '/mall/project/project-rule/create',
    method: 'post',
    data: data
  })
}

// 更新项目规则
export function updateProjectRule(data) {
  return request({
    url: '/mall/project/project-rule/update',
    method: 'put',
    data: data
  })
}

// 检测项目规则
export function testProjectRule(data) {
  return request({
    url: '/mall/project/project-rule/test',
    method: 'post',
    data: data
  })
}

// 更新项目规则状态
export function updateProjectRuleStatus(data) {
  return request({
    url: '/mall/project/project-rule/update-status',
    method: 'put',
    data: data
  })
}

// 删除项目规则
export function deleteProjectRule(id) {
  return request({
    url: '/mall/project/project-rule/delete?id=' + id,
    method: 'delete'
  })
}

// 获得项目规则
export function getProjectRule(id) {
  return request({
    url: '/mall/project/project-rule/get?id=' + id,
    method: 'get'
  })
}

// 获得项目规则分页
export function getProjectRulePage(query) {
  return request({
    url: '/mall/project/project-rule/page',
    method: 'get',
    params: query
  })
}