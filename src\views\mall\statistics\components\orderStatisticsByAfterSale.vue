<template>
    <div class="tradeStatisticsByAfterSale" :style="{ height: height, width: width }" />
</template>

<script>
import * as api from "@/api/mall/statistics/trade";
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '300px'
        }
    },
    data() {
        return {
            chart: null
        }
    },
    created() {
        this.getAfterSaleOrderSummary();
    },
    methods: {
        getAfterSaleOrderSummary() {
            api.getAfterSaleOrderSummary({}).then(response => {
                if (response.code === 0) {
                    this.initChart(response.data)
                }
            });
        },
        initChart(data) {
            const names = data.map(item => item.supplierName)
            const values = data.map(item => item.orderCount)

            this.line = document.getElementsByClassName('sku-count-category-ranking')
            this.chart = echarts.init(this.$el, 'macarons');
            this.chart.setOption({
                title: {
                    text: '商品品类数量和销量统计',
                    left: 'center',
                    top: 'top'
                },
                legend: {
                    data: ['品类商品数量', '品类商品销量'],
                    right: 'left',
                    top: 'top'

                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { type: 'shadow' }
                },
                grid: {
                    left: '1%',
                    right: '5%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: names
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '售后订单数',
                        position: 'left'
                    }
                ],
                series: [
                    {
                        type: 'line',
                        yAxisIndex: 0,
                        data: values
                    }
                ]
            })
        }
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    }
}
</script>