import request from '@/utils/request'

// 创建商城广告位
export function createAdvPosition(data) {
  return request({
    url: '/mall/adv-position/create',
    method: 'post',
    data: data
  })
}

// 更新商城广告位
export function updateAdvPosition(data) {
  return request({
    url: '/mall/adv-position/update',
    method: 'put',
    data: data
  })
}

// 更新商城广告位状态
export function updateAdvPositionStatus(data) {
  return request({
    url: '/mall/adv-position/update-status',
    method: 'put',
    data: data
  })
}

// 删除商城广告位
export function deleteAdvPosition(id) {
  return request({
    url: '/mall/adv-position/delete?id=' + id,
    method: 'delete'
  })
}

// 获得商城广告位
export function getAdvPosition(id) {
  return request({
    url: '/mall/adv-position/get?id=' + id,
    method: 'get'
  })
}

// 获得商城广告位分页
export function getAdvPositionPage(query) {
  return request({
    url: '/mall/adv-position/page',
    method: 'get',
    params: query
  })
}
