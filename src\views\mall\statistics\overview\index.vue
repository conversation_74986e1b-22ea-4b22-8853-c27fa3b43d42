<template>
  <div class="app-container">
    <div v-if="checkPermi(['statistics:overview'])">
      <overviewCard></overviewCard>
      <el-radio-group class="radio-group" v-model="activeName" @change="changeRadio">
        <el-radio-button label="1">近7天</el-radio-button>
        <el-radio-button label="2">近三十天</el-radio-button>
        <el-radio-button label="3">近一年</el-radio-button>
      </el-radio-group>
      <div class="chart-line" id="chart-line"></div>
      <div class="chart-pie-container">
        <div class="chart-pie" id="chart-pie-1"></div>
        <div class="chart-pie" id="chart-pie-2"></div>
        <div class="chart-pie" id="chart-pie-3"></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as api from "@/api/mall/statistics/overview"
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'
import overviewCard from '../components/overviewTop.vue'

export default {
  name: "Overview",
  mixins: [resize],
  components: {
    overviewCard
  },
  data() {
    return {
      chart: null,
      activeName: '1',
      staticsStartTime: '',
      staticsEndTime: ''
    };
  },
  created() {
    this.getDate()
  },
  methods: {
    changeRadio() {
      this.getDate()
    },
    getDate() {
      let result = {}
      const today = new Date();
      const endDate = today;
      if (this.activeName == 1) {
        const startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 6);
        result = { startDate: startDate.toISOString().slice(0, 10), endDate: endDate.toISOString().slice(0, 10) };
      } else if (this.activeName == 2) {
        const startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 29);
        result = { startDate: startDate.toISOString().slice(0, 10), endDate: endDate.toISOString().slice(0, 10) };
      } else if (this.activeName == 3) {
        const startDate = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate() + 1);
        result = { startDate: startDate.toISOString().slice(0, 10), endDate: endDate.toISOString().slice(0, 10) };
      }
      this.staticsDayStart = result.startDate
      this.staticsDayEnd = result.endDate
      this.staticsStartMonth = result.startDate.slice(0, 7)
      this.staticsEndMonth = result.endDate.slice(0, 7)
      this.getLineChart()
      this.getPieCharts()
    },
    getLineChart() {
      if (this.activeName == '1' || this.activeName == '2') {
        api.dailySaleSummaryV2({
          startTime: this.staticsDayStart,
          endTime: this.staticsDayEnd,
        }).then(response => {
          if (response.code === 0 && response.data && response.data.length > 0) {
            this.initChart(response.data)
          } else {
            if (this.chart) {
              this.chart.dispose()
              this.chart = null
              this.$message.info('当前范围内没有数据')
            }
          }
        })
      }
      else if (this.activeName == '3') {
        api.monthlySalesSummaryV2({
          startMonth: this.staticsStartMonth,
          endMonth: this.staticsEndMonth,
        }).then(response => {
          if (response.code === 0 && response.data && response.data.length > 0) {
            this.initChart(response.data)
          } else {
            if (this.chart) {
              this.chart.dispose()
              this.chart = null
              this.$message.info('当前范围内没有数据')
            }
          }
        })
      }
    },
    getPieCharts() {
      this.pieCharts = [],
        api.getProductCategoryProportion({
        }).then(response => {
          if (response.code === 0 && response.data && response.data.length > 0) {
            this.initProductCategoryPieChart(response.data)
          } else {
            if(this.pieCharts.length >= 0){
              chart = this.pieCharts[0]
              if (chart) {
                chart.dispose()
              }
              this.$message.info('当前范围内没有数据')
            }
          }
        }),
        api.getSupplierProductProportion({
        }).then(response => {
          if (response.code === 0 && response.data && response.data.length > 0) {
            this.initSupplierProductPieChart(response.data)
          } else {
            if(this.pieCharts.length >= 1){
              chart = this.pieCharts[1]
              if (chart) {
                chart.dispose()
              }
              this.$message.info('当前范围内没有数据')
            }
          }
        }),
        api.getSupplierSaleV2({
        }).then(response => {
          if (response.code === 0 && response.data && response.data.length > 0) {
            this.initSupplierSellPieChart(response.data)
          } else {
            if(this.pieCharts.length >= 2){
              chart = this.pieCharts[2]
              if (chart) {
                chart.dispose()
              }
              this.$message.info('当前范围内没有数据')
            }
          }
        })
    },
    initChart(data) {
      const chartDom = document.getElementById('chart-line');
      this.chart = echarts.init(chartDom, 'macarons')
      const determineXAxisData = (data) => {
        if (this.activeName == '1' || this.activeName == '2') {
          return data.map(item => item.timeString)
        }
        else if (this.activeName == '3') {
          return data.map(item => item.month)
        }
        return null
      }
      this.chart.setOption({
        // title: {
        //   text: '销售额和订单量统计',
        //   right: 'center'
        // },
        legend: {
          data: ['销售额', '订单量']
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        grid: {
          left: '1%',
          right: '1%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: determineXAxisData(data)
        },
        yAxis: [
          {
            type: 'value',
            name: '销售额'
          },
          {
            type: 'value',
            name: '订单量',
            position: 'right'
          }
        ],
        series: [
          {
            name: '销售额',
            type: 'line',
            yAxisIndex: 0,
            data: data.map(x => x.amount)
          },
          {
            name: '订单量',
            type: 'line',
            yAxisIndex: 1,
            data: data.map(x => x.count)
          }
        ]
      })
    },
    initProductCategoryPieChart(data) {
      const formattedData = data.map(item => ({
        name: item.categoryName,
        value: item.proportion
      }));

      const pieChartOptions = (pieName, pieData) => ({
        title: {
          text: pieName,
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            type: 'pie',
            roseType: 'radius',
            radius: [15, 95],
            // center: ['50%', '38%'],
            startAngle: 180,
            data: pieData,
            label: {
              show: true,
              formatter: '{b}: {d}%'
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            animationEasing: 'cubicInOut',
            animationDuration: 2600
          }
        ]
      });
      const chartDom = document.getElementById('chart-pie-1');
      const pieChat = echarts.init(chartDom, 'macarons');
      pieChat.setOption(pieChartOptions('商品分类占比', formattedData));
      this.pieCharts.push(pieChat);
    },
    initSupplierProductPieChart(data) {
      const formattedData = data.map(item => ({
        name: item.supplierName,
        value: item.skuTotal
      }));

      const pieChartOptions = (pieName, pieData) => ({
        title: {
          text: pieName,
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          bottom: '0%',
          left: 'center'
        },
        series: [
          {
            name: '商品数',
            type: 'pie',
            radius: ['35%', '55%'],
            avoidLabelOverlap: false,
            label: {
                show: true,
                formatter: '{b}: {d}%'
            },
            labelLine: {
              show: true
            },
            data: pieData
          }
        ]
      });
      const chartDom = document.getElementById('chart-pie-2');
      const pieChat = echarts.init(chartDom, 'macarons');
      pieChat.setOption(pieChartOptions('供应商商品占比', formattedData));
      this.pieCharts.push(pieChat);
    },
    initSupplierSellPieChart(data) {
      const formattedData = data.map(item => ({
        name: item.supplierName,
        value: item.netTotalPrice
      }));

      const pieChartOptions = (pieName, pieData) => ({
        title: {
          text: pieName,
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            return params.seriesName + '-' + params.name + '<br/>' + '销售总额 : ' + params.value + '<br/>销售占比 : ' + params.percent + '%';
        }
        },
        legend: {
          bottom: '2%',
          left: 'center'
        },
        series: [
          {
            name: '销售额',
            type: 'pie',
            radius: '50%',
            data: pieData,
            label: {
                show: true,
                formatter: '{b}: {d}%'
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      });

      const chartDom = document.getElementById('chart-pie-3');
      const pieChart = echarts.init(chartDom, 'macarons');
      pieChart.setOption(pieChartOptions('供应商销售额占比', formattedData));
      this.pieCharts.push(pieChart);
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    this.pieCharts.forEach(chart => {
        if (chart) {
          chart.dispose()
        }
      })
  }
};
</script>

<style lang="scss" scoped>
.chart-line {
  width: 100%;
  height: 450px;
}

.chart-pie-container {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.chart-pie {
  width: 32%;
  height: 450px;
}

.radio-group {
  margin: 24px 0 8px;
}
</style>