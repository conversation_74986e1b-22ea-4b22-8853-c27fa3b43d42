import request from '@/utils/request'



// 查询供应商开发应用
export function getOpenApp(id) {
  return request({
    url: '/mall/open-app/get?supplierId=' + id,
    method: 'get'
  })
}


// 更新供应商开发应用
export function updateOpenApp(data) {
  return request({
    url: '/mall/open-app/update',
    method: 'put',
    data: data
  })
}

// 新增供应商开发应用
export function refreshOpenAppSecret(data) {
  return request({
    url: '/mall/open-app/refresh-secret',
    method: 'put',
    data: data
  })
}

