<template>
    <div class="app-container">
      <div class="card-panel-wrapper">
        <supplier-card-panel></supplier-card-panel>
      </div>
      <supplierStatisticsSku></supplierStatisticsSku>
      <supplierStatisticsSale></supplierStatisticsSale>
      <supplierStatisticsAfterSale></supplierStatisticsAfterSale>
    </div>
  </template>
  
  <script>
  import supplierCardPanel from '../components/supplierCardPanel.vue'
  import supplierStatisticsSku from '../components/supplierStatisticsSku.vue'
  import supplierStatisticsSale from '../components/supplierStatisticsSale.vue'
  import supplierStatisticsAfterSale from '../components/supplierStatisticsAfterSale.vue'
  
  export default {
    name: "StatisticsSupplier",
    components: {
      supplierCardPanel,
      supplierStatisticsSku,
      supplierStatisticsSale,
      supplierStatisticsAfterSale
    },
    data() {
      return {
        activeName: 'supplier'
      };
    },
    methods: {}
  };
  </script>
  
  <style lang="scss" scoped>
  </style>