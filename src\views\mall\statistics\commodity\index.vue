<template>
  <div class="app-container">
    <div class="card-panel-wrapper">
      <skuCardPanel></skuCardPanel>
    </div>

    <skuCountCategoryRanking></skuCountCategoryRanking>
    <div class="sku-row">
      <!-- 商品销售数量供应商排行 -->
      <skuSellCountSupplierRanking></skuSellCountSupplierRanking>
      <!-- 商品销量排行 -->
      <skuSellCountRanking></skuSellCountRanking>
      <!-- 商品销售额排行 -->
      <skuSellTotalRanking></skuSellTotalRanking>
    </div>
  </div>
</template>

<script>
import skuCardPanel from '../components/skuCardPanel.vue'
import skuCountCategoryRanking from '../components/skuCountCategoryRanking.vue'
import skuSellCountSupplierRanking from '../components/skuSellCountSupplierRanking.vue'
import skuSellCountRanking from '../components/skuSellCountRanking.vue'
import skuSellTotalRanking from '../components/skuSellTotalRanking.vue'

export default {
  name: "StatisticsCommodity",
  components: {
    skuCardPanel,
    skuCountCategoryRanking,
    skuSellCountSupplierRanking,
    skuSellCountRanking,
    skuSellTotalRanking
  },
  data() {
    return {
      activeName: 'goods'
    };
  },
  created() {},
  methods: {}
};
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
}

.sku-row {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}
</style>