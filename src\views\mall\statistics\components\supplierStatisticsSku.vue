<template>
    <div class="supplierStatisticsSku" :style="{ height: height, width: width }" />
</template>

<script>
import * as api from "@/api/mall/statistics/supplier";
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '300px'
        }
    },
    data() {
        return {
            chart: null
        }
    },
    created() {
        this.getSupplierSkuTotal();
    },
    methods: {
        getSupplierSkuTotal() {
            api.getSupplierSkuTotal({}).then(response => {
                if (response.code === 0) {
                    this.initChart(response.data)
                }
            });
        },
        initChart(data) {
            const names = data.map(item => item.supplierName)
            const totalValues = data.map(item => item.skuSummaryRespVO.total)
            const listingTotalValues = data.map(item => item.skuSummaryRespVO.listingTotal)
            const delistingTotalValues = data.map(item => item.skuSummaryRespVO.delistingTotal)
            const warnValues = data.map(item => item.warn)
            this.chart = echarts.init(this.$el, 'macarons')
            this.chart.setOption({
                title: {
                    text: '供应商商品统计',
                    top: 'top',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['商品总数', '上架商品数', '下架商品数'],
                    right: 'left',
                    top: 'top'

                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                yAxis: [
                    {
                        type: 'category',
                        name: '供应商',
                        data: names
                    }
                ],
                xAxis: [
                    {
                        type: 'value',
                        name: '商品数量',
                        right: 'left'
                    }
                ],
                series: [
                    {
                        name: '商品总数',
                        type: 'bar',
                        data: totalValues.map((value, index) => ({
                            value,
                            itemStyle: {
                                color: warnValues[index] ? 'red' : null
                            }
                        }))
                    },
                    {
                        name: '上架商品数',
                        type: 'bar',
                        data: listingTotalValues
                    },
                    {
                        name: '下架商品数',
                        type: 'bar',
                        data: delistingTotalValues
                    }
                ]
            })
        }
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    }
}
</script>