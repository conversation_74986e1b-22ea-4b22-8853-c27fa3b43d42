import request from '@/utils/request'

// 创建规则商品白名单
export function createProjectWhiteSku(data) {
  return request({
    url: '/mall/project/white-sku/create',
    method: 'post',
    data: data
  })
}

// 创建规则商品白名单
export function createProjectWhiteSkuBatch(data) {
  return request({
    url: '/mall/project/white-sku/create-batch',
    method: 'post',
    data: data
  })
}

// 创建规则商品白名单
export function createProjectWhiteSkuBatchV2(data) {
  return request({
    url: '/mall/project/white-sku/create-batch-v2',
    method: 'post',
    data: data
  })
}

// 删除规则商品白名单
export function deleteProjectWhiteSku(id) {
  return request({
    url: '/mall/project/white-sku/delete?id=' + id,
    method: 'delete'
  })
}

// 删除规则商品白名单
export function deleteProjectWhiteSkuBatch(data) {
  return request({
    url: '/mall/project/white-sku/delete-batch',
    method: 'delete',
    data: data
  })
}


// 获得规则商品白名单分页
export function getProjectWhiteSkuPage(query) {
  return request({
    url: '/mall/project/white-sku/page',
    method: 'get',
    params: query
  })
}
