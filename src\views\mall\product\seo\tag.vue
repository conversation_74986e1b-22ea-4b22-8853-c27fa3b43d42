<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6">
        <div class="flex-between">
          <el-button type="primary" size="small" @click="addGroup">新建分组</el-button>
        </div>
        <div class="side-container">
          <el-menu :default-active="activeGroupId" @select="onMenuSelect" v-if="groupList.length">
            <el-menu-item v-for="(item, index) in groupList" :key="index" :index="item.id + ''">
              <div slot="title">
                <div class="flex-between" @mouseover="handleShowStatus(index, true)" @mouseleave="handleShowStatus(index, false)">
                  <div>{{ item.name }} <el-tag v-if="item.status === 1" type="info" size="small">停用</el-tag></div>
                  <div v-show="item.showActions">
                    <i class="el-icon-edit" @click="editGroup(item)"></i>
                    <i class="el-icon-delete" @click="deleteGroup(item)"></i>
                  </div>
                </div>
              </div>
            </el-menu-item>
          </el-menu>
          <el-empty description="无标签分组" v-else></el-empty>
        </div>
      </el-col>
      <el-col :span="18" v-if="activeGroupId">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="标签名称" prop="name">
            <el-input v-model="queryParams.name" placeholder="请输入标签名称" clearable @keyup.enter.native="handleQuery"/>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 操作工具栏 -->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handlTagAdd"
                      v-hasPermi="['product:tag:create']">新增</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getTagList"></right-toolbar>
        </el-row>

        <!-- 列表 -->
        <el-table v-loading="tagLoading" :data="tagList">
          <el-table-column label="标签编号" align="center" prop="id"/>
          <el-table-column label="标签名称" align="center" prop="name"/>
          <el-table-column label="标签备注" align="center" prop="memo"/>
          <el-table-column label="状态" align="center" prop="status">
            <template v-slot="scope">
              <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status"/>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template v-slot="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template v-slot="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleTagUpdate(scope.row)"
                        v-hasPermi="['product:tag:update']">修改
              </el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleTagDelete(scope.row)"
                        v-hasPermi="['product:tag:delete']">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页组件 -->
        <pagination v-show="tagTotal > 0" :total="tagTotal" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                    @pagination="getTagList"/>
      </el-col>
    </el-row>

    <TagGroupForm ref="tagGroupForm" @on-update="loadGroupList"></TagGroupForm>
    <TagForm ref="tagForm" @on-update="handleQuery"></TagForm>
  </div>
</template>
<script>
import * as api from "@/api/mall/product/tag";
import TagGroupForm from '@/views/mall/product/seo/components/tag-group-form'
import TagForm from '@/views/mall/product/seo/components/tag-form'
export default {
  name: "ProductSeoTag",
  components: { TagGroupForm, TagForm },
  data() {
    return {
      activeGroupId: '',
      groupList: [],
      tagLoading: false,
      showSearch: true,
      tagList: [],
      tagTotal: 0,
      queryParams: {
        name: '',
        pageNo: 1,
        pageSize: 10
      }
    }
  },
  computed: {
   
  },
  watch: {
    activeGroupId() {
      this.handleQuery()
    }
  },
  mounted() {
    this.loadGroupList()
  },
  methods: {
    handleShowStatus(index, status) {
      let item = this.groupList[index]
      item.showActions = status
      this.$set(this.groupList, index, item)
    },
    async loadGroupList() {
      let params = {
        pageNo: 1,
        pageSize: 100
      }
      let res = await api.getGroupPage(params)
      this.groupList = res.data.list || []
    },
    addGroup() {
      this.$refs.tagGroupForm.show()
    },
    editGroup(item) {
      this.$refs.tagGroupForm.show(item.id)
    },
    deleteGroup(item) {
      const id = item.id;
      this.$modal.confirm('是否确认删除分组"' + item.name + '"吗?').then(function () {
        return api.deleteGroup(id);
      }).then(() => {
        this.loadGroupList()
        this.activeGroupId = null
        this.$modal.msgSuccess("删除成功")
      })
    },
    handlTagAdd() {
      this.$refs.tagForm.show(this.activeGroupId)
    },
    handleTagUpdate(item) {
      this.$refs.tagForm.show(this.activeGroupId, item.id)
    },
    handleTagDelete(item) {
      const id = item.id;
      this.$modal.confirm('是否确认删除标签"' + item.name + '"吗?').then(function () {
        return api.deleteTag(id);
      }).then(() => {
        this.handleQuery();
        this.$modal.msgSuccess("删除成功");
      })
    },
    onMenuSelect(key, keyPath) {
      this.activeGroupId = key
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getTagList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    getTagList() {
      if(!this.activeGroupId) {
        return
      }
      let params = Object.assign({}, this.queryParams)
      params.groupId = this.activeGroupId
      this.tagLoading = true
      api.getTagPage(params).then(res => {
        this.tagList = res.data.list
        this.tagTotal = res.data.total
      }).finally(() => {
        this.tagLoading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>

.app-container {
  .el-menu-item {
    height: 40px;
    line-height: 40px;
  }
  .side-container {
    max-height: 600px;
    min-height: 500px;
    overflow-y: auto;
  }
}

</style>
