<template>
  <div class="app-container" v-show="open">
    <el-dialog title="京东商品同步" :visible.sync="open" @close="close" width="600px">
      <el-form ref="form" :model="form" :rules="rules" v-loading="loading">
        <el-form-item label="商品SKU" prop="skuId">
          <el-input placeholder="请输入京东商品SKU" v-model="form.skuId" clearable :maxlength="50"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
        <el-button @click="close">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as api from "@/api/mall/config/vopConfig.js"
export default {
  name: 'ProductSyncForm',
  props: {
    spuId: {
      type: [Number, String],
      default: () => null
    },
    open: {
      type: <PERSON>olean,
      default: () => false
    }
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      form: {},
      rules: {
        skuId: [{required: true, message: "商品SKU不能为空", trigger: "blur"}]
      }
    }
  },
  watch: {
    open(newVal, oldVal) {
      if(newVal) {

      }
    }
  },
  methods: {
    close() {
      this.$emit('update:open', false)
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        console.log('ratesForm:', this.ratesForm)
        if (!valid) {
          return;
        }
        let params = {
          skuId: this.form.skuId
        }
        this.submitLoading = true
        api.syncVopSingleGoods(params).then(res => {
          if(res.code === 0) {
            res.data ? this.$modal.msgSuccess('同步成功') : this.$modal.msgError('同步失败')
            this.$emit("success");
            this.close()
          }
        }).finally(() => {
          this.submitLoading = false
        })
      })
    },
    cancel() {
      this.close()
    }
  }
}
</script>

<style lang="scss">

</style>