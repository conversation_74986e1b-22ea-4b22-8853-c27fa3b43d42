<template>
  <div class="app-container purchase-list">
    <template v-if="isPurchaseOn">
      <!-- 搜索工作栏 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="75px">
        <el-form-item label="审批单号" prop="bpmNo"  v-if="isBpm">
          <el-input v-model="queryParams.bpmNo" placeholder="请输入审批单号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="员工编号" prop="userNo">
          <el-input v-model="queryParams.userNo" placeholder="请输入员工编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="订单号" prop="orderNo">
          <el-input v-model="queryParams.orderNo" placeholder="请输入订单号" clearable @keyup.enter.native="handleQuery"/>
        </el-form-item>
        <!-- <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MALL_PURCHASE_STATUS)" :key="dict.value"
              :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item> -->
        <template v-if="isBpm">
          <el-form-item label="审批结果" prop="auditStatus">
            <el-select v-model="queryParams.auditStatus" placeholder="请选择审批结果" clearable size="small">
              <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MALL_PURCHASE_AUDIT_STATUS)" :key="dict.value"
                :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
        </template>

        <template v-if="isProject">
          <el-form-item label="项目编号" prop="projectNo">
            <el-input v-model="queryParams.projectNo" placeholder="请输入项目编号" clearable @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="项目名称" prop="projectName">
            <el-input v-model="queryParams.projectName" placeholder="请输入项目名称" clearable @keyup.enter.native="handleQuery" />
          </el-form-item>
        </template>

        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 列表 -->
      <el-table ref="table" v-loading="loading" :data="list" show-summary :summary-method="getSummaries" :default-expand-all="false">
        <!-- <el-table-column label="采购单号" align="center" prop="id" width="190" /> -->
        <el-table-column type="expand">
          <template slot-scope="{ row }">
            <div v-if="row.orders && row.orders.length">
              <el-card v-for="(order,index) in row.orders" :key="index" class="order-block">
                <div class="flex-start order-base-info"> 
                  <div>供应商：{{ order.supplierName }}</div>
                  <div>订单号：{{ order.no }}</div>
                  <div>金额：{{ formatMoney(order.orderPrice) }}</div>
                </div>
                <div class="order-products"> 
                  <el-table :data="order.items" style="width: 100%">
                    <el-table-column prop="skuName" label="商品">
                      <template v-slot="subScope">
                        <img :src="subScope.row.picUrl" alt="" height="30" /> <span>{{ subScope.row.skuName }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="count" label="数量" width="120">
                      <template v-slot="subScope">
                        <span>x{{ subScope.row.count }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="address" label="单价" width="180">
                      <template v-slot="subScope">
                        <span>{{ formatMoney(subScope.row.skuPrice) }}</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-card>
            </div>
            <el-empty v-else :image-size="50"></el-empty>
          </template>
        </el-table-column>
        <el-table-column label="下单人" align="center" prop="userName"></el-table-column>
        <el-table-column label="订单数量" align="center" prop="orderCount"></el-table-column>
        <el-table-column label="采购金额" align="center" prop="totalAmount">
          <template v-slot="scope">
            <span>{{ formatMoney(scope.row.totalAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="提交时间" align="center" prop="createTime" width="190">
          <template v-slot="scope">
            {{ parseTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template v-slot="{ row }">
            <dict-tag :type="DICT_TYPE.MALL_PURCHASE_STATUS" :value="row.status" v-if="row.status !== 0 || !row.bpmNo"/>
            <dict-tag :type="DICT_TYPE.MALL_PURCHASE_AUDIT_STATUS" :value="row.auditStatus" v-if="row.bpmNo && row.status === 0"/>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template v-slot="scope">
            <el-button size="mini" type="text" icon="el-icon-view" @click="jumpToDetail(scope.row)">查看详情</el-button>
            <!-- <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
              v-hasPermi="['trade:purchase:update']">取消</el-button> -->
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
        @pagination="getList" />

    
    </template>
    <el-empty v-else description="审批流和经费卡开关配置关闭"></el-empty>
  </div>
</template>

<script>
import * as api from "@/api/mall/trade/purchase"
import { configMixins } from '@/views/mall/config/components/configMixin.js'
export default {
  name: "Purchase",
  mixins: [ configMixins ],
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购单列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        id: null,
        bpmNo: null,
        userNo: null,
        orderNo: null,
        projectNo: null,
        purchaseReason: null,
        auditStatus: null,
        createTime: [],
        projectName: null,
        status: null,
      },
      // 表单校验
      rules: {
      }
    };
  },
  mounted() {
    if(this.isPurchaseOn) {
      this.getList()
    }
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      api.getPurchasePageV2(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = [];
      sums[0] = '合计'
      let offset = 0, s1 = 0, s2 = 0 
      if(this.isBpm) {
        offset = 1
      }
      this.list.forEach(item => {
        s1 += item.orderCount
        s2 += item.totalAmount
      })
      sums[1 + offset] = s1
      sums[2 + offset] = this.formatMoney(s2)

      return sums;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    jumpToDetail(row) {
      this.$router.push({ name: 'PurchaseDetail', query: { id: row.id } });
    },
  }
};
</script>
<style scoped lang="scss">
.purchase-list {
  .order-block {
    margin: 0 0 10px;
  }
  .order-base-info {
    width: 100%;
    margin: 0 0 10px;
    div {
      margin: 0 10px;
    }
  }
  .order-products {
    width: 100%;
  }
  .el-card__body {
    padding: 10px;
  }
}
</style>
