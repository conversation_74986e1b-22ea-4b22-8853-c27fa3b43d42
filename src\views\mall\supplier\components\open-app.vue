<template>
  <div> 
    <el-card class="box-card" v-loading="loading">
      <div slot="header" class="clearfix">
        <span class="title">开放API</span>
        <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="openAppInfo.status" />
        <el-button style="float: right; padding: 3px 0" type="text" v-hasPermi="['mall:supplier-profile:openApp']" v-if="openAppInfo.id && formMode === 'init'" @click="edit">修改</el-button>
      </div>
      <div v-if="formMode === 'init'">
        <el-descriptions title="" :column="1" :border="true" v-if="openAppInfo.id">
          <el-descriptions-item label="APP_KEY">{{ openAppInfo.appKey || '--'}}</el-descriptions-item>
          <el-descriptions-item label="APP_SECRET">
            {{ openAppInfo.appSecret || '--'}}
            <el-button size="small" plain type="primary" style="margin-left:10px" @click="refreshSecret">刷新</el-button>
          </el-descriptions-item>
          <el-descriptions-item label="IP白名单">{{ openAppInfo.ipWhiteList || '--' }}</el-descriptions-item>
          <el-descriptions-item label="消息订阅状态">
            <el-tag :type="supplierInfo.subMessage ? 'success' : 'warning'">{{ supplierInfo.subMessage ? '打开' : '关闭' }}</el-tag>
          </el-descriptions-item>
        </el-descriptions>
        <el-empty v-else>
          <el-button type="text" icon="el-icon-plus" @click="add">开始配置</el-button>
        </el-empty>
      </div>
      <div style="width:800px;">
        <el-form v-if="formMode === 'edit'" ref="form" label-position="left" :model="form" :rules="rules" label-width="150px">
          <el-form-item label="IP白名单" prop="ipWhiteList">
            <el-input v-model="form.ipWhiteList" :rows="4" type="textarea" :maxlength="200" placeholder="请输入IP白名单，多个IP逗号分隔" />
          </el-form-item>
          <el-form-item>
            <el-button type="default" @click="cancelForm">取 消</el-button>
            <el-button type="primary" @click="submitForm" :loading="submitLoading">提 交</el-button>
          </el-form-item>
        </el-form>
      </div>

    </el-card>
  </div>
</template>

<script>
import * as api from '@/api/mall/supplier/supplier-profile'
export default {
  name: 'SupplierOpenApp',
  props: {
    supplierInfo: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      formMode: 'init',
      loading: false,
      submitLoading: false,
      openAppInfo: {},
      form: {
        id: undefined,
        ipWhiteList: ''
      },
      rules: {
        ipWhiteList: [
          { required: true, trigger: 'blur', message: '请输入IP白名单' }
        ],
      }
    }
  },
  mounted() {
    this.loadOpenAppInfo()
  },
  methods: {
    async loadOpenAppInfo() {
      let res = await api.getSupplierOpenApp()
      if(res.data) {
        this.openAppInfo = res.data
      }
    },
    add() {
      this.formMode = 'edit'
    },
    edit() {
      this.formMode = 'edit'
      Object.keys(this.form).forEach(key => {
        this.form[key] = this.openAppInfo[key]
      })
    },
    cancelForm() {
      this.formMode = 'init'
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return
        }
        this.doSubmit()
      })
    },
    async doSubmit() {
      this.submitLoading = true
      try {
        let data = {...this.openAppInfo}
        Object.assign(data, this.form)
        await api.updateSupplierOpenApp(data)
        this.$modal.msgSuccess("修改成功")
        this.$emit('on-update')
        this.loadOpenAppInfo()
        this.submitLoading = false
        this.formMode = 'init'
      } catch(e) {
        this.$modal.msgError("保存失败，请稍后重试")
        this.submitLoading = false
      }
    },
    refreshSecret() {
      const id = this.openAppInfo.id;
      if(!id) {
        return
      }
      this.$modal.confirm('密钥刷新后需要通知到调用方，是否确认刷新密钥?').then(function() {
        return api.refreshSupplierOpenAppSecret({id: id})
      }).then(() => {
        this.$modal.msgSuccess("刷新成功")
        this.loadOpenAppInfo()
      }).catch(() => {})
    },
  }

}
</script>