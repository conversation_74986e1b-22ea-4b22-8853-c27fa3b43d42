<template>
  <el-row :gutter="40" class="panel-group">
    <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
      <div class="sell-card-panel">
        <div class="card-panel-icon-wrapper icon-people">
          <svg-icon icon-class="map" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            总销量
          </div>
          <count-to :start-val="0" :end-val="sellCount" :duration="2600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
      <div class="sell-card-panel">
        <div class="card-panel-icon-wrapper icon-message">
          <svg-icon icon-class="pay" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            总销售额
          </div>
          <count-to :start-val="0" :end-val="sellAmount" :duration="3000" :decimals="decimals" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
      <div class="sell-card-panel">
        <div class="card-panel-icon-wrapper icon-money">
          <svg-icon icon-class="radio" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            平均单价
          </div>
          <count-to :start-val="0" :end-val="averagePrice" :duration="3200" :decimals="decimals"
            class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
      <div class="sell-card-panel">
        <div class="card-panel-icon-wrapper icon-category">
          <svg-icon icon-class="select" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            待结算金额
          </div>
          <count-to :start-val="0" :end-val="notSettleAmount" :duration="3200" :decimals="decimals"
            class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
      <div class="sell-card-panel">
        <div class="card-panel-icon-wrapper icon-drag">
          <svg-icon icon-class="drag" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            待确认订单
          </div>
          <count-to :start-val="0" :end-val="notConfirmOrderCount" :duration="2600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
      <div class="sell-card-panel">
        <div class="card-panel-icon-wrapper icon-merchant">
          <svg-icon icon-class="merchant" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            待发货订单
          </div>
          <count-to :start-val="0" :end-val="notDeliveryCount" :duration="3000" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
      <div class="sell-card-panel">
        <div class="card-panel-icon-wrapper icon-druid">
          <svg-icon icon-class="druid" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            售后订单
          </div>
          <count-to :start-val="0" :end-val="afterSellCount" :duration="3200" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="6" :sm="6" :lg="6" class="card-panel-col">
      <div class="sell-card-panel">
        <div class="card-panel-icon-wrapper icon-chart">
          <svg-icon icon-class="chart" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            已完成订单
          </div>
          <count-to :start-val="0" :end-val="completeCount" :duration="3200" class="card-panel-num" />
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import * as api from "@/api/mall/statistics/trade";
import CountTo from 'vue-count-to'

export default {
  components: {
    CountTo
  },
  data() {
    return {
      decimals: 2,
      sellCount: 0,
      sellAmount: 0,
      averagePrice: 0,
      notSettleAmount: 0,
      notConfirmOrderCount: 0,
      notDeliveryCount: 0,
      afterSellCount: 0,
      completeCount: 0
    };
  },
  created() {
    Promise.all([
      this.getSellCount(),
      this.getSellAmount(),
      this.getNotSettleAmount(),
      this.getOrderSummary(),
      this.getOrderSummaryByDept(),
      this.getOrderSummaryByProject(),
      this.getOrderSummaryBySupplier(),
      this.getAfterSaleOrderSummary()
    ]).then(([sellCountData, sellAmountData, notSettleAmount, orderSummaryData, orderSummaryByDeptData, orderSummaryByProjectData, orderSummaryBySupplierData, afterSaleOrderSummaryData]) => {
      this.sellCount = sellCountData,
        this.sellAmount = sellAmountData,
        this.averagePrice = sellAmountData / sellCountData,
        this.notSettleAmount = notSettleAmount,
        this.notConfirmOrderCount = orderSummaryData.notConfirmOrderCount,
        this.notDeliveryCount = orderSummaryData.notDeliveryCount,
        this.afterSellCount = orderSummaryData.afterSellCount,
        this.completeCount = orderSummaryData.completeCount
    });
  },
  methods: {
    getSellCount() {
      return new Promise((resolve, reject) => {
        api.getSellCount({}).then(response => {
          if (response.code === 0) {
            resolve(response.data);
          } else {
            reject(new Error('Failed to fetch sell product category summary'));
          }
        });
      });
    },
    getSellAmount() {
      return new Promise((resolve, reject) => {
        api.getSellAmount({}).then(response => {
          if (response.code === 0) {
            resolve(response.data);
          } else {
            reject(new Error('Failed to fetch sell product category summary'));
          }
        });
      });
    },
    getNotSettleAmount() {
      return new Promise((resolve, reject) => {
        api.getNotSettleAmount({}).then(response => {
          if (response.code === 0) {
            resolve(response.data);
          } else {
            reject(new Error('Failed to fetch sell product category summary'));
          }
        });
      });
    },
    getOrderSummary() {
      return new Promise((resolve, reject) => {
        api.getOrderSummary({}).then(response => {
          if (response.code === 0) {
            resolve(response.data);
          } else {
            reject(new Error('Failed to fetch sell product category summary'));
          }
        });
      });
    },
    getOrderSummaryByDept() {
      return new Promise((resolve, reject) => {
        api.getOrderSummaryByDept({limit:10}).then(response => {
          if (response.code === 0) {
            resolve(response.data);
          } else {
            reject(new Error('Failed to fetch sell product category summary'));
          }
        });
      });
    },
    getOrderSummaryByProject() {
      return new Promise((resolve, reject) => {
        api.getOrderSummaryByProject({}).then(response => {
          if (response.code === 0) {
            resolve(response.data);
          } else {
            reject(new Error('Failed to fetch sell product category summary'));
          }
        });
      });
    },
    getOrderSummaryBySupplier() {
      return new Promise((resolve, reject) => {
        api.getOrderSummaryBySupplier({}).then(response => {
          if (response.code === 0) {
            resolve(response.data);
          } else {
            reject(new Error('Failed to fetch sell product category summary'));
          }
        });
      });
    },
    getAfterSaleOrderSummary() {
      return new Promise((resolve, reject) => {
        api.getAfterSaleOrderSummary({}).then(response => {
          if (response.code === 0) {
            resolve(response.data);
          } else {
            reject(new Error('Failed to fetch sell product category summary'));
          }
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.panel-group {
  margin-top: 18px;

  .card-panel-col {
    margin-bottom: 32px;
  }

  .sell-card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-category {
        background: #866e5d;
      }

      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-drag {
        background: #1296db;
      }

      .icon-merchant {
        background: #d4237a;
      }

      .icon-druid {
        background: #13227a;
      }

      .icon-chart {
        background: #f4ea2a;
      }
    }

    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-category {
      color: #866e5d;
    }

    .icon-drag {
      color: #1296db;
    }

    .icon-merchant {
      color: #d4237a;
    }

    .icon-druid {
      color: #13227a;
    }

    .icon-chart {
      color: #f4ea2a;
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px 26px 26px 0;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}
</style>