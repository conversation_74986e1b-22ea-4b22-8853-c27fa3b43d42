<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-tabs class="form" v-model="queryParams.type" type="card" style="margin-bottom: 10px;" @tab-click="handleQuery">
      <el-tab-pane label="京东分类" :name="1"></el-tab-pane>
      <el-tab-pane label="平台分类" :name="2"></el-tab-pane>
    </el-tabs>
    
    <!-- 列表 -->
    <el-table v-if="refreshTable" v-loading="loading" :data="list"  row-key="id" :default-expand-all="isExpandAll"
              :tree-props="{children: 'children', hasChildren: 'hasChildren'}" lazy :load="getChildrenList">
      <el-table-column label="分类ID" prop="categoryId" width="240px"/>
      <el-table-column label="分类名称" prop="categoryName" width="200px"/>
      <el-table-column label="分类图标" align="center" prop="icon">
        <template v-slot="scope">
          <el-image v-if="scope.row.icon" style="height: 16px;" :src="scope.row.icon" :fit="fit"></el-image>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="分类排序" align="center" prop="orderSort"/>
      <el-table-column label="开启状态" key="status" align="center">
        <template v-slot="scope">
          <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" @change="handleStatusChange(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      layout="total"
      :total="totalCount">
    </el-pagination>


    <ProductCategoryTree ref="productCategoryTree" @on-select="handleSelectProductCategory" />
  </div>
</template>

<script>
import * as api from "@/api/mall/product/configCategory.js";
import ProductCategoryTree from "@/components/ProductCategoryTree";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "ConfigProductCategory",
  components: {
    Treeselect, ProductCategoryTree
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      originalList: [],
      // 分类列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        type: 1
      },
      // 表单参数
      form: {},
      totalCount: 0,
      // 表单校验
      rules: {
        parentId: [{required: true, message: "请选择上级分类", trigger: "blur"}],
        name: [{required: true, message: "分类名称不能为空", trigger: "blur"}],
        sort: [{required: true, message: "分类排序不能为空", trigger: "blur"}],
        status: [{required: true, message: "状态不能为空", trigger: "blur"}],
      }
    };
  },
  created() {
    this.handleQuery()
  },
  methods: {
    async getTotalCount() {
      let res = await api.getCategoryTotalCount({
        type: this.queryParams.type
      })
      if(res.code === 0) {
        this.totalCount = res.data
      }
    },
    /** 查询列表 */
    getRootList() {
      this.loading = true;
      // 处理查询参数
      let params = {...this.queryParams};
      // 执行查询
      api.getCategoryRootList(params).then(response => {
        this.originalList = response.data
        this.list = this.handleTree(response.data, "categoryId", "parentId");
        this.list.forEach(item => {
          item.hasChildren = true
        })
        console.log('nodes===', this.list)
        this.loading = false;
      });
    },
    getChildrenList(row, treeNode, resolve) {
      // 处理查询参数
      let params = {
        parentCategoryId: row.categoryId
      };
      // 执行查询
      api.getCategoryChildTreeList(params).then(response => {
        this.originalList = response.data
        let nodes = this.handleTree(response.data, "categoryId", "parentId");
        nodes.forEach(item => {
          item.hasChildren = item.childCategoryList && item.childCategoryList.length > 0
        })
        console.log('nodes===', nodes)
        resolve(nodes)
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        parentId: undefined,
        categoryName: undefined,
        categoryId: undefined,
        orderSort: 0,
        categoryLevel: undefined,
        status: 1
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getRootList();
      this.getTotalCount();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.categoryName,
        children: node.children
      };
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加分类";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      api.getProductCategory(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改商品分类";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.id != null) {
          api.updateProductCategory(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getRootList();
          });
          return;
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.categoryId;
      this.$modal.confirm('是否确认删除分类编号为"' + id + '"的数据项?').then(function () {
        return api.deleteProductCategory(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    // 状态修改
    handleStatusChange(row) {
      let text = row.status === 1 ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '"分类吗?').then(function() {
          let params = { id: row.id, status: row.status }
          return api.updateProductCategoryStatus(params);
        }).then(() => {
          this.$modal.msgSuccess(text + "成功");
        }).catch(function() {
          row.status = row.status === 1 ? 0: 1;
        });
    },
    addProductCategory() {
      this.$refs.productCategoryTree.show()
    },
    async handleSelectProductCategory(nodes) {
      console.log('nodes===', nodes)
      if(nodes && nodes.length) {
        let ids = nodes.map(item => item.categoryId)
        let params = { categoryIdList: ids }
        let res = await api.cloneProductCategory(params)
        if(res.code === 0) {
          this.$modal.msgSuccess("添加成功");
          this.handleQuery()
        } else {
          this.$modal.msgSuccess(res.msg);
        } 
      }
      
    }
  }
};
</script>