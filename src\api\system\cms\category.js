import request from "@/utils/request";

// 创建cms分类
export function createCmsCategory(data) {
  return request({
    url: '/system/cms-category/create',
    method: 'post',
    data: data
  })
}

// 更新cms分类
export function updateCmsCategory(data) {
  return request({
    url: '/system/cms-category/update',
    method: 'put',
    data: data
  })
}

// 删除cms分类
export function deleteCmsCategory(id) {
  return request({
    url: '/system/cms-category/delete?id=' + id,
    method: 'delete'
  })
}

// 获得cms分类
export function getCmsCategory(id) {
  return request({
    url: '/system/cms-category/get?id=' + id,
    method: 'get'
  })
}

// 获得cms分类分页
export function getCmsCategoryPage(query) {
  return request({
    url: '/system/cms-category/page',
    method: 'get',
    params: query
  })
}

// 获得cms分类分页
export function getCmsCategoryList(query) {
  return request({
    url: '/system/cms-category/list-all',
    method: 'get',
    params: query
  })
}

