<template>
  <div> 
    <el-steps :active="curStep" align-center finish-status="success">
      <el-step title="提交入驻申请" description="请您准备好入驻的相关材料并提交，比如法人身份证，执照以及相关资质"></el-step>
      <el-step title="等待平台审核" description="请您耐心等待平台审核结果，平台会在1-3个工作日完成审核"></el-step>
      <el-step title="入驻审核成功" description="入驻审核通过，您可以看到商城相关功能菜单" :status="status === 2 ? 'success' : 'wait'"></el-step>
    </el-steps>
  </div>
</template>

<script>
export default {
  name: 'SupplierApplyFormSteps',
  props: {
    status: {
      type: Number,
      default: 0
    }
  },
  computed: {
    curStep() {
      if([0].includes(this.status)) {
        return 0
      } else if([1,3].includes(this.status)) {
        return 1
      } else if([2].includes(this.status)) {
        return 2
      }

      return 0
    }
  },
  data() {
    return {

    }
  },
  methods: {

  }

}
</script>

<style>

</style>