<template>
  <div class="app-container" v-show="open">
    <el-dialog title="商品区域置顶配置" :visible.sync="open" @close="close" width="1100px">
      <div class="flex-between"> 
        <div> 
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-if="tableDataList.length <= 20">添加商品</el-button>
          <el-button type="warning" plain icon="el-icon-trash" size="mini" @click="handleDeleteAll" >全部清空</el-button>
        </div>
        <div> 
          <span>SKU数量: {{ tableDataList ? tableDataList.length : 0 }}</span>
        </div>
      </div>
      <el-table
        :data="tableDataList"
        :loading="loading"
        border
        style="width: 100%;margin:20px 0;">
        <el-table-column prop="skuId" label="序号" width="60" align="center">
          <template v-slot="scope">
            <span>{{ scope.$index + 1 }}</span><br>
          </template>
        </el-table-column>
        <el-table-column prop="skuName" label="商品名称" width="450"> 
          <template v-slot="scope">
            <div class="product-info">
              <img v-if="scope.row.picUrl" :src="scope.row.picUrl" alt="分类图片" class="img-height" />
              <div :title="scope.row.skuName" class="message">【{{ scope.row.supplierName }}】{{ scope.row.skuName }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="skuId"   label="平台/三方SKU" width="190">
          <template v-slot="scope">
            <span>{{ scope.row.id }}</span><br>
            <span v-if="scope.row.skuInnerId">{{ scope.row.skuInnerId }}</span>
          </template>
        </el-table-column>
        <el-table-column label="SKU状态" align="center" prop="status">
          <template v-slot="{row}">
            <dict-tag :type="DICT_TYPE.PRODUCT_SPU_STATUS" :value="row.status"/>
            <el-popover
              style="margin-left:5px;"
              v-if="row.statusError"
              title="不可售"
              width="200"
              trigger="click"
              :content="row.indexStatusDetail.reason">
              <i slot="reference" class="el-icon-warning"></i>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="170">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="deleteSelf(scope.$index)">删除</el-button>
            <el-button type="text" size="small" @click="moveTop(scope.row, scope.$index)" v-if="scope.$index > 1">置顶</el-button>
            <el-button type="text" size="small" @click="moveUp(scope.row, scope.$index)" v-if="scope.$index > 0 ">上移</el-button>
            <el-button type="text" size="small" @click="moveDown(scope.row, scope.$index)" v-if="scope.$index >= 0 && scope.$index < tableDataList.length -1">下移</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
        <el-button @click="close">取 消</el-button>
      </div>
    </el-dialog>

    <ProductSkuSelect ref="productSkuSelect" @on-confirmed="handleAddCallback"></ProductSkuSelect>
  </div>
</template>

<script>
import { getSeoSkuPage, querySkuIndexStatus } from "@/api/mall/product/sku";
import { updateSeoCard } from "@/api/mall/product/seoCard"
import ProductSkuSelect from "@/views/mall/product/spu/components/sku-select"
export default {
  name: 'ProductSeoCardSku',
  components: { ProductSkuSelect},
  data() {
    return {
      open: false,
      loading: false,
      submitLoading: false,
      cardInfo: null,
      topSkuIdList: [], // sku id数组
      tableDataList: []
    }
  },
  methods: {
    async parseTopSku() {
      this.topSkuIdList = []
      this.tableDataList = []

      if(this.cardInfo.topSku) {
        this.topSkuIdList = this.cardInfo.topSku.split(',')
        await this.loadTableDataList()
      }
    },
    init(cardInfo) {
      this.cardInfo = cardInfo
      this.parseTopSku()
      this.open = true
    },
    handleAdd() {
      let extParams = {
        seoStatus: 1,
        sortType: 50
      }
      if(this.cardInfo.content) {
        let contentObj = JSON.parse(this.cardInfo.content)
        if(contentObj && contentObj.tags) {
          extParams.tags = contentObj.tags.map(tag => tag.id)
        }
      }

      this.$refs.productSkuSelect.init(extParams)
    },
    async handleAddCallback(rows) {
      if(!rows || !rows.length) {
        return;
      }
      let newList = rows.map(item => item.id)
      const seen = new Map()
      this.topSkuIdList.forEach(item => {
        seen.set(item, true)
      })
      this.topSkuIdList = this.topSkuIdList.concat(
        newList.filter(item => {
          if (!seen.has(item)) {
            seen.set(item, true)
            return true
          }
          return false
        })
      )
      await this.loadTableDataList()
    },
    async loadTableDataList() {
      if(!this.topSkuIdList || !this.topSkuIdList.length) {
        return
      }
      let params = {
        skuIds: this.topSkuIdList,
        pageSize: 50
      }
      this.loading = true
      getSeoSkuPage(params).then(async (response) => {
        let list = ((response.data && response.data.list) || []).map(item => {
          if (item.showStatus === undefined) {
            item.showStatus = 1
          }
          return item
        });
        await this.getSkuIndexStatus(list) 
        this.tableDataList = list
      }).finally(() => {
        this.loading = false
      })
    },
    async getSkuIndexStatus(list) {
      if(!list || !list.length) {
        return
      }
      let params = {
        skuIds: list.map(item => item.id)
      }
      let res = await querySkuIndexStatus(params)
      if(res.data && res.data.length) {
        list.forEach((item) => {
          item.indexStatusDetail = res.data.find(indexItem => indexItem.skuId === item.id)
          item.statusError = item.indexStatusDetail && !item.indexStatusDetail.needIndex && (item.status && item.platformStatus && item.showStatus)
        })
      }
    },
    handleDeleteAll() {
      this.topSkuIdList = []
      this.tableDataList = []
    },
    submitForm() {
      this.submitLoading = true
      let ids = this.tableDataList.map(item => item.id) || []
      this.cardInfo.topSku = this.topSkuIdList.join(',')
      updateSeoCard(this.cardInfo).then(res => {
        if(res.code === 0) {
          this.$modal.msgSuccess("保存成功");
          this.$emit("update-success");
          this.close()
        }
      }).finally(() => {
        this.submitLoading = false
      })
    },
    close() {
      this.open = false
    },
    updateTopSkuIdList() {
      this.topSkuIdList = this.tableDataList.map(item => item.id) || []
    },
    deleteSelf(index) {
      const list = this.tableDataList
      list.splice(index, 1)
      this.updateTopSkuIdList()
    },
    moveTop(item, index) {
      if (index <= 0) return
      const list = this.tableDataList
      list.splice(index, 1)
      list.splice(0, 0, item)
      this.updateTopSkuIdList()
    },
    moveUp(item, index) {
      if (index <= 0) return
      const list = this.tableDataList
      list.splice(index - 1, 2, list[index], list[index - 1])
      this.updateTopSkuIdList()
    },
    moveDown(item, index) {
      if (index >= this.tableDataList.length - 1) return
      const list = this.tableDataList
      list.splice(index, 2, list[index + 1], list[index])
      this.updateTopSkuIdList()
    }
  }
}
</script>

<style lang="scss">

</style>