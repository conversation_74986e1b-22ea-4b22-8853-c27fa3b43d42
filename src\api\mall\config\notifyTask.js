import request from '@/utils/request'

// 创建会员通知任务
export function createNotifyTask(data) {
  return request({
    url: '/mall/notify-task/create',
    method: 'post',
    data: data
  })
}

// 更新会员通知任务
export function updateNotifyTask(data) {
  return request({
    url: '/mall/notify-task/update',
    method: 'put',
    data: data
  })
}

// 校验模板
export function validateTplCode(data) {
  return request({
    url: '/mall/notify-task/validate-tpl',
    method: 'post',
    data: data
  })
}

// 删除会员通知任务
export function deleteNotifyTask(id) {
  return request({
    url: '/mall/notify-task/delete?id=' + id,
    method: 'delete'
  })
}

// 获得会员通知任务
export function getNotifyTask(id) {
  return request({
    url: '/mall/notify-task/get?id=' + id,
    method: 'get'
  })
}

// 获得会员通知任务分页
export function getNotifyTaskPage(query) {
  return request({
    url: '/mall/notify-task/page',
    method: 'get',
    params: query
  })
}

// 获得会员通知任务枚举列表
export function getNotifyTaskEnumList(query) {
  return request({
    url: '/mall/notify-task/enum-list',
    method: 'get',
    params: query
  })
}
