import request from '@/utils/request'

// 创建运营区域
export function createSeoCard(data) {
  return request({
    url: '/product/seo-card/create',
    method: 'post',
    data: data
  })
}

// 更新运营区域
export function updateSeoCard(data) {
  return request({
    url: '/product/seo-card/update',
    method: 'put',
    data: data
  })
}

// 更新运营区域状态
export function updateSeoCardStatus(data) {
  return request({
    url: '/product/seo-card/update-status',
    method: 'put',
    data: data
  })
}

// 删除运营区域
export function deleteSeoCard(id) {
  return request({
    url: '/product/seo-card/delete?id=' + id,
    method: 'delete'
  })
}

// 获得运营区域
export function getSeoCard(id) {
  return request({
    url: '/product/seo-card/get?id=' + id,
    method: 'get'
  })
}

// 获得运营区域分页
export function getSeoCardPage(query) {
  return request({
    url: '/product/seo-card/page',
    method: 'get',
    params: query
  })
}

