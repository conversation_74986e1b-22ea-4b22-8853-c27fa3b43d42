<template>
  <div> 
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span class="title">客服信息</span>
        <el-button style="float: right; padding: 3px 0" type="text" v-hasPermi="['mall:supplier-profile:update']" v-if="formMode === 'init'" @click="edit">修改</el-button>
      </div>
      <div v-if="formMode === 'init'">
        <el-descriptions title="" :column="1" :border="true">
          <el-descriptions-item label="客服姓名">{{ supplierInfo.serviceAgent || '--'}}</el-descriptions-item>
          <el-descriptions-item label="客服电话">{{ supplierInfo.servicePhone || '--'}}</el-descriptions-item>
          <el-descriptions-item label="客服微信号">{{ supplierInfo.serviceWechatId || '--'}}</el-descriptions-item>
          <el-descriptions-item label="客服QQ号">{{ supplierInfo.serviceQqId || '--'}}</el-descriptions-item>
          <el-descriptions-item label="退货地址">{{ supplierInfo.returnAddress || '--'}}</el-descriptions-item>
        </el-descriptions>
      </div>
      <div style="width:800px;">
        <el-form v-if="formMode === 'edit'" ref="form" label-position="left" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="客服姓名" prop="serviceAgent">
            <el-input v-model="form.serviceAgent" :maxlength="30" placeholder="请输入客服姓名" />
          </el-form-item>
          <el-form-item label="客服电话" prop="servicePhone">
            <el-input v-model="form.servicePhone" :maxlength="20" placeholder="请输入客服电话" />
          </el-form-item>
          <el-form-item label="客服微信号" prop="serviceWechatId">
            <el-input v-model="form.serviceWechatId" :maxlength="50" placeholder="请输入客服微信号" />
          </el-form-item>
          <el-form-item label="客服QQ号" prop="serviceQqId">
            <el-input v-model="form.serviceQqId" :maxlength="50" placeholder="请输入客服QQ号" />
          </el-form-item>
          <el-form-item label="退货地址" prop="returnAddress">
            <el-input v-model="form.returnAddress" :maxlength="200" placeholder="请输入退货地址" />
          </el-form-item>
          <el-form-item>
            <el-button type="default" @click="cancelForm">取 消</el-button>
            <el-button type="primary" @click="submitForm" :loading="submitLoading">提 交</el-button>
          </el-form-item>
        </el-form>
      </div>

    </el-card>
  </div>
</template>

<script>
import * as api from '@/api/mall/supplier/supplier-profile'
export default {
  name: 'SupplierProfileBaseInfo',
  props: {
    supplierInfo: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      formMode: 'init',
      submitLoading: false,
      form: {
        serviceAgent: '',
        servicePhone: '',
        returnAddress: '',
        serviceWechatId: '',
        serviceQqId: ''
      },
      rules: {
        serviceAgent: [
          { required: true, trigger: 'blur', message: '请输入客服姓名' }
        ],
        servicePhone: [
          { required: true, trigger: 'blur', message: '请输入客服电话' }
        ],
      }
    }
  },
  methods: {
    edit() {
      this.formMode = 'edit'
      Object.keys(this.form).forEach(key => {
        this.form[key] = this.supplierInfo[key]
      })
    },
    cancelForm() {
      this.formMode = 'init'
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return
        }
        this.doSubmit()
      })
    },
    async doSubmit() {
      this.submitLoading = true
      try {
        let data = {...this.supplierInfo}
        Object.assign(data, this.form)
        await api.updateSupplierProfile(data)
        this.$modal.msgSuccess("修改成功")

        this.$emit('on-update')
        this.submitLoading = false
        this.formMode = 'init'
      } catch(e) {
        this.$modal.msgError("保存失败，请稍后重试")
        this.submitLoading = false
      }
    },
  }

}
</script>

<style>

</style>