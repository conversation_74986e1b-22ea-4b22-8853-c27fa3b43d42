<template>
  <div class="brand-select-con">
    <el-select :value="selectedVal" @input="onInput" @change="handleChange" :remote-method="loadOptions" :allowCreate="allowCreate"
      :placeholder="placeholder" :autoWidth="autoWidth" :autofocus="autofocus" :borderless="borderless"
      :clearable="clearable" :filterable="filterable" :multiple="multiple" :readonly="readonly" :remote="remote"
      :size="size" style="width: 100%;">
      <el-option v-for="item in options" :key="item.id" :disabled="item.status !== 0" :label="item.name"
        :value="item.id">
      </el-option>
    </el-select>
    <div class="brand-add-block" v-if="manualCreate"> 
      <span>搜不到品牌，可尝试手动</span><el-link type="primary" round size="small" @click="addbrand">添加</el-link>
    </div>
  </div>
</template>

<script>
import { getProductBrands, createBrandByName } from '@/api/mall/product/spu'
export default {
  name: 'ProductBrandSelect',
  props: {
    value: {
      type: [String, Number],
      default: () => null
    },
    extParams: {
      type: Object,
      default: () => { }
    },
    placeholder: {
      type: String,
      default: () => '请输入品牌名称搜索'
    },
    autoWidth: {
      type: Boolean,
      default: () => false
    },
    autofocus: {
      type: Boolean,
      default: () => false
    },
    borderless: {
      type: Boolean,
      default: () => false
    },
    clearable: {
      type: Boolean,
      default: () => true
    },
    filterable: {
      type: Boolean,
      default: () => true
    },
    multiple: {
      type: Boolean,
      default: () => false
    },
    readonly: {
      type: Boolean,
      default: () => false
    },
    remote: {
      type: Boolean,
      default: () => true
    },
    manualCreate: {
      type: Boolean,
      default: () => false
    },
    allowCreate: {
      type: Boolean,
      default: () => false
    },
    size: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      options: []
    }
  },
  computed: {
    selectedVal() {
      return this.value
    }
  },
  created() {
    this.loadOptions()
  },
  methods: {
    async loadOptions(keyword) {
      let params = Object.assign({
        pageSize: 100,
        status: 0
      }, this.extParams)
      if (keyword) {
        params.name = keyword
      }

      let res = await getProductBrands(params)
      res.data.forEach(element => {
        element.id = element.id
      });
      this.options = res.data || []
    },
    onInput(val) {
      this.$emit("input", val)
    },
    handleChange(val) {
      let opt = this.options.find(item => item.id === val)
      this.$emit("change", val, opt)
    },
    clickSelect() {
      if (this.options.length === 0) {
        this.loadOptions('')
      }
    },
    saveBrandManual(opt) {
      let hitObj = this.options.find(item => item.name === opt.name)
      if(hitObj) {
        this.onInput(hitObj.id)
      } else {
        let data = {
          name: opt.name
        }
        createBrandByName(data).then(res => {
          opt.id = res.data
          this.options.push(opt)
          this.onInput(opt.id)
        })  
      }
    },
    addbrand() {
      this.$prompt('请输入品牌名称', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^.{2,30}$/,
        inputErrorMessage: '品牌名称长度必须在2-30个字符内'
      }).then(({ value }) => {
        this.saveBrandManual({name: value})
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss">
.brand-select-con {
  .brand-add-block {
    display: inline-block;
    margin: 0 5px;
    span {
      margin: 0 5px 0 0;
    }
  }
}
</style>