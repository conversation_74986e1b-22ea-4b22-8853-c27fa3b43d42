<template>
  <div class="app-container">
    <el-tabs class="form" v-model="tabType" style="float:none;" @tab-click="handleClick" v-if="showTab">
      <el-tab-pane :label="`全部(${allTotal})`" name="all"></el-tab-pane>
      <el-tab-pane :label="`未确认(${statusData[1] || 0})`" name="1"></el-tab-pane>
      <el-tab-pane :label="`已确认(${statusData[2] || 0})`" name="2"></el-tab-pane>
      <el-tab-pane :label="`已发货(${statusData[3] || 0})`" name="3"></el-tab-pane>
      <el-tab-pane :label="`已送达(${statusData[4] || 0})`" name="4"></el-tab-pane>
      <el-tab-pane :label="`已签收(${statusData[5] || 0})`" name="5"></el-tab-pane>
      <el-tab-pane :label="`已完成(${statusData[8] || 0})`" name="8"></el-tab-pane>
      <el-tab-pane :label="`已取消(${statusData[9] || 0})`" name="9"></el-tab-pane>
      <el-tab-pane v-if="notSupplierUser()" :label="`售后中(${statusData[6] || 0})`" name="6"></el-tab-pane>
      <el-tab-pane v-if="notSupplierUser()" :label="`售后完成(${statusData[7] || 0})`" name="7"></el-tab-pane>
    </el-tabs>

    <el-form :model="queryParams" ref="queryForm" @keyup.enter.native="handleQuery" class="order-form" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单">
        <el-input v-model="queryParams.orderCode" clearable placeholder="请输入订单号">
        </el-input>
      </el-form-item>
      <el-form-item label="商品">
        <el-input v-model="queryParams.skuCode" clearable placeholder="请输入商品SKU">
        </el-input>
      </el-form-item>
      <el-form-item label="用户">
        <el-input v-model="queryParams.searchValue" clearable style="width: 280px">
          <el-select v-model="queryParams.searchType" slot="prepend" style="width: 110px">
            <el-option v-for="dict in searchTypes" :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-input>
      </el-form-item>
      <el-form-item v-if="notSupplierUser()" v-hasPermi="['mall:supplier']" label="供应商" prop="supplierId">
        <supplier-select v-model="queryParams.supplierId" placeholder="请选择供应商" @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="审批状态" prop="platform" label-width="70px" v-if="isBpm">
        <el-select v-model="queryParams.auditStatusList" multiple  collapse-tags clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MALL_ORDER_AUDIT_STATUS)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="支付方式" prop="paymentMethod" label-width="70px" v-if="payMethodOpts.length">
        <el-select v-model="queryParams.paymentMethod" clearable size="small">
          <el-option v-for="dict in payMethodOpts"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item v-if="notSupplierUser()" label="终端" prop="platform">
        <el-select v-model="queryParams.platform" placeholder="请选择终端" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MALL_ORDER_PLATFORM)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="日期" prop="submitTime">
        <el-date-picker v-model="queryParams.submitTime" value-format="yyyy-MM-dd HH:mm:ss" type="daterange" :picker-options="pickerOptions"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']" style="width: 240px;"/>
      </el-form-item>
      <el-form-item label="商品标签" prop="skuTags" v-if="showTagSelect">
        <tag-select size="small" v-model="queryParams.skuTags" placeholder="请选择标签" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary"  size="small" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        <el-button v-hasPermi="['trade:order:export']" icon="el-icon-files"  size="small" :loading="exportLoading" @click="exportOrder">订单导出</el-button>
      </el-form-item>
    </el-form>

    <el-table
      ref="table"
      class="order-table"
      v-loading="loading" 
      :data="list"
      show-summary
      :summary-method="getSummaries"
      row-key="no"
      max-height="560"
      default-expand-all
      :row-class-name="cellClass"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column label="商品" width="360">
        <template slot-scope="{ row }">
          <span v-if="row.children != undefined" class="orderCodeNum">
            订单编号: {{ row.no || '--' }} <OrderTagCom v-if="showOrderTag" :orderItems="row.items"></OrderTagCom>
          </span>
          <div v-else class="goodsCard">
            <div class="goodsCard-item" v-for="item in row.productList" :key="item.skuId">
              <div class="goodsCard-item-img">
                <img :src="item.picUrl" alt="" height="30" />
              </div>
              <div class="goodsCard-item-info">
                <div class="goodsCard-item-info-name text-ellipsis" :title="item.skuName">{{ item.skuName || '--' }}</div>
                <div class="goodsCard-item-info-code">{{ item.skuId || '--' }} <span v-if="item.skuInnerId && row.supplierType !== 20"> / {{ item.skuInnerId }}</span></div>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="单价" width="130px">
        <template slot-scope="{ row }">
          <div v-if="row.children != undefined">下单时间:</div>
          <div
            v-else
            class="unit-price"
            v-for="item in row.productList"
            :key="item.skuId"
          >
            <span class="unit-price-number">{{ formatMoney(item.skuPrice) }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="数量" width="180px">
        <template slot-scope="{ row }">
          <div v-if="row.children != undefined" :class="calDiffClass(row)">
            {{ parseTime(row.submitTime) }}
          </div>
          <div v-else class="unit-price" v-for="item in row.productList" :key="item.skuId">
            x{{ item.count }} 
            <div style="display:inline-block;margin-left:5px;" v-if="item.afterSaleStatus">
              <dict-tag :type="DICT_TYPE.TRADE_ORDER_ITEM_AFTER_SALE_STATUS" :value="item.afterSaleStatus" />
            </div>
            <OrderTagCom v-if="showOrderTag" :tags="item.skuTags"></OrderTagCom>
            <OrderAcceptInfo :orderItem="item" :orderStatus="row.status"></OrderAcceptInfo>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="下单人" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.children != undefined">
            <span v-if="row.cancelTime">取消时间:</span>
            <span v-else-if="row.finishTime">完成时间:</span>
            <span v-else-if="row.receiveTime">签收时间:</span>
            <span v-else-if="row.deliveryTime">发货时间:</span>
          </div>
          <div v-if="row.children == undefined">
            <div>{{ row.userName }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="收货人" align="center" width="170px">
        <template slot-scope="{ row }">
          <div v-if="row.children != undefined">
            <span v-if="row.cancelTime">{{ parseTime(row.cancelTime) }}</span>
            <span v-else-if="row.finishTime">{{ parseTime(row.finishTime) }}</span>
            <span v-else-if="row.receiveTime">{{ parseTime(row.receiveTime) }}</span>
            <span v-else-if="row.deliveryTime" :class="calDiffClass(row, 2)">{{ parseTime(row.deliveryTime) }}</span>
          </div>
          <div v-if="row.children == undefined">
            <div>{{ row.receiverName }}</div>
            <div>{{ row.receiverMobile || '--' }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="下单金额" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.children != undefined">
            <dict-tag :type="DICT_TYPE.MALL_ORDER_PLATFORM" :value="row.platform" />
          </div>
          <div v-if="row.children == undefined">
            <div>{{ formatMoney(row.orderPrice) }}</div>
            <dict-tag :type="DICT_TYPE.MALL_PAYMENT_METHOD" :value="row.paymentMethod || 1" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.children == undefined">
            <div class="status-box-">
              <div class="status-ball" :style="{ backgroundColor: getStatusColor(row.status) }"></div>
              <OrderStatusCom :order="row"></OrderStatusCom>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="供应商" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.children != undefined">
            <div>{{ row.supplierName }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" v-if="false">
        <template slot-scope="{ row }">
          <div v-if="row.children == undefined">
            <div>{{ parseTime(row.updateTime) }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button v-if="!scope.row.children" size="medium" type="text" @click="goToDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination v-show="total > 0" :page-sizes="[20, 50, 100, 200]" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
      @pagination="getList"/>

    <ExportAlert ref="exportAlert"></ExportAlert>
  </div>
</template>

<script>
import OrderTagCom from '@/views/mall/trade/order/components/orderTag' 
import OrderStatusCom from '@/views/mall/trade/order/components/orderStatus' 
import { getOrderList, getOrderStatus, exportOrderList } from "@/api/mall/trade/order.js"
import { formatDate } from "@/utils/dateUtils";
import SupplierSelect from "@/views/mall/product/spu/components/supplier-select";
import ExportAlert from '@/components/AsyncTaskAlert/export'
import TagSelect from "@/views/mall/product/seo/components/tag-select"
import { configMixins } from '@/views/mall/config/components/configMixin.js'
import OrderAcceptInfo from '@/views/mall/trade/order/components/acceptInfo'
export default {
  name: "TradeOrderList",
  mixins: [ configMixins ],
  props: {
    showTab: {
      type: Boolean,
      default() {
        return true
      }
    },
    paramTags: {
      type: Array,
      default() {
        return []
      }
    },
    showOrderTag: {
      type: Boolean,
      default() {
        return false
      }
    },
    showTagSelect: {
      type: Boolean,
      default() {
        return false
      }
    }
  },
  components: { SupplierSelect, ExportAlert, OrderTagCom, TagSelect, OrderStatusCom, OrderAcceptInfo },
  data () {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      tabType: 'all',
      // 总条数
      allTotal: 0,
      total: 0,
      // 交易售后列表
      list: [],
      queryParams: {
        pageNo: 1,
        pageSize: 20,
        status: 'all',
        supplierId: null,
        platform: null,
        submitTime: [],
        skuTags: [],
        orderCode: null,
        skuCode: null,
        paymentMethod: null,
        searchType: 'userName',
        searchValue: null,
        auditStatusList: [],
      },
      searchTypes: [
        { label: '下单人', value: 'userName' },
        { label: '收货人', value: 'receiverName' },
        { label: '下单人Id', value: 'userId' },
        { label: '下单人手机', value: 'userMobile' },
        { label: '收货人手机', value: 'receiverMobile' }
      ],
      statusData: {},
      pickerOptions: {
        shortcuts: [{
          text: '本周',
          onClick(picker) {
            // This week (Monday to Sunday)
            const now = new Date()
            const day = now.getDay() || 7 // 周日返回0，转换为7
            const monday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - day + 1)
            const sunday = new Date(now.getFullYear(), now.getMonth(), now.getDate() + (7 - day))
            picker.$emit('pick', [monday, sunday])
          }
        }, {
          text: '本月',
          onClick(picker) {
            // Current month
            const start = new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            const end = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '本年',
          onClick(picker) {
            // Current year
            const start = new Date(new Date().getFullYear(), 0, 1)
            const end = new Date(new Date().getFullYear(), 11, 31)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '近7天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近30天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近90天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近180天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
            picker.$emit('pick', [start, end]);
          }
        }]
      }
    }
  },
  created() {
    let preDate = new Date()
    preDate.setTime(new Date().getTime() - (1000 * 60 * 60 * 24 * 30))
    this.queryParams.submitTime.push(formatDate(preDate, 'yyyy-MM-dd 00:00:00'))
    this.queryParams.submitTime.push(formatDate(new Date(), 'yyyy-MM-dd 23:59:59'))

    this.getList();
  },
  methods: {
    getStatusColor(status) {
      if ([1, 2, 3, 6].includes(status)) {
        return '#FF9500'
      } else if (status === 9) {
        return '#E5E6EB'
      }
      return '#00B42A'
    },
    handleClick(val) {
      this.queryParams = {
        pageNo: 1,
        pageSize: 20,
        status: this.tabType,
        submitTime: [],
      }
      this.getList();
    },
    getParams() {
      const params = {
        pageNo: this.queryParams.pageNo,
        pageSize: this.queryParams.pageSize,
        sortType: 1
      }
      if(this.queryParams.supplierId) {
        params.supplierId = this.queryParams.supplierId
      }
      if(this.queryParams.paymentMethod) {
        params.paymentMethod = this.queryParams.paymentMethod
      }
      if(this.queryParams.platform) {
        params.platform = this.queryParams.platform
      }
      if (this.queryParams.status !== 'all') {
        params.status = this.queryParams.status
      }
      if (this.queryParams.submitTime && this.queryParams.submitTime.length > 0) {
        params.submitTime = this.queryParams.submitTime.join(',')
      }
      if (this.queryParams.orderCode) {
        params.orderCode = this.queryParams.orderCode.trim()
      }
      if (this.queryParams.skuCode) {
        params.skuCode = this.queryParams.skuCode.trim()
      }
      if(this.paramTags && this.paramTags.length) {
        params.skuTags = this.paramTags
      }
      if(this.queryParams.skuTags && this.queryParams.skuTags.length) {
        params.skuTags = this.queryParams.skuTags
      }
      if(this.queryParams.auditStatusList && this.queryParams.auditStatusList.length > 0) {
        params.auditStatusList = this.queryParams.auditStatusList.map(item => {
          return Number(item)
        })
      }

      if (this.queryParams.searchValue) {
        if (this.queryParams.searchType == 'userId') {
          if (Number.isNaN(Number(this.queryParams.searchValue.trim()))) {
            this.$message.error('用户ID必须为数字')
            return null
          }
          else {
            params.userId = this.queryParams.searchValue.trim()
          }
        }

        if (this.queryParams.searchType == 'userName') {
          params.userName = this.queryParams.searchValue.trim()
        }

        if (this.queryParams.searchType == 'receiverName') {
          params.receiverName = this.queryParams.searchValue.trim()
        }

        if (this.queryParams.searchType == 'userMobile') {
          params.userMobile = this.queryParams.searchValue.trim()
        }

        if (this.queryParams.searchType == 'receiverMobile') {
          params.receiverMobile = this.queryParams.searchValue.trim()
        }
      }

      return params
    },
    /** 查询列表 */
    async getList() {
      this.loading = true;
      const params = this.getParams();
      if (params == null){
        this.loading = false;
        return
      }
      const res = await getOrderList(params);
      if (res.code === 0 && res.data) {
        const list = res.data.list;
        res.data.list.forEach((item, index) => {
          item.children = [
            {
              id: item.id,
              no: item.no + index,
              supplierType: item.supplierType,
              productList: item.items || [],
              productCount: item.productCount,
              userName: item.userName,
              receiverName: item.receiverName,
              productPrice: item.productPrice,
              orderPrice: item.orderPrice,
              status: item.status,
              submitTime: item.submitTime,
              receiveTime: item.receiveTime,
              deliveryTime: item.deliveryTime,
              finishTime: item.finishTime,
              cancelTime: item.cancelTime,
              updateTime: item.updateTime,
              userDeleted: item.userDeleted,
              platform: item.platform,
              receiverMobile: item.receiverMobile,
              skuTags: item.skuTags,
              paymentMethod: item.paymentMethod,
              auditStatus: item.auditStatus
            }
          ]
        })
        this.list = list
        if (this.queryParams.status === 'all') {
          this.allTotal = Number(res.data.total)
        }
        this.total = Number(res.data.total);
        this.$nextTick(() => {
          this.$refs.table.doLayout()
        })
      } else {
        this.list = []
        this.total = 0
      }
      this.loading = false;
      this.getOrderStatus()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 20,
        orderCode: null,
        skuCode: null,
        status: '',
        submitTime: [],
        searchType: 'userName',
        searchValue: null,
      }
      this.handleQuery();
    },
    goToDetail(row) {
      this.$router.push({ name: this.showOrderTag ? 'TradeOrderDetail' : 'TradeOrderSDetail', query: { id: row.id } })
    },
    cellClass({ row }) {
      if (!row.hasOwnProperty('children')) {
        return 'disableheadselection'
      }
      return 'orderCode'
    },
    async getOrderStatus() {
      const res = await getOrderStatus()
      if (res.code === 0 && res.data) {
        res.data.forEach(item => {
          this.$set(this.statusData, item.status, item.count)
        })
      }
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      sums[0] = '合计'
      let s1 = 0
      this.list.forEach(item => {
        s1 += item.orderPrice
      })
      sums[5] = this.formatMoney(s1)

      return sums;
    },
    // 订单导出
    exportOrder() {
      const params = this.getParams();
      this.$refs.exportAlert.init(exportOrderList, params, '订单导出')
    },
  }
}
</script>

<style lang="scss" scoped>
.order-table {
  :deep(.el-table__expand-icon) {
    display: none;
  }
  :deep(.disableheadselection) {
    color: #8c8c8c;
    > td:first-child {
      > .cell {
        > .el-checkbox {
          display: none;
        }
      }
    }
  }
  :deep(.orderCode) {
    color: #333333;
    background-color: #f2f4f7;
  }
  .orderCodeNum {
    font-size: 12px;
    font-weight: bold;
    color: #333333;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .goodsCard {
    display: block;
    .goodsCard-item {
      display: flex;
      width: 100%;
      + .goodsCard-item {
        margin-top: 20px;
      }
      .goodsCard-item-img {
        position: relative;
      }
      .goodsCard-item-info {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-left: 8px;
        font-size: 12px;
        line-height: 20px;
        max-width: 300px;
        .goodsCard-item-info-name {
          width: 300px;
          color: #1d2129;
        }
        .goodsCard-item-info-code {
          color: #86909c;
        }
      }
    }
  }
  .unit-price {
    display: flex;
    align-items: center;
    height: 38px;
    + .unit-price {
      margin-top: 20px;
    }
    .unit-price-number {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .status-box {
    display: flex;
    align-items: center;
    justify-content: center;
    .status-ball {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      margin-right: 10px;
    }
  }
}
</style>
