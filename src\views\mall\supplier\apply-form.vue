<template>
  <div class="app-container" v-loading="loading">
    <div class="flex-center apply-tip" v-if="applyFormInfo && applyFormInfo.approveStatus === 2"> 
      <el-alert title="入驻审核通过" type="success"
        description="请您前往基础配置完成初始化设置，并检查您当前的运营状态是否为已开启，如有问题请联系平台运营"
        show-icon>
      </el-alert>
    </div>
    
    <div v-if="applyFormId && operMode === 'view'">
      <ApplyFormDetail ref="applyFormDetail" :id="applyFormId" role="user" @on-edit="applyFormEdit"></ApplyFormDetail>
    </div>
    <div v-if="['init','edit'].includes(operMode) && inited"> 
      <el-empty v-if="!applyFormId && operMode === 'init'">
        <el-button icon="el-icon-add" type="primary" round @click="beginConfig">点击发起申请</el-button>
      </el-empty>
      <ApplyFormAdd v-if="operMode === 'edit'" ref="applyFormAdd" @on-update="applyFormUpdate" @on-cancel="applyFormCancel"></ApplyFormAdd>
    </div>
  </div>
</template>

<script>
import * as api from '@/api/mall/supplier/supplier-form'
import ApplyFormDetail from '@/views/mall/supplier/components/apply-detail'
import ApplyFormAdd from '@/views/mall/supplier/components/apply-add'
export default {
  name: 'SupplierApplyForm',
  components: { ApplyFormDetail, ApplyFormAdd },
  data() {
    return {
      loading: false,
      inited: false,
      operMode: 'init',
      applyFormInfo: null
    }
  },
  computed: {
    applyFormId() {
      return this.applyFormInfo ? this.applyFormInfo.id : null
    }
  },
  mounted() {
    this.loadApplyFormInfo()
  },
  methods: {
    loadApplyFormInfo() {
      this.loading = true
      api.getUserApplyFormPage().then(res => {
        this.applyFormInfo = null
        if(res.data && res.data.total) {
          this.applyFormInfo = res.data.list[0]
          this.operMode = 'view'
        }
      }).finally(() => {
        this.inited = true
        this.loading = false
      })  
    },
    beginConfig() {
      this.operMode = 'edit'
    },
    applyFormEdit() {
      this.operMode = 'edit'
      this.$nextTick(() => {
        this.$refs.applyFormAdd.edit(this.applyFormId)
      })
    },
    applyFormCancel() {
      if(this.applyFormId) {
        this.operMode = 'view'
      } else {
        this.operMode = 'init'
      }
    },
    applyFormUpdate() {
      this.loadApplyFormInfo()
    }
  }

}
</script>

<style lang="scss">
.apply-tip {
  width: 100%;
  padding: 0 20px;
  .el-alert__description {
    font-size: 13px;
  }
}

</style>