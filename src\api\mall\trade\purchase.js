import request from '@/utils/request'

// 获得采购单分页
export function getPurchasePage(query) {
  return request({
    url: '/trade/purchase/page',
    method: 'get',
    params: query
  })
}

// 获得采购单分页
export function getPurchasePageV2(query) {
  return request({
    url: '/trade/purchase/page/v2',
    method: 'get',
    params: query
  })
}

// 获得采购单
export function getPurchaseDetail(id) {
  return request({
    url: '/trade/purchase/detail?id=' + id,
    method: 'get'
  })
}

// 取消采购单
export function cancelPurchase(id) {
  return request({
    url: '/trade/purchase/cancel?id=' + id,
    method: 'put'
  })
}

// 查询审批流状态
export function pullBpmStatus(query) {
  return request({
    url: '/trade/purchase/pull-bpm-status',
    method: 'post',
    params: query
  })
}
