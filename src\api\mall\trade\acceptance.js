import request from '@/utils/request'

// 获得报销材料分页列表
export function getOrderAcceptancePage(params) {
  return request({
    url: '/trade/order-acceptance/page',
    method: 'get',
    params
  })
}

// 获得报销材料
export function getOrderAcceptanceByItem(params) {
  return request({
    url: '/trade/order-acceptance/get-by-item',
    method: 'get',
    params
  })
}

// 获得报销材料列表
export function getOrderAcceptanceListByOrder(params) {
  return request({
    url: '/trade/order-acceptance/list-by-order',
    method: 'get',
    params
  })
}

// 获得报销材料详情
export function getOrderAcceptanceInfo(params) {
  return request({
    url: '/trade/order-acceptance/get',
    method: 'get',
    params
  })
}

// 保存销材料详情
export function saveAcceptanceInfoByItem(params) {
  return request({
    url: '/trade/order-acceptance/save-by-item',
    method: 'post',
    data: params
  })
}
