import request from '@/utils/request'

export function pageQueryStatsOrderItem(params) {
	return request({
		url: '/mall/stats/page/order-item' ,
		method: 'get',
		params
	})
}

export function pageQueryStatsAfterSale(params) {
	return request({
		url: '/mall/stats/page/after-sale' ,
		method: 'get',
		params
	})
}

// 导出
export function exportStatsOrderItem(params) {
  return request({
    url: '/mall/stats/export/order-item',
    method: 'get',
    params
  })
}

// 导出
export function exportStatsAfterSale(params) {
  return request({
    url: '/mall/stats/export/after-sale',
    method: 'get',
    params
  })
}

export function getStatsSaleTotal(params) {
	return request({
		url: '/mall/stats/get/sale-total' ,
		method: 'get',
		params
	})
}

export function pageQuerySaleOnDate(params) {
	return request({
		url: '/mall/stats/page/sale-date' ,
		method: 'get',
		params
	})
}

export function pageQuerySaleOnYearMonth(params) {
	return request({
		url: '/mall/stats/page/sale-year-month' ,
		method: 'get',
		params
	})
}

export function pageQueryRankSku(params) {
	return request({
		url: '/mall/stats/page/rank-sku' ,
		method: 'get',
		params
	})
}

export function exportStatsSaleByType(params) {
	return request({
		url: '/mall/stats/export/sale-date/' + params.expType ,
		method: 'get',
		params
	})
}