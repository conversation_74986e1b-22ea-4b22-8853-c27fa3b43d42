export const productFieldList = [
  { label: '列表供应商名称', value: 'list-supplier' },
  { label: '商品市场价', value: 'market-price' }
]

export const extConfigDics = [
  { label: '客服二维码',  key: 'custQRImg', comType: 'input', maxLength: 150, placeholder: '客服二维码', memo: '' },
  { label: '客服链接',  key: 'custLink', comType: 'input', maxLength: 150, placeholder: '客服链接', memo: '可跳转到客服聊天框' },
  { label: '客服电话',  key: 'custPhone', comType: 'input', maxLength: 100, placeholder: '请输入平台客服配置，格式为 k=v&k2=v2 小王=13599996666&小李=13966668888', memo: '' },
  { label: '结算单模版',  key: 'billSettleTemplate', comType: 'input', maxLength: 150, placeholder: '请输入结算单模版名称', memo: '' },
  { label: '申购单导出开关',  key: 'expApply', defaultVal: false, comType: 'switch', placeholder: '', memo: '打开后，在订单详情可显示申购单导出按钮' },
  { label: '签收单导出开关',  key: 'expDelivery', defaultVal: false, comType: 'switch', placeholder: '', memo: '打开后，在订单详情可显示签收单导出按钮' },
  { label: '下载发票开关',  key: 'downInvoice', defaultVal: false, comType: 'switch', placeholder: '', memo: '打开后，在订单详情可下载发票' },
  { label: '报销材料开关',  key: 'acceptSwitch', defaultVal: false, comType: 'switch', placeholder: '', memo: '打开后，在订单详情中商品可显示上传报销材料按钮' },
  { label: '协同采购开关',  key: 'teamOrderSwitch', defaultVal: false, comType: 'switch', placeholder: '', memo: '打开后，个人中心以及下单页会显示协同采购入口' },
  { label: '供应商入驻开关',  key: 'supRegSwitch', defaultVal: false, comType: 'switch', placeholder: '供应商入驻开关', memo: '打开后，在商城首页会显示供应商入驻的入口' },
]