import request from '@/utils/request'

// 重试处理API调用
export function retryHandle(data) {
  return request({
    url: '/mall/api-fail/retry',
    method: 'post',
    data: data
  })
}

// 删除API调用失败
export function deleteApiFail(id) {
  return request({
    url: '/mall/api-fail/delete?id=' + id,
    method: 'delete'
  })
}

// 获得API调用失败分页
export function getApiFailPage(query) {
  return request({
    url: '/mall/api-fail/page',
    method: 'get',
    params: query
  })
}
