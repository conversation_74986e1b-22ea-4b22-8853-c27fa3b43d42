<template>
  <div class="app-container">
    <el-empty v-if="!configInfo.id && formMode === 'init'">
      <el-button type="text" icon="el-icon-plus" @click="beginConfig">开始配置</el-button>
    </el-empty>
    <div v-if="configInfo.id && formMode === 'init'">
      <el-descriptions title="配置信息" :column="1" :border="true">
        <template slot="extra">
          <el-button type="primary" size="small" @click="editConfig">修改</el-button>
        </template>
        <el-descriptions-item label="APP_KEY">{{ configInfo.appKey || '--' }}</el-descriptions-item>
        <el-descriptions-item label="APP_SECRET">
          {{ configInfo.appSecret || '--' }}
          <el-button size="small" plain type="primary" style="margin-left:10px" @click="refreshSecret">刷新</el-button>
        </el-descriptions-item>
        <el-descriptions-item label="状态"><dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="configInfo.status" /></el-descriptions-item>
        <el-descriptions-item label="IP白名单">{{ configInfo.ipWhiteList || '--' }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <el-form v-if="formMode === 'edit'" ref="configForm" label-position="top" :model="configForm" :rules="configRules"
      label-width="80px" style="width:600px">
      <el-form-item label="APP_KEY" prop="appKey">
        <el-input v-model="configForm.appKey" maxlength="50" placeholder="请输入APP_KEY" disabled/>
      </el-form-item>
      <el-form-item label="APP_SECRET" prop="appSecret">
        <el-input v-model="configForm.appSecret" maxlength="100" placeholder="请输入APP_SECRET" readonly="true" disabled/>
      </el-form-item>
      <el-form-item label="IP白名单(逗号分隔)" prop="ipWhiteList">
        <el-input  v-model="configForm.ipWhiteList" maxlength="200" :rows="4" type="textarea" placeholder="请输入IP白名单" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="configForm.status">
          <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                    :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button type="default" @click="cancelForm">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">提 交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import * as api from "@/api/mall/common/openApp";
export default {
  name: "MallConfigSupplierOpenApp",
  components: {},
  props: {
    id: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      configInfo: {},
      formMode: 'init',
      submitLoading: false,
      configForm: {
        id: undefined,
        appKey: '',
        appSecret: '',
        ipWhiteList: '',
        status: 0
      },
      configRules: {
        appKey: [
          { required: true, trigger: 'blur', message: '请输入appKey' }
        ],
        appSecret: [
          { required: true, trigger: 'blur', message: '请输入appSecret' }
        ]
      },
    }
  },
  created() {
    this.loadConfig()
  },
  methods: {
    beginConfig() {
      this.formMode = 'edit'
    },
    async loadConfig() {
      if(!this.id) {
        return
      }
      let res = await api.getOpenApp(this.id);
      this.configInfo = res.data || {}
    },
    editConfig() {
      this.formMode = 'edit'
      Object.assign(this.configForm, this.configInfo)
    },
    cancelForm() {
      this.formMode = 'init'
    },
    refreshSecret() {
      const id = this.configInfo.id;
      if(!id) {
        return
      }
      this.$modal.confirm('密钥刷新后需要通知到调用方，是否确认刷新密钥?').then(function() {
        return api.refreshOpenAppSecret({id: id});
      }).then(() => {
        this.$modal.msgSuccess("刷新成功");
        this.loadConfig()
      }).catch(() => {});
    },
    async doSubmit() {
      this.submitLoading = true
      this.configForm.supplierId = this.id
      try {
        if (this.configForm.id !== undefined) {
          await api.updateOpenApp(this.configForm)
          this.$modal.msgSuccess("修改成功");
        } 
        this.submitLoading = false
        this.formMode = 'init'
        this.loadConfig()
      } catch(e) {
        this.$modal.msgError("保存失败，请稍后重试")
        this.submitLoading = false
      }
    },
    submitForm() {
      this.$refs["configForm"].validate(valid => {
        if (!valid) {
          return;
        }
        this.doSubmit()
      });
    }
  }
}
</script>

<style lang="scss" scoped></style>