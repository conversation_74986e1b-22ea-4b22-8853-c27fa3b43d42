import request from '@/utils/request'

// 创建导出任务
export function createExportTask(data) {
  return request({
    url: '/mall/export-task/create',
    method: 'post',
    data: data
  })
}

// 更新导出任务
export function updateExportTask(data) {
  return request({
    url: '/mall/export-task/update',
    method: 'put',
    data: data
  })
}

// 删除导出任务
export function deleteExportTask(id) {
  return request({
    url: '/mall/export-task/delete?id=' + id,
    method: 'delete'
  })
}

// 获得导出任务
export function getExportTask(id) {
  return request({
    url: '/mall/export-task/get?id=' + id,
    method: 'get'
  })
}

// 获得导出任务分页
export function getExportTaskPage(query) {
  return request({
    url: '/mall/export-task/page',
    method: 'get',
    params: query
  })
}

// 下载导出文件
export function download(query) {
  return request({
    url: '/mall/export-task/download',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 清理任务锁
export function termate(query) {
  return request({
    url: '/mall/export-task/termate',
    method: 'get',
    params: query
  })
}